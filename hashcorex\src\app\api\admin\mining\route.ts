import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { verifyAdminAuth } from '@/lib/auth';

export async function GET(request: NextRequest) {
  try {
    // Verify admin authentication
    const adminUser = await verifyAdminAuth(request);
    if (!adminUser) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const search = searchParams.get('search') || '';
    const status = searchParams.get('status') || 'ALL';

    // Build where clause for filtering
    const whereClause: any = {};
    
    if (status !== 'ALL') {
      whereClause.status = status;
    }

    if (search) {
      whereClause.OR = [
        {
          user: {
            firstName: {
              contains: search,
              mode: 'insensitive',
            },
          },
        },
        {
          user: {
            lastName: {
              contains: search,
              mode: 'insensitive',
            },
          },
        },
        {
          user: {
            email: {
              contains: search,
              mode: 'insensitive',
            },
          },
        },
        {
          user: {
            referralId: {
              contains: search,
              mode: 'insensitive',
            },
          },
        },
      ];
    }

    // Fetch mining units with user data
    const miningUnits = await prisma.miningUnit.findMany({
      where: whereClause,
      include: {
        user: {
          select: {
            firstName: true,
            lastName: true,
            email: true,
            referralId: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    // Calculate progress percentage for each unit
    const unitsWithProgress = miningUnits.map(unit => {
      const maxEarnings = unit.investmentAmount * 5; // 5x return limit
      const progressPercentage = (unit.totalEarned / maxEarnings) * 100;
      
      return {
        id: unit.id,
        userId: unit.userId,
        user: unit.user,
        thsAmount: unit.thsAmount,
        investmentAmount: unit.investmentAmount,
        totalEarned: unit.totalEarned,
        dailyROI: unit.dailyROI,
        status: unit.status,
        purchaseDate: unit.createdAt.toISOString(),
        expiryDate: unit.expiryDate.toISOString(),
        progressPercentage: Math.min(progressPercentage, 100),
      };
    });

    // Calculate statistics
    const activeUnits = miningUnits.filter(unit => unit.status === 'ACTIVE');
    const totalActiveTHS = activeUnits.reduce((sum, unit) => sum + unit.thsAmount, 0);
    const totalInvestment = miningUnits.reduce((sum, unit) => sum + unit.investmentAmount, 0);
    const totalEarningsDistributed = miningUnits.reduce((sum, unit) => sum + unit.totalEarned, 0);
    const averageDailyROI = activeUnits.length > 0 
      ? activeUnits.reduce((sum, unit) => sum + unit.dailyROI, 0) / activeUnits.length 
      : 0;

    // Get unique active users count
    const activeUserIds = new Set(activeUnits.map(unit => unit.userId));
    const activeUsers = activeUserIds.size;

    const stats = {
      totalActiveUnits: activeUnits.length,
      totalActiveTHS,
      totalInvestment,
      totalEarningsDistributed,
      averageDailyROI,
      activeUsers,
    };

    return NextResponse.json({
      success: true,
      units: unitsWithProgress,
      stats,
    });

  } catch (error: any) {
    console.error('Mining management API error:', error);
    
    return NextResponse.json(
      { success: false, error: 'Failed to fetch mining data' },
      { status: 500 }
    );
  }
}
