"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/SupportCenter.tsx":
/*!****************************************************!*\
  !*** ./src/components/dashboard/SupportCenter.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SupportCenter: () => (/* binding */ SupportCenter)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BookOpen,CheckCircle,Clock,HelpCircle,MessageCircle,Plus,Send,Settings,Shield,TrendingUp,Users,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BookOpen,CheckCircle,Clock,HelpCircle,MessageCircle,Plus,Send,Settings,Shield,TrendingUp,Users,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wallet.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BookOpen,CheckCircle,Clock,HelpCircle,MessageCircle,Plus,Send,Settings,Shield,TrendingUp,Users,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BookOpen,CheckCircle,Clock,HelpCircle,MessageCircle,Plus,Send,Settings,Shield,TrendingUp,Users,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BookOpen,CheckCircle,Clock,HelpCircle,MessageCircle,Plus,Send,Settings,Shield,TrendingUp,Users,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BookOpen,CheckCircle,Clock,HelpCircle,MessageCircle,Plus,Send,Settings,Shield,TrendingUp,Users,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BookOpen,CheckCircle,Clock,HelpCircle,MessageCircle,Plus,Send,Settings,Shield,TrendingUp,Users,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BookOpen,CheckCircle,Clock,HelpCircle,MessageCircle,Plus,Send,Settings,Shield,TrendingUp,Users,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BookOpen,CheckCircle,Clock,HelpCircle,MessageCircle,Plus,Send,Settings,Shield,TrendingUp,Users,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BookOpen,CheckCircle,Clock,HelpCircle,MessageCircle,Plus,Send,Settings,Shield,TrendingUp,Users,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BookOpen,CheckCircle,Clock,HelpCircle,MessageCircle,Plus,Send,Settings,Shield,TrendingUp,Users,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BookOpen,CheckCircle,Clock,HelpCircle,MessageCircle,Plus,Send,Settings,Shield,TrendingUp,Users,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-question-mark.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BookOpen,CheckCircle,Clock,HelpCircle,MessageCircle,Plus,Send,Settings,Shield,TrendingUp,Users,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ SupportCenter auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n// FAQ Data\nconst faqData = [\n    {\n        id: '1',\n        category: 'Getting Started',\n        question: 'How do I start mining with HashCoreX?',\n        answer: \"To start mining with HashCoreX, follow these simple steps:\\n\\n1. Complete KYC Verification: First, complete your KYC verification in the Profile section. This is required for all mining activities.\\n\\n2. Deposit Funds: Go to your Wallet and deposit USDT (TRC20) to fund your account. The minimum deposit amount is configured by the admin.\\n\\n3. Purchase Mining Units: Navigate to the Mining section and purchase mining units. Choose your desired TH/s amount and investment level.\\n\\n4. Start Earning: Your mining units will start generating daily returns automatically. Earnings are calculated based on your TH/s amount and current market conditions.\\n\\n5. Track Progress: Monitor your earnings in the Earnings section and watch your mining units progress toward their 5x investment return limit.\\n\\nRemember: Mining units expire after 24 months or when they reach 5x their investment amount, whichever comes first.\",\n        icon: _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        tags: [\n            'mining',\n            'getting started',\n            'kyc',\n            'deposit'\n        ]\n    },\n    {\n        id: '2',\n        category: 'Mining Units',\n        question: 'How are daily returns calculated?',\n        answer: \"Daily returns are calculated using a dynamic system based on your mining unit's TH/s amount:\\n\\nDynamic ROI System:\\n• Different TH/s ranges have different return rates\\n• Returns are calculated as: (Investment Amount \\xd7 Daily ROI%) \\xf7 100\\n• ROI percentages are determined by admin-configured ranges\\n\\nExample Ranges:\\n• 0-10 TH/s: 0.3% - 0.5% daily\\n• 10-50 TH/s: 0.4% - 0.6% daily\\n• 50+ TH/s: 0.5% - 0.7% daily\\n\\nImportant Notes:\\n• Returns are credited daily but paid out weekly (Sundays at 00:00 AM GMT+5:30)\\n• Mining units expire when they reach 5x their investment amount\\n• FIFO system: Oldest units receive earnings first\\n• All earnings are subject to market conditions and admin configuration\",\n        icon: _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        tags: [\n            'mining',\n            'returns',\n            'calculation',\n            'roi'\n        ]\n    },\n    {\n        id: '3',\n        category: 'Wallet & Payments',\n        question: 'How do deposits and withdrawals work?',\n        answer: \"Deposits:\\n• Only USDT (TRC20) deposits are accepted\\n• Minimum deposit amount is set by admin (typically $50)\\n• Deposits are automatically verified on the Tron blockchain\\n• Funds are available immediately after confirmation\\n\\nWithdrawals:\\n• Minimum withdrawal: $50\\n• Fixed fee: $3 + 1% of withdrawal amount\\n• Processing time: Up to 3 business days\\n• Only to verified TRC20 addresses\\n\\nWallet Balance:\\n• Available Balance: Funds ready for use or withdrawal\\n• Pending Balance: Earnings waiting for weekly distribution\\n• Total Earnings: Cumulative earnings from all sources\\n\\nImportant:\\n• Complete KYC verification before making withdrawals\\n• Ensure your TRC20 address is correct before submitting\\n• Withdrawal fees are deducted from your balance\",\n        icon: _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        tags: [\n            'wallet',\n            'deposit',\n            'withdrawal',\n            'usdt',\n            'trc20'\n        ]\n    },\n    {\n        id: '4',\n        category: 'Referral System',\n        question: 'How does the binary referral system work?',\n        answer: \"HashCoreX uses a binary tree referral system with three placement types:\\n\\nPlacement Types:\\n1. General Referral: Placed in weaker leg automatically\\n2. Left-Side Referral: Specifically placed on left side\\n3. Right-Side Referral: Specifically placed on right side\\n\\nEarning Structure:\\n• Direct Referral Commission: 10% one-time bonus when your referral purchases mining units\\n• Binary Matching Bonus: Weekly matching of binary points (1 point = $10)\\n\\nBinary Points:\\n• Earned when your referrals purchase mining units\\n• $150 purchase = 1.5 points (supports 2 decimal places)\\n• Points are matched weekly between left and right sides\\n• Excess points reset to 0 after matching\\n\\nRequirements:\\n• You must have active mining units to earn commissions\\n• Binary matching occurs weekly at 15:00 UTC\\n• All earnings are allocated to your mining units using FIFO system\",\n        icon: _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        tags: [\n            'referral',\n            'binary',\n            'commission',\n            'points',\n            'matching'\n        ]\n    },\n    {\n        id: '5',\n        category: 'Account & Security',\n        question: 'What is KYC and why is it required?',\n        answer: \"KYC (Know Your Customer) Verification:\\n\\nKYC is a mandatory verification process required for all HashCoreX users to ensure compliance with financial regulations.\\n\\nKYC Process:\\n1. Personal Information: Provide accurate personal details\\n2. ID Document: Upload government-issued ID (passport, driver's license, etc.)\\n3. Selfie Verification: Take a selfie holding your ID document\\n4. Admin Review: Our team reviews your submission (typically 24-48 hours)\\n\\nKYC Status:\\n• Not Submitted: KYC documents not yet uploaded\\n• Pending: Under admin review\\n• Approved: Verification complete - full access granted\\n• Rejected: Resubmission required with correct documents\\n\\nWhy KYC is Required:\\n• Legal compliance with financial regulations\\n• Account security and fraud prevention\\n• Enables withdrawals and full platform access\\n• Protects both users and the platform\\n\\nImportant: Profile name fields become read-only after KYC submission to prevent fraud.\",\n        icon: _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        tags: [\n            'kyc',\n            'verification',\n            'security',\n            'compliance',\n            'identity'\n        ]\n    },\n    {\n        id: '6',\n        category: 'Technical Support',\n        question: 'What should I do if I encounter technical issues?',\n        answer: \"Common Solutions:\\n\\nLogin Issues:\\n• Clear browser cache and cookies\\n• Try incognito/private browsing mode\\n• Ensure you're using the correct email address\\n• Check if your account is active\\n\\nTransaction Issues:\\n• Verify transaction hash on Tron blockchain explorer\\n• Check if you're using the correct network (Mainnet/Testnet)\\n• Ensure sufficient balance for fees\\n• Wait for blockchain confirmations\\n\\nDisplay Issues:\\n• Refresh the page\\n• Try a different browser\\n• Disable browser extensions temporarily\\n• Check your internet connection\\n\\nStill Need Help?\\n1. Create a support ticket with detailed information\\n2. Include screenshots if possible\\n3. Provide transaction hashes for payment issues\\n4. Specify your browser and device type\\n\\nResponse Times:\\n• General inquiries: 24-48 hours\\n• Technical issues: 12-24 hours\\n• Payment issues: Priority handling within 12 hours\",\n        icon: _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        tags: [\n            'technical',\n            'support',\n            'troubleshooting',\n            'login',\n            'transactions'\n        ]\n    },\n    {\n        id: '7',\n        category: 'Mining Units',\n        question: 'What happens when mining units expire?',\n        answer: 'Mining units expire under two conditions:\\n\\nExpiry Conditions:\\n• After 24 months from purchase date\\n• When they reach 5x their investment amount (whichever comes first)\\n\\nFIFO Expiry System:\\n• Oldest mining units expire first\\n• Earnings are allocated to oldest units first\\n• This ensures fair distribution and maximum returns\\n\\nWhat Happens at Expiry:\\n• Unit stops generating daily returns\\n• All accumulated earnings are credited to your wallet\\n• Unit status changes to \"Expired\"\\n• No further earnings from that specific unit\\n\\nImportant Notes:\\n• You can track each unit\\'s progress toward 5x return\\n• Expired units are shown in your mining history\\n• Purchase new units to continue earning\\n• All earnings are subject to weekly payout schedule',\n        icon: _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        tags: [\n            'mining',\n            'expiry',\n            'fifo',\n            'returns',\n            'units'\n        ]\n    },\n    {\n        id: '8',\n        category: 'Wallet & Payments',\n        question: 'How do I set up my TRC20 wallet address?',\n        answer: \"Setting up your TRC20 wallet address for withdrawals:\\n\\nSupported Wallets:\\n• TronLink (Browser extension)\\n• Trust Wallet (Mobile app)\\n• Atomic Wallet (Desktop/Mobile)\\n• Any wallet supporting TRC20 tokens\\n\\nSetup Process:\\n1. Go to Profile Settings → Billing & Payments\\n2. Enter your TRC20 wallet address\\n3. Verify the address is correct (double-check!)\\n4. Save the address for future withdrawals\\n\\nImportant Security Tips:\\n• Always copy-paste addresses, never type manually\\n• Test with a small amount first\\n• Ensure your wallet supports USDT TRC20\\n• Keep your private keys secure and never share them\\n• Use only your own wallet addresses\\n\\nNetwork Information:\\n• Network: Tron (TRX)\\n• Token Standard: TRC20\\n• Supported Token: USDT\\n• Withdrawal fees apply as per platform settings\",\n        icon: _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        tags: [\n            'wallet',\n            'trc20',\n            'setup',\n            'withdrawal',\n            'address'\n        ]\n    },\n    {\n        id: '9',\n        category: 'Referral System',\n        question: 'How can I maximize my referral earnings?',\n        answer: \"Strategies to maximize your referral earnings:\\n\\nBuilding Your Network:\\n• Share your referral links on social media\\n• Explain the benefits of HashCoreX to friends and family\\n• Use different placement types strategically\\n• Focus on quality referrals who will be active\\n\\nPlacement Strategy:\\n• Use general referrals for automatic balancing\\n• Use left/right specific placements for strategic building\\n• Monitor your binary tree balance regularly\\n• Help your referrals understand the system\\n\\nEarning Optimization:\\n• Maintain active mining units to earn commissions\\n• Focus on both direct referrals and binary matching\\n• Track your binary points weekly\\n• Reinvest earnings to purchase more mining units\\n\\nBest Practices:\\n• Provide support to your referrals\\n• Share educational content about mining\\n• Be transparent about risks and rewards\\n• Build long-term relationships, not just quick referrals\\n\\nRemember: You must have active mining units to earn any referral commissions.\",\n        icon: _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        tags: [\n            'referral',\n            'earnings',\n            'strategy',\n            'network',\n            'optimization'\n        ]\n    },\n    {\n        id: '10',\n        category: 'Account & Security',\n        question: 'How do I reset my password?',\n        answer: \"Password reset process:\\n\\nStep-by-Step Process:\\n1. Go to the login page\\n2. Click \\\"Forgot Password\\\" link\\n3. Enter your registered email address\\n4. Check your email for OTP verification code\\n5. Enter the OTP code when prompted\\n6. Create a new strong password\\n7. Confirm your new password\\n8. You'll be automatically redirected to dashboard\\n\\nPassword Requirements:\\n• Minimum 8 characters\\n• Include uppercase and lowercase letters\\n• Include at least one number\\n• Include at least one special character\\n• Avoid common passwords\\n\\nSecurity Tips:\\n• Use a unique password for HashCoreX\\n• Consider using a password manager\\n• Don't share your password with anyone\\n• Change your password regularly\\n• Log out from shared devices\\n\\nTroubleshooting:\\n• Check spam folder for OTP email\\n• Ensure email address is correct\\n• Wait a few minutes for email delivery\\n• Contact support if issues persist\",\n        icon: _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        tags: [\n            'password',\n            'reset',\n            'security',\n            'otp',\n            'login'\n        ]\n    },\n    {\n        id: '11',\n        category: 'Getting Started',\n        question: 'What are the minimum requirements to start?',\n        answer: \"Minimum requirements to start mining with HashCoreX:\\n\\nFinancial Requirements:\\n• Minimum deposit: $50 USDT (TRC20)\\n• Minimum mining unit purchase: As configured by admin\\n• Transaction fees: Network fees for deposits/withdrawals\\n\\nAccount Requirements:\\n• Valid email address for registration\\n• Complete KYC verification (mandatory)\\n• TRC20 wallet for deposits and withdrawals\\n• Active status (maintained by having mining units)\\n\\nTechnical Requirements:\\n• Modern web browser (Chrome, Firefox, Safari, Edge)\\n• Stable internet connection\\n• Email access for verification and notifications\\n• Basic understanding of cryptocurrency transactions\\n\\nGetting Started Checklist:\\n1. ✓ Register with valid email\\n2. ✓ Verify email address\\n3. ✓ Complete KYC verification\\n4. ✓ Set up TRC20 wallet\\n5. ✓ Make first deposit ($50+ USDT)\\n6. ✓ Purchase first mining units\\n7. ✓ Start earning daily returns\\n\\nTime Investment:\\n• Initial setup: 30-60 minutes\\n• Daily monitoring: 5-10 minutes\\n• Weekly review: 15-30 minutes\",\n        icon: _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        tags: [\n            'requirements',\n            'minimum',\n            'getting started',\n            'checklist',\n            'setup'\n        ]\n    },\n    {\n        id: '12',\n        category: 'Mining Units',\n        question: 'How do I track my mining performance?',\n        answer: \"Tracking your mining performance effectively:\\n\\nDashboard Overview:\\n• Total active TH/s amount\\n• Daily earnings summary\\n• Pending vs available balance\\n• Mining units status overview\\n\\nDetailed Tracking:\\n• Individual unit performance\\n• Progress toward 5x return limit\\n• Daily ROI percentages (7-day average)\\n• Earnings allocation per unit\\n\\nKey Metrics to Monitor:\\n• Total investment amount\\n• Total earnings to date\\n• Average daily return percentage\\n• Time remaining until expiry\\n• FIFO allocation progress\\n\\nPerformance Analysis:\\n• Compare actual vs expected returns\\n• Monitor market condition impacts\\n• Track weekly payout schedules\\n• Review binary earnings allocation\\n\\nOptimization Tips:\\n• Reinvest earnings for compound growth\\n• Monitor unit expiry dates\\n• Balance investment across different TH/s ranges\\n• Keep track of referral earnings contribution\\n\\nReports Available:\\n• Transaction history\\n• Earnings breakdown\\n• Mining unit details\\n• Referral performance summary\",\n        icon: _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        tags: [\n            'tracking',\n            'performance',\n            'monitoring',\n            'analytics',\n            'optimization'\n        ]\n    }\n];\nconst SupportCenter = ()=>{\n    _s();\n    const [tickets, setTickets] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showNewTicketModal, setShowNewTicketModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedTicket, setSelectedTicket] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedFAQ, setSelectedFAQ] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [faqSearchTerm, setFaqSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('All');\n    const [newTicketForm, setNewTicketForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        subject: '',\n        message: '',\n        priority: 'MEDIUM'\n    });\n    const [newResponse, setNewResponse] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [submitting, setSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SupportCenter.useEffect\": ()=>{\n            fetchTickets();\n        }\n    }[\"SupportCenter.useEffect\"], []);\n    // Get unique categories for filtering\n    const categories = [\n        'All',\n        ...Array.from(new Set(faqData.map((faq)=>faq.category)))\n    ];\n    // Filter FAQs based on search term and category\n    const filteredFAQs = faqData.filter((faq)=>{\n        const matchesSearch = faq.question.toLowerCase().includes(faqSearchTerm.toLowerCase()) || faq.answer.toLowerCase().includes(faqSearchTerm.toLowerCase()) || faq.tags.some((tag)=>tag.toLowerCase().includes(faqSearchTerm.toLowerCase()));\n        const matchesCategory = selectedCategory === 'All' || faq.category === selectedCategory;\n        return matchesSearch && matchesCategory;\n    });\n    const fetchTickets = async ()=>{\n        try {\n            const response = await fetch('/api/support/tickets', {\n                credentials: 'include'\n            });\n            if (response.ok) {\n                const data = await response.json();\n                if (data.success) {\n                    setTickets(data.data);\n                }\n            }\n        } catch (error) {\n            console.error('Failed to fetch tickets:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const createTicket = async (e)=>{\n        e.preventDefault();\n        setSubmitting(true);\n        try {\n            const response = await fetch('/api/support/tickets', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                credentials: 'include',\n                body: JSON.stringify(newTicketForm)\n            });\n            if (response.ok) {\n                setNewTicketForm({\n                    subject: '',\n                    message: '',\n                    priority: 'MEDIUM'\n                });\n                setShowNewTicketModal(false);\n                fetchTickets();\n            }\n        } catch (error) {\n            console.error('Failed to create ticket:', error);\n        } finally{\n            setSubmitting(false);\n        }\n    };\n    const addResponse = async (ticketId)=>{\n        if (!newResponse.trim()) return;\n        try {\n            const response = await fetch(\"/api/support/tickets/\".concat(ticketId, \"/responses\"), {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                credentials: 'include',\n                body: JSON.stringify({\n                    message: newResponse\n                })\n            });\n            if (response.ok) {\n                setNewResponse('');\n                fetchTickets();\n                // Update selected ticket\n                const updatedTickets = await fetch('/api/support/tickets', {\n                    credentials: 'include'\n                });\n                if (updatedTickets.ok) {\n                    const data = await updatedTickets.json();\n                    const updatedTicket = data.data.find((t)=>t.id === ticketId);\n                    if (updatedTicket) {\n                        setSelectedTicket(updatedTicket);\n                    }\n                }\n            }\n        } catch (error) {\n            console.error('Failed to add response:', error);\n        }\n    };\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case 'OPEN':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    className: \"h-4 w-4 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                    lineNumber: 560,\n                    columnNumber: 16\n                }, undefined);\n            case 'IN_PROGRESS':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"h-4 w-4 text-solar-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                    lineNumber: 562,\n                    columnNumber: 16\n                }, undefined);\n            case 'RESOLVED':\n            case 'CLOSED':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    className: \"h-4 w-4 text-eco-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                    lineNumber: 565,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    className: \"h-4 w-4 text-gray-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                    lineNumber: 567,\n                    columnNumber: 16\n                }, undefined);\n        }\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case 'OPEN':\n                return 'bg-red-100 text-red-700';\n            case 'IN_PROGRESS':\n                return 'bg-solar-100 text-solar-700';\n            case 'RESOLVED':\n            case 'CLOSED':\n                return 'bg-eco-100 text-eco-700';\n            default:\n                return 'bg-gray-100 text-gray-700';\n        }\n    };\n    const getPriorityColor = (priority)=>{\n        switch(priority){\n            case 'URGENT':\n                return 'bg-red-100 text-red-700';\n            case 'HIGH':\n                return 'bg-orange-100 text-orange-700';\n            case 'MEDIUM':\n                return 'bg-solar-100 text-solar-700';\n            case 'LOW':\n                return 'bg-gray-100 text-gray-700';\n            default:\n                return 'bg-gray-100 text-gray-700';\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-pulse\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-8 bg-gray-200 rounded w-1/4 mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                        lineNumber: 604,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-64 bg-gray-200 rounded\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                        lineNumber: 605,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                lineNumber: 603,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n            lineNumber: 602,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: \"Support Center\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                lineNumber: 616,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mt-1\",\n                                children: \"Get help and manage support tickets\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                lineNumber: 617,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                        lineNumber: 615,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        onClick: ()=>setShowNewTicketModal(true),\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                lineNumber: 623,\n                                columnNumber: 11\n                            }, undefined),\n                            \"New Ticket\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                        lineNumber: 619,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                lineNumber: 614,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                        lineNumber: 633,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    \"Support Tickets\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                lineNumber: 632,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                            lineNumber: 631,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            children: tickets.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: tickets.map((ticket)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        onClick: ()=>setSelectedTicket(ticket),\n                                        className: \"p-4 border border-gray-200 rounded-lg cursor-pointer\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start justify-between mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-gray-900 truncate flex-1\",\n                                                        children: ticket.subject\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                        lineNumber: 647,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2 ml-2\",\n                                                        children: [\n                                                            getStatusIcon(ticket.status),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs px-2 py-1 rounded-full \".concat(getStatusColor(ticket.status)),\n                                                                children: ticket.status\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                                lineNumber: 652,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                        lineNumber: 650,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                lineNumber: 646,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600 mb-2 line-clamp-2\",\n                                                children: ticket.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                lineNumber: 657,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between text-xs text-gray-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-2 py-1 rounded-full \".concat(getPriorityColor(ticket.priority)),\n                                                        children: ticket.priority\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                        lineNumber: 661,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.formatDateTime)(ticket.createdAt)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                        lineNumber: 664,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                lineNumber: 660,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, ticket.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                        lineNumber: 641,\n                                        columnNumber: 21\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                lineNumber: 639,\n                                columnNumber: 17\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                        lineNumber: 671,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                                        children: \"No Support Tickets\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                        lineNumber: 672,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"You haven't created any support tickets yet.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                        lineNumber: 673,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                lineNumber: 670,\n                                columnNumber: 17\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                            lineNumber: 637,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                    lineNumber: 630,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                lineNumber: 629,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                        lineNumber: 685,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    \"Help Tutorials & FAQ\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                lineNumber: 684,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                            lineNumber: 683,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6 space-y-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col sm:flex-row gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                                    placeholder: \"Search FAQs...\",\n                                                    value: faqSearchTerm,\n                                                    onChange: (e)=>setFaqSearchTerm(e.target.value),\n                                                    className: \"w-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                    lineNumber: 694,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                lineNumber: 693,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"sm:w-48\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: selectedCategory,\n                                                    onChange: (e)=>setSelectedCategory(e.target.value),\n                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-solar-500 focus:border-transparent\",\n                                                    children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: category,\n                                                            children: category\n                                                        }, category, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                            lineNumber: 708,\n                                                            columnNumber: 23\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                    lineNumber: 702,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                lineNumber: 701,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                        lineNumber: 692,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                    lineNumber: 691,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                                    children: filteredFAQs.map((faq)=>{\n                                        const IconComponent = faq.icon;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            onClick: ()=>setSelectedFAQ(faq),\n                                            className: \"p-6 border border-gray-200 rounded-lg cursor-pointer hover:border-solar-300 hover:shadow-md transition-all duration-200 bg-white\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start gap-3 mb-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-2 bg-solar-100 rounded-lg\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                                className: \"h-5 w-5 text-solar-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                                lineNumber: 727,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                            lineNumber: 726,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs font-medium text-solar-600 bg-solar-50 px-2 py-1 rounded-full\",\n                                                                children: faq.category\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                                lineNumber: 730,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                            lineNumber: 729,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                    lineNumber: 725,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-semibold text-gray-900 mb-2 line-clamp-2\",\n                                                    children: faq.question\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                    lineNumber: 735,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600 line-clamp-3 mb-3\",\n                                                    children: faq.answer.split('\\n')[0]\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                    lineNumber: 738,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-wrap gap-1\",\n                                                    children: [\n                                                        faq.tags.slice(0, 3).map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded\",\n                                                                children: tag\n                                                            }, tag, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                                lineNumber: 743,\n                                                                columnNumber: 25\n                                                            }, undefined)),\n                                                        faq.tags.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-gray-500\",\n                                                            children: [\n                                                                \"+\",\n                                                                faq.tags.length - 3,\n                                                                \" more\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                            lineNumber: 748,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                    lineNumber: 741,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, faq.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                            lineNumber: 720,\n                                            columnNumber: 19\n                                        }, undefined);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                    lineNumber: 716,\n                                    columnNumber: 13\n                                }, undefined),\n                                filteredFAQs.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-12\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                            lineNumber: 758,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-gray-900 mb-2\",\n                                            children: \"No FAQs Found\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                            lineNumber: 759,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: faqSearchTerm || selectedCategory !== 'All' ? 'Try adjusting your search or filter criteria.' : 'FAQ content is being updated.'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                            lineNumber: 760,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                    lineNumber: 757,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                            lineNumber: 689,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                    lineNumber: 682,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                lineNumber: 681,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Modal, {\n                isOpen: showNewTicketModal,\n                onClose: ()=>setShowNewTicketModal(false),\n                title: \"Create Support Ticket\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: createTicket,\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                            label: \"Subject\",\n                            value: newTicketForm.subject,\n                            onChange: (e)=>setNewTicketForm((prev)=>({\n                                        ...prev,\n                                        subject: e.target.value\n                                    })),\n                            placeholder: \"Brief description of your issue\",\n                            required: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                            lineNumber: 778,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"Priority\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                    lineNumber: 787,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: newTicketForm.priority,\n                                    onChange: (e)=>setNewTicketForm((prev)=>({\n                                                ...prev,\n                                                priority: e.target.value\n                                            })),\n                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-solar-500 focus:border-transparent\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"LOW\",\n                                            children: \"Low\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                            lineNumber: 795,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"MEDIUM\",\n                                            children: \"Medium\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                            lineNumber: 796,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"HIGH\",\n                                            children: \"High\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                            lineNumber: 797,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"URGENT\",\n                                            children: \"Urgent\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                            lineNumber: 798,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                    lineNumber: 790,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                            lineNumber: 786,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"Message\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                    lineNumber: 803,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                    value: newTicketForm.message,\n                                    onChange: (e)=>setNewTicketForm((prev)=>({\n                                                ...prev,\n                                                message: e.target.value\n                                            })),\n                                    placeholder: \"Describe your issue in detail...\",\n                                    rows: 4,\n                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-solar-500 focus:border-transparent\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                    lineNumber: 806,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                            lineNumber: 802,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    type: \"button\",\n                                    variant: \"outline\",\n                                    onClick: ()=>setShowNewTicketModal(false),\n                                    className: \"flex-1\",\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                    lineNumber: 817,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    type: \"submit\",\n                                    loading: submitting,\n                                    className: \"flex-1\",\n                                    children: \"Create Ticket\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                    lineNumber: 825,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                            lineNumber: 816,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                    lineNumber: 777,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                lineNumber: 772,\n                columnNumber: 7\n            }, undefined),\n            selectedTicket && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Modal, {\n                isOpen: !!selectedTicket,\n                onClose: ()=>setSelectedTicket(null),\n                title: \"Ticket: \".concat(selectedTicket.subject),\n                size: \"xl\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                getStatusIcon(selectedTicket.status),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm px-2 py-1 rounded-full \".concat(getStatusColor(selectedTicket.status)),\n                                    children: selectedTicket.status\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                    lineNumber: 847,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm px-2 py-1 rounded-full \".concat(getPriorityColor(selectedTicket.priority)),\n                                    children: selectedTicket.priority\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                    lineNumber: 850,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                            lineNumber: 845,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-50 p-4 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600 mb-2\",\n                                    children: \"Original Message:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                    lineNumber: 856,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-900\",\n                                    children: selectedTicket.message\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                    lineNumber: 857,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-500 mt-2\",\n                                    children: [\n                                        \"Created: \",\n                                        (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.formatDateTime)(selectedTicket.createdAt)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                    lineNumber: 858,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                            lineNumber: 855,\n                            columnNumber: 13\n                        }, undefined),\n                        selectedTicket.responses.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"font-medium text-gray-900\",\n                                    children: \"Responses:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                    lineNumber: 865,\n                                    columnNumber: 17\n                                }, undefined),\n                                selectedTicket.responses.map((response)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 rounded-lg \".concat(response.isAdmin ? 'bg-blue-50 border-l-4 border-blue-500' : 'bg-gray-50'),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-900\",\n                                                children: response.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                lineNumber: 873,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-500 mt-1\",\n                                                children: [\n                                                    response.isAdmin ? 'Support Team' : 'You',\n                                                    \" • \",\n                                                    (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.formatDateTime)(response.createdAt)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                lineNumber: 874,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, response.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                        lineNumber: 867,\n                                        columnNumber: 19\n                                    }, undefined))\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                            lineNumber: 864,\n                            columnNumber: 15\n                        }, undefined),\n                        selectedTicket.status !== 'CLOSED' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                    value: newResponse,\n                                    onChange: (e)=>setNewResponse(e.target.value),\n                                    placeholder: \"Add a response...\",\n                                    rows: 3,\n                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-solar-500 focus:border-transparent\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                    lineNumber: 884,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: ()=>addResponse(selectedTicket.id),\n                                    disabled: !newResponse.trim(),\n                                    className: \"w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                            lineNumber: 896,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        \"Send Response\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                    lineNumber: 891,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                            lineNumber: 883,\n                            columnNumber: 15\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                    lineNumber: 844,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                lineNumber: 838,\n                columnNumber: 9\n            }, undefined),\n            selectedFAQ && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Modal, {\n                isOpen: !!selectedFAQ,\n                onClose: ()=>setSelectedFAQ(null),\n                title: selectedFAQ.question,\n                size: \"xl\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-3 pb-4 border-b border-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 bg-solar-100 rounded-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(selectedFAQ.icon, {\n                                        className: \"h-6 w-6 text-solar-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                        lineNumber: 916,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                    lineNumber: 915,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium text-solar-600 bg-solar-50 px-3 py-1 rounded-full\",\n                                        children: selectedFAQ.category\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                        lineNumber: 919,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                    lineNumber: 918,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                            lineNumber: 914,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"prose prose-sm max-w-none\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"whitespace-pre-line text-gray-700 leading-relaxed\",\n                                children: selectedFAQ.answer\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                lineNumber: 926,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                            lineNumber: 925,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"pt-4 border-t border-gray-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium text-gray-700 mr-2\",\n                                        children: \"Tags:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                        lineNumber: 933,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    selectedFAQ.tags.map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-solar-600 bg-solar-50 px-3 py-1 rounded-full\",\n                                            children: tag\n                                        }, tag, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                            lineNumber: 935,\n                                            columnNumber: 19\n                                        }, undefined))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                lineNumber: 932,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                            lineNumber: 931,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"pt-4 border-t border-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600 mb-3\",\n                                    children: \"Still need help? Create a support ticket for personalized assistance.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                    lineNumber: 943,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: ()=>{\n                                        setSelectedFAQ(null);\n                                        setShowNewTicketModal(true);\n                                    },\n                                    className: \"w-full sm:w-auto\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                            lineNumber: 953,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        \"Create Support Ticket\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                    lineNumber: 946,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                            lineNumber: 942,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                    lineNumber: 913,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                lineNumber: 907,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n        lineNumber: 612,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SupportCenter, \"vrB8hUSPY7c0kHi45cPRLsly9qg=\");\n_c = SupportCenter;\nvar _c;\n$RefreshReg$(_c, \"SupportCenter\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/SupportCenter.tsx\n"));

/***/ })

});