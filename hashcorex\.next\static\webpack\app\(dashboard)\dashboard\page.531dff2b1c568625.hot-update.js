"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/SupportCenter.tsx":
/*!****************************************************!*\
  !*** ./src/components/dashboard/SupportCenter.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SupportCenter: () => (/* binding */ SupportCenter)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BookOpen,CheckCircle,Clock,HelpCircle,MessageCircle,Plus,Send,Settings,Shield,TrendingUp,Users,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BookOpen,CheckCircle,Clock,HelpCircle,MessageCircle,Plus,Send,Settings,Shield,TrendingUp,Users,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wallet.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BookOpen,CheckCircle,Clock,HelpCircle,MessageCircle,Plus,Send,Settings,Shield,TrendingUp,Users,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BookOpen,CheckCircle,Clock,HelpCircle,MessageCircle,Plus,Send,Settings,Shield,TrendingUp,Users,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BookOpen,CheckCircle,Clock,HelpCircle,MessageCircle,Plus,Send,Settings,Shield,TrendingUp,Users,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BookOpen,CheckCircle,Clock,HelpCircle,MessageCircle,Plus,Send,Settings,Shield,TrendingUp,Users,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BookOpen,CheckCircle,Clock,HelpCircle,MessageCircle,Plus,Send,Settings,Shield,TrendingUp,Users,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BookOpen,CheckCircle,Clock,HelpCircle,MessageCircle,Plus,Send,Settings,Shield,TrendingUp,Users,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BookOpen,CheckCircle,Clock,HelpCircle,MessageCircle,Plus,Send,Settings,Shield,TrendingUp,Users,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BookOpen,CheckCircle,Clock,HelpCircle,MessageCircle,Plus,Send,Settings,Shield,TrendingUp,Users,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BookOpen,CheckCircle,Clock,HelpCircle,MessageCircle,Plus,Send,Settings,Shield,TrendingUp,Users,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-question-mark.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BookOpen,CheckCircle,Clock,HelpCircle,MessageCircle,Plus,Send,Settings,Shield,TrendingUp,Users,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BookOpen,CheckCircle,Clock,HelpCircle,MessageCircle,Plus,Send,Settings,Shield,TrendingUp,Users,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ SupportCenter auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n// FAQ Data\nconst faqData = [\n    {\n        id: '1',\n        category: 'Getting Started',\n        question: 'How do I start mining with HashCoreX?',\n        answer: \"To start mining with HashCoreX, follow these simple steps:\\n\\n1. Complete KYC Verification: First, complete your KYC verification in the Profile section. This is required for all mining activities.\\n\\n2. Deposit Funds: Go to your Wallet and deposit USDT (TRC20) to fund your account. The minimum deposit amount is configured by the admin.\\n\\n3. Purchase Mining Units: Navigate to the Mining section and purchase mining units. Choose your desired TH/s amount and investment level.\\n\\n4. Start Earning: Your mining units will start generating daily returns automatically. Earnings are calculated based on your TH/s amount and current market conditions.\\n\\n5. Track Progress: Monitor your earnings in the Earnings section and watch your mining units progress toward their 5x investment return limit.\\n\\nRemember: Mining units expire after 24 months or when they reach 5x their investment amount, whichever comes first.\",\n        icon: _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        tags: [\n            'mining',\n            'getting started',\n            'kyc',\n            'deposit'\n        ]\n    },\n    {\n        id: '2',\n        category: 'Mining Units',\n        question: 'How are daily returns calculated?',\n        answer: \"Daily returns are calculated using a dynamic system based on your mining unit's TH/s amount:\\n\\n**Dynamic ROI System:**\\n- Different TH/s ranges have different return rates\\n- Returns are calculated as: (Investment Amount \\xd7 Daily ROI%) \\xf7 100\\n- ROI percentages are determined by admin-configured ranges\\n\\n**Example Ranges:**\\n- 0-10 TH/s: 0.3% - 0.5% daily\\n- 10-50 TH/s: 0.4% - 0.6% daily\\n- 50+ TH/s: 0.5% - 0.7% daily\\n\\n**Important Notes:**\\n- Returns are credited daily but paid out weekly (Sundays at 00:00 AM GMT+5:30)\\n- Mining units expire when they reach 5x their investment amount\\n- FIFO system: Oldest units receive earnings first\\n- All earnings are subject to market conditions and admin configuration\",\n        icon: _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        tags: [\n            'mining',\n            'returns',\n            'calculation',\n            'roi'\n        ]\n    },\n    {\n        id: '3',\n        category: 'Wallet & Payments',\n        question: 'How do deposits and withdrawals work?',\n        answer: \"**Deposits:**\\n- Only USDT (TRC20) deposits are accepted\\n- Minimum deposit amount is set by admin (typically $50)\\n- Deposits are automatically verified on the Tron blockchain\\n- Funds are available immediately after confirmation\\n\\n**Withdrawals:**\\n- Minimum withdrawal: $50\\n- Fixed fee: $3 + 1% of withdrawal amount\\n- Processing time: Up to 3 business days\\n- Only to verified TRC20 addresses\\n\\n**Wallet Balance:**\\n- Available Balance: Funds ready for use or withdrawal\\n- Pending Balance: Earnings waiting for weekly distribution\\n- Total Earnings: Cumulative earnings from all sources\\n\\n**Important:**\\n- Complete KYC verification before making withdrawals\\n- Ensure your TRC20 address is correct before submitting\\n- Withdrawal fees are deducted from your balance\",\n        icon: _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        tags: [\n            'wallet',\n            'deposit',\n            'withdrawal',\n            'usdt',\n            'trc20'\n        ]\n    },\n    {\n        id: '4',\n        category: 'Referral System',\n        question: 'How does the binary referral system work?',\n        answer: \"HashCoreX uses a binary tree referral system with three placement types:\\n\\n**Placement Types:**\\n1. **General Referral**: Placed in weaker leg automatically\\n2. **Left-Side Referral**: Specifically placed on left side\\n3. **Right-Side Referral**: Specifically placed on right side\\n\\n**Earning Structure:**\\n- **Direct Referral Commission**: 10% one-time bonus when your referral purchases mining units\\n- **Binary Matching Bonus**: Weekly matching of binary points (1 point = $10)\\n\\n**Binary Points:**\\n- Earned when your referrals purchase mining units\\n- $150 purchase = 1.5 points (supports 2 decimal places)\\n- Points are matched weekly between left and right sides\\n- Excess points reset to 0 after matching\\n\\n**Requirements:**\\n- You must have active mining units to earn commissions\\n- Binary matching occurs weekly at 15:00 UTC\\n- All earnings are allocated to your mining units using FIFO system\",\n        icon: _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        tags: [\n            'referral',\n            'binary',\n            'commission',\n            'points',\n            'matching'\n        ]\n    },\n    {\n        id: '5',\n        category: 'Account & Security',\n        question: 'What is KYC and why is it required?',\n        answer: \"**KYC (Know Your Customer) Verification:**\\n\\nKYC is a mandatory verification process required for all HashCoreX users to ensure compliance with financial regulations.\\n\\n**KYC Process:**\\n1. **Personal Information**: Provide accurate personal details\\n2. **ID Document**: Upload government-issued ID (passport, driver's license, etc.)\\n3. **Selfie Verification**: Take a selfie holding your ID document\\n4. **Admin Review**: Our team reviews your submission (typically 24-48 hours)\\n\\n**KYC Status:**\\n- **Not Submitted**: KYC documents not yet uploaded\\n- **Pending**: Under admin review\\n- **Approved**: Verification complete - full access granted\\n- **Rejected**: Resubmission required with correct documents\\n\\n**Why KYC is Required:**\\n- Legal compliance with financial regulations\\n- Account security and fraud prevention\\n- Enables withdrawals and full platform access\\n- Protects both users and the platform\\n\\n**Important:** Profile name fields become read-only after KYC submission to prevent fraud.\",\n        icon: _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        tags: [\n            'kyc',\n            'verification',\n            'security',\n            'compliance',\n            'identity'\n        ]\n    },\n    {\n        id: '6',\n        category: 'Technical Support',\n        question: 'What should I do if I encounter technical issues?',\n        answer: \"**Common Solutions:**\\n\\n**Login Issues:**\\n- Clear browser cache and cookies\\n- Try incognito/private browsing mode\\n- Ensure you're using the correct email address\\n- Check if your account is active\\n\\n**Transaction Issues:**\\n- Verify transaction hash on Tron blockchain explorer\\n- Check if you're using the correct network (Mainnet/Testnet)\\n- Ensure sufficient balance for fees\\n- Wait for blockchain confirmations\\n\\n**Display Issues:**\\n- Refresh the page\\n- Try a different browser\\n- Disable browser extensions temporarily\\n- Check your internet connection\\n\\n**Still Need Help?**\\n1. Create a support ticket with detailed information\\n2. Include screenshots if possible\\n3. Provide transaction hashes for payment issues\\n4. Specify your browser and device type\\n\\n**Response Times:**\\n- General inquiries: 24-48 hours\\n- Technical issues: 12-24 hours\\n- Payment issues: Priority handling within 12 hours\",\n        icon: _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        tags: [\n            'technical',\n            'support',\n            'troubleshooting',\n            'login',\n            'transactions'\n        ]\n    }\n];\nconst SupportCenter = ()=>{\n    _s();\n    const [tickets, setTickets] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showNewTicketModal, setShowNewTicketModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedTicket, setSelectedTicket] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedFAQ, setSelectedFAQ] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [faqSearchTerm, setFaqSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('All');\n    const [newTicketForm, setNewTicketForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        subject: '',\n        message: '',\n        priority: 'MEDIUM'\n    });\n    const [newResponse, setNewResponse] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [submitting, setSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SupportCenter.useEffect\": ()=>{\n            fetchTickets();\n        }\n    }[\"SupportCenter.useEffect\"], []);\n    // Get unique categories for filtering\n    const categories = [\n        'All',\n        ...Array.from(new Set(faqData.map((faq)=>faq.category)))\n    ];\n    // Filter FAQs based on search term and category\n    const filteredFAQs = faqData.filter((faq)=>{\n        const matchesSearch = faq.question.toLowerCase().includes(faqSearchTerm.toLowerCase()) || faq.answer.toLowerCase().includes(faqSearchTerm.toLowerCase()) || faq.tags.some((tag)=>tag.toLowerCase().includes(faqSearchTerm.toLowerCase()));\n        const matchesCategory = selectedCategory === 'All' || faq.category === selectedCategory;\n        return matchesSearch && matchesCategory;\n    });\n    const fetchTickets = async ()=>{\n        try {\n            const response = await fetch('/api/support/tickets', {\n                credentials: 'include'\n            });\n            if (response.ok) {\n                const data = await response.json();\n                if (data.success) {\n                    setTickets(data.data);\n                }\n            }\n        } catch (error) {\n            console.error('Failed to fetch tickets:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const createTicket = async (e)=>{\n        e.preventDefault();\n        setSubmitting(true);\n        try {\n            const response = await fetch('/api/support/tickets', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                credentials: 'include',\n                body: JSON.stringify(newTicketForm)\n            });\n            if (response.ok) {\n                setNewTicketForm({\n                    subject: '',\n                    message: '',\n                    priority: 'MEDIUM'\n                });\n                setShowNewTicketModal(false);\n                fetchTickets();\n            }\n        } catch (error) {\n            console.error('Failed to create ticket:', error);\n        } finally{\n            setSubmitting(false);\n        }\n    };\n    const addResponse = async (ticketId)=>{\n        if (!newResponse.trim()) return;\n        try {\n            const response = await fetch(\"/api/support/tickets/\".concat(ticketId, \"/responses\"), {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                credentials: 'include',\n                body: JSON.stringify({\n                    message: newResponse\n                })\n            });\n            if (response.ok) {\n                setNewResponse('');\n                fetchTickets();\n                // Update selected ticket\n                const updatedTickets = await fetch('/api/support/tickets', {\n                    credentials: 'include'\n                });\n                if (updatedTickets.ok) {\n                    const data = await updatedTickets.json();\n                    const updatedTicket = data.data.find((t)=>t.id === ticketId);\n                    if (updatedTicket) {\n                        setSelectedTicket(updatedTicket);\n                    }\n                }\n            }\n        } catch (error) {\n            console.error('Failed to add response:', error);\n        }\n    };\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case 'OPEN':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"h-4 w-4 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                    lineNumber: 342,\n                    columnNumber: 16\n                }, undefined);\n            case 'IN_PROGRESS':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    className: \"h-4 w-4 text-solar-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                    lineNumber: 344,\n                    columnNumber: 16\n                }, undefined);\n            case 'RESOLVED':\n            case 'CLOSED':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    className: \"h-4 w-4 text-eco-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                    lineNumber: 347,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    className: \"h-4 w-4 text-gray-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                    lineNumber: 349,\n                    columnNumber: 16\n                }, undefined);\n        }\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case 'OPEN':\n                return 'bg-red-100 text-red-700';\n            case 'IN_PROGRESS':\n                return 'bg-solar-100 text-solar-700';\n            case 'RESOLVED':\n            case 'CLOSED':\n                return 'bg-eco-100 text-eco-700';\n            default:\n                return 'bg-gray-100 text-gray-700';\n        }\n    };\n    const getPriorityColor = (priority)=>{\n        switch(priority){\n            case 'URGENT':\n                return 'bg-red-100 text-red-700';\n            case 'HIGH':\n                return 'bg-orange-100 text-orange-700';\n            case 'MEDIUM':\n                return 'bg-solar-100 text-solar-700';\n            case 'LOW':\n                return 'bg-gray-100 text-gray-700';\n            default:\n                return 'bg-gray-100 text-gray-700';\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-pulse\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-8 bg-gray-200 rounded w-1/4 mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                        lineNumber: 386,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-64 bg-gray-200 rounded\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                        lineNumber: 387,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                lineNumber: 385,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n            lineNumber: 384,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: \"Support Center\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                lineNumber: 398,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mt-1\",\n                                children: \"Get help and manage support tickets\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                lineNumber: 399,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                        lineNumber: 397,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        onClick: ()=>setShowNewTicketModal(true),\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                lineNumber: 405,\n                                columnNumber: 11\n                            }, undefined),\n                            \"New Ticket\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                        lineNumber: 401,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                lineNumber: 396,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                        lineNumber: 415,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    \"Support Tickets\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                lineNumber: 414,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                            lineNumber: 413,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            children: tickets.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: tickets.map((ticket)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        onClick: ()=>setSelectedTicket(ticket),\n                                        className: \"p-4 border border-gray-200 rounded-lg cursor-pointer\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start justify-between mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-gray-900 truncate flex-1\",\n                                                        children: ticket.subject\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                        lineNumber: 429,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2 ml-2\",\n                                                        children: [\n                                                            getStatusIcon(ticket.status),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs px-2 py-1 rounded-full \".concat(getStatusColor(ticket.status)),\n                                                                children: ticket.status\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                                lineNumber: 434,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                        lineNumber: 432,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                lineNumber: 428,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600 mb-2 line-clamp-2\",\n                                                children: ticket.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                lineNumber: 439,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between text-xs text-gray-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-2 py-1 rounded-full \".concat(getPriorityColor(ticket.priority)),\n                                                        children: ticket.priority\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                        lineNumber: 443,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.formatDateTime)(ticket.createdAt)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                        lineNumber: 446,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                lineNumber: 442,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, ticket.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                        lineNumber: 423,\n                                        columnNumber: 21\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                lineNumber: 421,\n                                columnNumber: 17\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                        lineNumber: 453,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                                        children: \"No Support Tickets\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                        lineNumber: 454,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"You haven't created any support tickets yet.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                        lineNumber: 455,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                lineNumber: 452,\n                                columnNumber: 17\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                            lineNumber: 419,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                    lineNumber: 412,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                lineNumber: 411,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                        lineNumber: 467,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    \"Help Tutorials & FAQ\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                lineNumber: 466,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                            lineNumber: 465,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6 space-y-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col sm:flex-row gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                                    placeholder: \"Search FAQs...\",\n                                                    value: faqSearchTerm,\n                                                    onChange: (e)=>setFaqSearchTerm(e.target.value),\n                                                    className: \"w-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                    lineNumber: 476,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                lineNumber: 475,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"sm:w-48\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: selectedCategory,\n                                                    onChange: (e)=>setSelectedCategory(e.target.value),\n                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-solar-500 focus:border-transparent\",\n                                                    children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: category,\n                                                            children: category\n                                                        }, category, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                            lineNumber: 490,\n                                                            columnNumber: 23\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                    lineNumber: 484,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                lineNumber: 483,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                        lineNumber: 474,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                    lineNumber: 473,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                                    children: filteredFAQs.map((faq)=>{\n                                        const IconComponent = faq.icon;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            onClick: ()=>setSelectedFAQ(faq),\n                                            className: \"p-6 border border-gray-200 rounded-lg cursor-pointer hover:border-solar-300 hover:shadow-md transition-all duration-200 bg-white\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start gap-3 mb-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-2 bg-solar-100 rounded-lg\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                                className: \"h-5 w-5 text-solar-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                                lineNumber: 509,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                            lineNumber: 508,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs font-medium text-solar-600 bg-solar-50 px-2 py-1 rounded-full\",\n                                                                children: faq.category\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                                lineNumber: 512,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                            lineNumber: 511,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                    lineNumber: 507,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-semibold text-gray-900 mb-2 line-clamp-2\",\n                                                    children: faq.question\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                    lineNumber: 517,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600 line-clamp-3 mb-3\",\n                                                    children: faq.answer.split('\\n')[0]\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                    lineNumber: 520,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-wrap gap-1\",\n                                                    children: [\n                                                        faq.tags.slice(0, 3).map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded\",\n                                                                children: tag\n                                                            }, tag, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                                lineNumber: 525,\n                                                                columnNumber: 25\n                                                            }, undefined)),\n                                                        faq.tags.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-gray-500\",\n                                                            children: [\n                                                                \"+\",\n                                                                faq.tags.length - 3,\n                                                                \" more\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                            lineNumber: 530,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                    lineNumber: 523,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, faq.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                            lineNumber: 502,\n                                            columnNumber: 19\n                                        }, undefined);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                    lineNumber: 498,\n                                    columnNumber: 13\n                                }, undefined),\n                                filteredFAQs.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-12\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                            lineNumber: 540,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-gray-900 mb-2\",\n                                            children: \"No FAQs Found\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                            lineNumber: 541,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: faqSearchTerm || selectedCategory !== 'All' ? 'Try adjusting your search or filter criteria.' : 'FAQ content is being updated.'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                            lineNumber: 542,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                    lineNumber: 539,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                            lineNumber: 471,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                    lineNumber: 464,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                lineNumber: 463,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Modal, {\n                isOpen: showNewTicketModal,\n                onClose: ()=>setShowNewTicketModal(false),\n                title: \"Create Support Ticket\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: createTicket,\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                            label: \"Subject\",\n                            value: newTicketForm.subject,\n                            onChange: (e)=>setNewTicketForm((prev)=>({\n                                        ...prev,\n                                        subject: e.target.value\n                                    })),\n                            placeholder: \"Brief description of your issue\",\n                            required: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                            lineNumber: 560,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"Priority\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                    lineNumber: 569,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: newTicketForm.priority,\n                                    onChange: (e)=>setNewTicketForm((prev)=>({\n                                                ...prev,\n                                                priority: e.target.value\n                                            })),\n                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-solar-500 focus:border-transparent\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"LOW\",\n                                            children: \"Low\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                            lineNumber: 577,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"MEDIUM\",\n                                            children: \"Medium\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                            lineNumber: 578,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"HIGH\",\n                                            children: \"High\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                            lineNumber: 579,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"URGENT\",\n                                            children: \"Urgent\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                            lineNumber: 580,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                    lineNumber: 572,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                            lineNumber: 568,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"Message\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                    lineNumber: 585,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                    value: newTicketForm.message,\n                                    onChange: (e)=>setNewTicketForm((prev)=>({\n                                                ...prev,\n                                                message: e.target.value\n                                            })),\n                                    placeholder: \"Describe your issue in detail...\",\n                                    rows: 4,\n                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-solar-500 focus:border-transparent\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                    lineNumber: 588,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                            lineNumber: 584,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    type: \"button\",\n                                    variant: \"outline\",\n                                    onClick: ()=>setShowNewTicketModal(false),\n                                    className: \"flex-1\",\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                    lineNumber: 599,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    type: \"submit\",\n                                    loading: submitting,\n                                    className: \"flex-1\",\n                                    children: \"Create Ticket\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                    lineNumber: 607,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                            lineNumber: 598,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                    lineNumber: 559,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                lineNumber: 554,\n                columnNumber: 7\n            }, undefined),\n            selectedTicket && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Modal, {\n                isOpen: !!selectedTicket,\n                onClose: ()=>setSelectedTicket(null),\n                title: \"Ticket: \".concat(selectedTicket.subject),\n                size: \"xl\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                getStatusIcon(selectedTicket.status),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm px-2 py-1 rounded-full \".concat(getStatusColor(selectedTicket.status)),\n                                    children: selectedTicket.status\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                    lineNumber: 629,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm px-2 py-1 rounded-full \".concat(getPriorityColor(selectedTicket.priority)),\n                                    children: selectedTicket.priority\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                    lineNumber: 632,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                            lineNumber: 627,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-50 p-4 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600 mb-2\",\n                                    children: \"Original Message:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                    lineNumber: 638,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-900\",\n                                    children: selectedTicket.message\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                    lineNumber: 639,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-500 mt-2\",\n                                    children: [\n                                        \"Created: \",\n                                        (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.formatDateTime)(selectedTicket.createdAt)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                    lineNumber: 640,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                            lineNumber: 637,\n                            columnNumber: 13\n                        }, undefined),\n                        selectedTicket.responses.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"font-medium text-gray-900\",\n                                    children: \"Responses:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                    lineNumber: 647,\n                                    columnNumber: 17\n                                }, undefined),\n                                selectedTicket.responses.map((response)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 rounded-lg \".concat(response.isAdmin ? 'bg-blue-50 border-l-4 border-blue-500' : 'bg-gray-50'),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-900\",\n                                                children: response.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                lineNumber: 655,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-500 mt-1\",\n                                                children: [\n                                                    response.isAdmin ? 'Support Team' : 'You',\n                                                    \" • \",\n                                                    (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.formatDateTime)(response.createdAt)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                lineNumber: 656,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, response.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                        lineNumber: 649,\n                                        columnNumber: 19\n                                    }, undefined))\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                            lineNumber: 646,\n                            columnNumber: 15\n                        }, undefined),\n                        selectedTicket.status !== 'CLOSED' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                    value: newResponse,\n                                    onChange: (e)=>setNewResponse(e.target.value),\n                                    placeholder: \"Add a response...\",\n                                    rows: 3,\n                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-solar-500 focus:border-transparent\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                    lineNumber: 666,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: ()=>addResponse(selectedTicket.id),\n                                    disabled: !newResponse.trim(),\n                                    className: \"w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                            lineNumber: 678,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        \"Send Response\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                    lineNumber: 673,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                            lineNumber: 665,\n                            columnNumber: 15\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                    lineNumber: 626,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                lineNumber: 620,\n                columnNumber: 9\n            }, undefined),\n            selectedFAQ && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Modal, {\n                isOpen: !!selectedFAQ,\n                onClose: ()=>setSelectedFAQ(null),\n                title: selectedFAQ.question,\n                size: \"xl\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-3 pb-4 border-b border-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 bg-solar-100 rounded-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(selectedFAQ.icon, {\n                                        className: \"h-6 w-6 text-solar-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                        lineNumber: 698,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                    lineNumber: 697,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium text-solar-600 bg-solar-50 px-3 py-1 rounded-full\",\n                                        children: selectedFAQ.category\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                        lineNumber: 701,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                    lineNumber: 700,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                            lineNumber: 696,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"prose prose-sm max-w-none\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"whitespace-pre-line text-gray-700 leading-relaxed\",\n                                children: selectedFAQ.answer\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                lineNumber: 708,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                            lineNumber: 707,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"pt-4 border-t border-gray-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium text-gray-700 mr-2\",\n                                        children: \"Tags:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                        lineNumber: 715,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    selectedFAQ.tags.map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-solar-600 bg-solar-50 px-3 py-1 rounded-full\",\n                                            children: tag\n                                        }, tag, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                            lineNumber: 717,\n                                            columnNumber: 19\n                                        }, undefined))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                lineNumber: 714,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                            lineNumber: 713,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"pt-4 border-t border-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600 mb-3\",\n                                    children: \"Still need help? Create a support ticket for personalized assistance.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                    lineNumber: 725,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: ()=>{\n                                        setSelectedFAQ(null);\n                                        setShowNewTicketModal(true);\n                                    },\n                                    className: \"w-full sm:w-auto\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                            lineNumber: 735,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        \"Create Support Ticket\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                    lineNumber: 728,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                            lineNumber: 724,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                    lineNumber: 695,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                lineNumber: 689,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n        lineNumber: 394,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SupportCenter, \"vrB8hUSPY7c0kHi45cPRLsly9qg=\");\n_c = SupportCenter;\nvar _c;\n$RefreshReg$(_c, \"SupportCenter\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/SupportCenter.tsx\n"));

/***/ })

});