"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./src/components/admin/AdminLayout.tsx":
/*!**********************************************!*\
  !*** ./src/components/admin/AdminLayout.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AdminLayout: () => (/* binding */ AdminLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useAuth */ \"(app-pages-browser)/./src/hooks/useAuth.tsx\");\n/* harmony import */ var _components_layout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/layout */ \"(app-pages-browser)/./src/components/layout/index.ts\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _components_icons__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/icons */ \"(app-pages-browser)/./src/components/icons/index.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowUpDown_Bell_ChevronDown_CreditCard_DollarSign_FileText_LayoutDashboard_LogOut_Mail_Menu_MessageCircle_Settings_Shield_TrendingUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowUpDown,Bell,ChevronDown,CreditCard,DollarSign,FileText,LayoutDashboard,LogOut,Mail,Menu,MessageCircle,Settings,Shield,TrendingUp,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/layout-dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowUpDown_Bell_ChevronDown_CreditCard_DollarSign_FileText_LayoutDashboard_LogOut_Mail_Menu_MessageCircle_Settings_Shield_TrendingUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowUpDown,Bell,ChevronDown,CreditCard,DollarSign,FileText,LayoutDashboard,LogOut,Mail,Menu,MessageCircle,Settings,Shield,TrendingUp,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowUpDown_Bell_ChevronDown_CreditCard_DollarSign_FileText_LayoutDashboard_LogOut_Mail_Menu_MessageCircle_Settings_Shield_TrendingUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowUpDown,Bell,ChevronDown,CreditCard,DollarSign,FileText,LayoutDashboard,LogOut,Mail,Menu,MessageCircle,Settings,Shield,TrendingUp,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowUpDown_Bell_ChevronDown_CreditCard_DollarSign_FileText_LayoutDashboard_LogOut_Mail_Menu_MessageCircle_Settings_Shield_TrendingUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowUpDown,Bell,ChevronDown,CreditCard,DollarSign,FileText,LayoutDashboard,LogOut,Mail,Menu,MessageCircle,Settings,Shield,TrendingUp,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowUpDown_Bell_ChevronDown_CreditCard_DollarSign_FileText_LayoutDashboard_LogOut_Mail_Menu_MessageCircle_Settings_Shield_TrendingUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowUpDown,Bell,ChevronDown,CreditCard,DollarSign,FileText,LayoutDashboard,LogOut,Mail,Menu,MessageCircle,Settings,Shield,TrendingUp,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowUpDown_Bell_ChevronDown_CreditCard_DollarSign_FileText_LayoutDashboard_LogOut_Mail_Menu_MessageCircle_Settings_Shield_TrendingUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowUpDown,Bell,ChevronDown,CreditCard,DollarSign,FileText,LayoutDashboard,LogOut,Mail,Menu,MessageCircle,Settings,Shield,TrendingUp,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowUpDown_Bell_ChevronDown_CreditCard_DollarSign_FileText_LayoutDashboard_LogOut_Mail_Menu_MessageCircle_Settings_Shield_TrendingUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowUpDown,Bell,ChevronDown,CreditCard,DollarSign,FileText,LayoutDashboard,LogOut,Mail,Menu,MessageCircle,Settings,Shield,TrendingUp,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-up-down.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowUpDown_Bell_ChevronDown_CreditCard_DollarSign_FileText_LayoutDashboard_LogOut_Mail_Menu_MessageCircle_Settings_Shield_TrendingUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowUpDown,Bell,ChevronDown,CreditCard,DollarSign,FileText,LayoutDashboard,LogOut,Mail,Menu,MessageCircle,Settings,Shield,TrendingUp,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowUpDown_Bell_ChevronDown_CreditCard_DollarSign_FileText_LayoutDashboard_LogOut_Mail_Menu_MessageCircle_Settings_Shield_TrendingUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowUpDown,Bell,ChevronDown,CreditCard,DollarSign,FileText,LayoutDashboard,LogOut,Mail,Menu,MessageCircle,Settings,Shield,TrendingUp,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowUpDown_Bell_ChevronDown_CreditCard_DollarSign_FileText_LayoutDashboard_LogOut_Mail_Menu_MessageCircle_Settings_Shield_TrendingUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowUpDown,Bell,ChevronDown,CreditCard,DollarSign,FileText,LayoutDashboard,LogOut,Mail,Menu,MessageCircle,Settings,Shield,TrendingUp,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowUpDown_Bell_ChevronDown_CreditCard_DollarSign_FileText_LayoutDashboard_LogOut_Mail_Menu_MessageCircle_Settings_Shield_TrendingUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowUpDown,Bell,ChevronDown,CreditCard,DollarSign,FileText,LayoutDashboard,LogOut,Mail,Menu,MessageCircle,Settings,Shield,TrendingUp,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowUpDown_Bell_ChevronDown_CreditCard_DollarSign_FileText_LayoutDashboard_LogOut_Mail_Menu_MessageCircle_Settings_Shield_TrendingUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowUpDown,Bell,ChevronDown,CreditCard,DollarSign,FileText,LayoutDashboard,LogOut,Mail,Menu,MessageCircle,Settings,Shield,TrendingUp,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowUpDown_Bell_ChevronDown_CreditCard_DollarSign_FileText_LayoutDashboard_LogOut_Mail_Menu_MessageCircle_Settings_Shield_TrendingUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowUpDown,Bell,ChevronDown,CreditCard,DollarSign,FileText,LayoutDashboard,LogOut,Mail,Menu,MessageCircle,Settings,Shield,TrendingUp,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowUpDown_Bell_ChevronDown_CreditCard_DollarSign_FileText_LayoutDashboard_LogOut_Mail_Menu_MessageCircle_Settings_Shield_TrendingUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowUpDown,Bell,ChevronDown,CreditCard,DollarSign,FileText,LayoutDashboard,LogOut,Mail,Menu,MessageCircle,Settings,Shield,TrendingUp,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowUpDown_Bell_ChevronDown_CreditCard_DollarSign_FileText_LayoutDashboard_LogOut_Mail_Menu_MessageCircle_Settings_Shield_TrendingUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowUpDown,Bell,ChevronDown,CreditCard,DollarSign,FileText,LayoutDashboard,LogOut,Mail,Menu,MessageCircle,Settings,Shield,TrendingUp,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowUpDown_Bell_ChevronDown_CreditCard_DollarSign_FileText_LayoutDashboard_LogOut_Mail_Menu_MessageCircle_Settings_Shield_TrendingUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowUpDown,Bell,ChevronDown,CreditCard,DollarSign,FileText,LayoutDashboard,LogOut,Mail,Menu,MessageCircle,Settings,Shield,TrendingUp,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowUpDown_Bell_ChevronDown_CreditCard_DollarSign_FileText_LayoutDashboard_LogOut_Mail_Menu_MessageCircle_Settings_Shield_TrendingUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowUpDown,Bell,ChevronDown,CreditCard,DollarSign,FileText,LayoutDashboard,LogOut,Mail,Menu,MessageCircle,Settings,Shield,TrendingUp,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowUpDown_Bell_ChevronDown_CreditCard_DollarSign_FileText_LayoutDashboard_LogOut_Mail_Menu_MessageCircle_Settings_Shield_TrendingUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowUpDown,Bell,ChevronDown,CreditCard,DollarSign,FileText,LayoutDashboard,LogOut,Mail,Menu,MessageCircle,Settings,Shield,TrendingUp,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* __next_internal_client_entry_do_not_use__ AdminLayout auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst AdminLayout = (param)=>{\n    let { children, activeTab, onTabChange } = param;\n    var _navigationItems_find, _user_firstName, _user_firstName1;\n    _s();\n    const { user, logout } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [userDropdownOpen, setUserDropdownOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const dropdownRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Admin navigation items\n    const navigationItems = [\n        {\n            id: 'dashboard',\n            label: 'Dashboard',\n            icon: _barrel_optimize_names_ArrowLeft_ArrowUpDown_Bell_ChevronDown_CreditCard_DollarSign_FileText_LayoutDashboard_LogOut_Mail_Menu_MessageCircle_Settings_Shield_TrendingUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n        },\n        {\n            id: 'users',\n            label: 'User Management',\n            icon: _barrel_optimize_names_ArrowLeft_ArrowUpDown_Bell_ChevronDown_CreditCard_DollarSign_FileText_LayoutDashboard_LogOut_Mail_Menu_MessageCircle_Settings_Shield_TrendingUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n        },\n        {\n            id: 'kyc',\n            label: 'KYC Review',\n            icon: _barrel_optimize_names_ArrowLeft_ArrowUpDown_Bell_ChevronDown_CreditCard_DollarSign_FileText_LayoutDashboard_LogOut_Mail_Menu_MessageCircle_Settings_Shield_TrendingUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n        },\n        {\n            id: 'deposits',\n            label: 'Deposits',\n            icon: _barrel_optimize_names_ArrowLeft_ArrowUpDown_Bell_ChevronDown_CreditCard_DollarSign_FileText_LayoutDashboard_LogOut_Mail_Menu_MessageCircle_Settings_Shield_TrendingUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n        },\n        {\n            id: 'withdrawals',\n            label: 'Withdrawals',\n            icon: _barrel_optimize_names_ArrowLeft_ArrowUpDown_Bell_ChevronDown_CreditCard_DollarSign_FileText_LayoutDashboard_LogOut_Mail_Menu_MessageCircle_Settings_Shield_TrendingUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n        },\n        {\n            id: 'mining-management',\n            label: 'Mining Management',\n            icon: Zap\n        },\n        {\n            id: 'support',\n            label: 'Support Tickets',\n            icon: _barrel_optimize_names_ArrowLeft_ArrowUpDown_Bell_ChevronDown_CreditCard_DollarSign_FileText_LayoutDashboard_LogOut_Mail_Menu_MessageCircle_Settings_Shield_TrendingUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n        },\n        {\n            id: 'binary-points',\n            label: 'Binary Points',\n            icon: _barrel_optimize_names_ArrowLeft_ArrowUpDown_Bell_ChevronDown_CreditCard_DollarSign_FileText_LayoutDashboard_LogOut_Mail_Menu_MessageCircle_Settings_Shield_TrendingUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n        },\n        {\n            id: 'referral-commissions',\n            label: 'Referral Commissions',\n            icon: _barrel_optimize_names_ArrowLeft_ArrowUpDown_Bell_ChevronDown_CreditCard_DollarSign_FileText_LayoutDashboard_LogOut_Mail_Menu_MessageCircle_Settings_Shield_TrendingUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"]\n        },\n        {\n            id: 'email-settings',\n            label: 'Email Settings',\n            icon: _barrel_optimize_names_ArrowLeft_ArrowUpDown_Bell_ChevronDown_CreditCard_DollarSign_FileText_LayoutDashboard_LogOut_Mail_Menu_MessageCircle_Settings_Shield_TrendingUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"]\n        },\n        {\n            id: 'settings',\n            label: 'System Settings',\n            icon: _barrel_optimize_names_ArrowLeft_ArrowUpDown_Bell_ChevronDown_CreditCard_DollarSign_FileText_LayoutDashboard_LogOut_Mail_Menu_MessageCircle_Settings_Shield_TrendingUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"]\n        },\n        {\n            id: 'logs',\n            label: 'System Logs',\n            icon: _barrel_optimize_names_ArrowLeft_ArrowUpDown_Bell_ChevronDown_CreditCard_DollarSign_FileText_LayoutDashboard_LogOut_Mail_Menu_MessageCircle_Settings_Shield_TrendingUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"]\n        }\n    ];\n    const handleLogout = async ()=>{\n        await logout();\n    };\n    // Close dropdown when clicking outside\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminLayout.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"AdminLayout.useEffect.handleClickOutside\": (event)=>{\n                    if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n                        setUserDropdownOpen(false);\n                    }\n                }\n            }[\"AdminLayout.useEffect.handleClickOutside\"];\n            document.addEventListener('mousedown', handleClickOutside);\n            return ({\n                \"AdminLayout.useEffect\": ()=>{\n                    document.removeEventListener('mousedown', handleClickOutside);\n                }\n            })[\"AdminLayout.useEffect\"];\n        }\n    }[\"AdminLayout.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-slate-900 flex admin-panel\",\n        \"data-admin-panel\": \"true\",\n        children: [\n            sidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-40 bg-black bg-opacity-75 lg:hidden\",\n                onClick: ()=>setSidebarOpen(false)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                lineNumber: 85,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                className: \"\\n        fixed inset-y-0 left-0 z-50 w-64 bg-slate-800 shadow-xl border-r border-slate-700\\n        transform transition-all duration-300 ease-in-out\\n        \".concat(sidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0', \"\\n      \"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col h-screen\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between h-14 px-5 border-b border-slate-700 bg-slate-800 flex-shrink-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/\",\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons__WEBPACK_IMPORTED_MODULE_6__.SolarPanel, {\n                                                className: \"h-5 w-5 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                                lineNumber: 102,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                            lineNumber: 101,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-lg font-bold text-white\",\n                                            children: \"HashCoreX\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                            lineNumber: 104,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                    lineNumber: 100,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setSidebarOpen(false),\n                                    className: \"lg:hidden p-1.5 rounded-lg text-slate-400\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowUpDown_Bell_ChevronDown_CreditCard_DollarSign_FileText_LayoutDashboard_LogOut_Mail_Menu_MessageCircle_Settings_Shield_TrendingUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-3 py-3 bg-red-600 border-b border-slate-700\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 text-white\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowUpDown_Bell_ChevronDown_CreditCard_DollarSign_FileText_LayoutDashboard_LogOut_Mail_Menu_MessageCircle_Settings_Shield_TrendingUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-semibold\",\n                                        children: \"Admin Panel\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                            lineNumber: 115,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"flex-1 px-3 py-4 space-y-1 min-h-0\",\n                            children: navigationItems.map((item)=>{\n                                const Icon = item.icon;\n                                const isActive = activeTab === item.id;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        onTabChange(item.id);\n                                        setSidebarOpen(false);\n                                    },\n                                    className: \"\\n                    w-full flex items-center space-x-3 px-3 py-2.5 rounded-lg text-left group\\n                    \".concat(isActive ? 'bg-blue-600 text-white shadow-md' : 'text-slate-300', \"\\n                  \"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                            className: \"h-4 w-4 \".concat(isActive ? 'text-white' : 'text-slate-400')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                            lineNumber: 143,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium text-sm\",\n                                            children: item.label\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                            lineNumber: 144,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, item.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                    lineNumber: 129,\n                                    columnNumber: 17\n                                }, undefined);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-3 py-3 border-t border-slate-700\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/dashboard\",\n                                className: \"w-full flex items-center space-x-3 px-3 py-2.5 rounded-lg text-slate-300 group\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowUpDown_Bell_ChevronDown_CreditCard_DollarSign_FileText_LayoutDashboard_LogOut_Mail_Menu_MessageCircle_Settings_Shield_TrendingUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                        lineNumber: 156,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium text-sm\",\n                                        children: \"Back to Dashboard\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                        lineNumber: 157,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                            lineNumber: 151,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-3 py-3 border-t border-slate-700 bg-slate-900 flex-shrink-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleLogout,\n                                className: \"w-full flex items-center space-x-3 px-3 py-2.5 rounded-lg text-slate-300 group\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowUpDown_Bell_ChevronDown_CreditCard_DollarSign_FileText_LayoutDashboard_LogOut_Mail_Menu_MessageCircle_Settings_Shield_TrendingUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium text-sm\",\n                                        children: \"Logout\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                            lineNumber: 162,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                    lineNumber: 97,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                lineNumber: 92,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col min-w-0 lg:ml-64\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"bg-slate-800 shadow-sm border-b border-slate-700 sticky top-0 z-30\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-4 sm:px-6 lg:px-8 xl:px-12\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout__WEBPACK_IMPORTED_MODULE_4__.Flex, {\n                                justify: \"between\",\n                                align: \"center\",\n                                className: \"h-16\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setSidebarOpen(true),\n                                                className: \"lg:hidden p-2 rounded-lg text-slate-400\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowUpDown_Bell_ChevronDown_CreditCard_DollarSign_FileText_LayoutDashboard_LogOut_Mail_Menu_MessageCircle_Settings_Shield_TrendingUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    className: \"h-6 w-6\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                                    lineNumber: 185,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                                lineNumber: 181,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-2xl font-bold text-white capitalize tracking-wide drop-shadow-lg\",\n                                                        children: ((_navigationItems_find = navigationItems.find((item)=>item.id === activeTab)) === null || _navigationItems_find === void 0 ? void 0 : _navigationItems_find.label) || 'Admin Dashboard'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                                        lineNumber: 188,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-slate-300 hidden sm:block font-medium\",\n                                                        children: \"Manage platform operations and user activities\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                                        lineNumber: 191,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"relative p-2 rounded-lg text-slate-400\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowUpDown_Bell_ChevronDown_CreditCard_DollarSign_FileText_LayoutDashboard_LogOut_Mail_Menu_MessageCircle_Settings_Shield_TrendingUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                                        lineNumber: 200,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"absolute top-1 right-1 h-2 w-2 bg-orange-500 rounded-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                                        lineNumber: 201,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                                lineNumber: 199,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"px-3 py-1.5 rounded-lg text-xs font-semibold border bg-red-600 text-white border-red-500\",\n                                                children: \"ADMIN\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                                lineNumber: 205,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                ref: dropdownRef,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setUserDropdownOpen(!userDropdownOpen),\n                                                        className: \"flex items-center space-x-2 p-1 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_5__.ProfileImage, {\n                                                                src: user === null || user === void 0 ? void 0 : user.profilePicture,\n                                                                alt: \"Profile\",\n                                                                size: 32,\n                                                                className: \"rounded-lg\",\n                                                                fallbackText: (user === null || user === void 0 ? void 0 : (_user_firstName = user.firstName) === null || _user_firstName === void 0 ? void 0 : _user_firstName.charAt(0).toUpperCase()) || (user === null || user === void 0 ? void 0 : user.email.charAt(0).toUpperCase()),\n                                                                fallbackBgColor: \"bg-orange-600\",\n                                                                loading: \"lazy\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                                                lineNumber: 215,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowUpDown_Bell_ChevronDown_CreditCard_DollarSign_FileText_LayoutDashboard_LogOut_Mail_Menu_MessageCircle_Settings_Shield_TrendingUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                className: \"h-4 w-4 text-slate-400 transition-transform \".concat(userDropdownOpen ? 'rotate-180' : '')\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                                                lineNumber: 224,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                                        lineNumber: 211,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    userDropdownOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute right-0 mt-2 w-64 bg-slate-800 rounded-xl shadow-lg border border-slate-700 py-2 z-50\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"px-4 py-3 border-b border-slate-700\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_5__.ProfileImage, {\n                                                                            src: user === null || user === void 0 ? void 0 : user.profilePicture,\n                                                                            alt: \"Profile\",\n                                                                            size: 40,\n                                                                            className: \"rounded-lg\",\n                                                                            fallbackText: (user === null || user === void 0 ? void 0 : (_user_firstName1 = user.firstName) === null || _user_firstName1 === void 0 ? void 0 : _user_firstName1.charAt(0).toUpperCase()) || (user === null || user === void 0 ? void 0 : user.email.charAt(0).toUpperCase()),\n                                                                            fallbackBgColor: \"bg-orange-600\",\n                                                                            loading: \"lazy\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                                                            lineNumber: 233,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex-1 min-w-0\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm font-semibold text-white truncate\",\n                                                                                    children: (user === null || user === void 0 ? void 0 : user.firstName) && (user === null || user === void 0 ? void 0 : user.lastName) ? \"\".concat(user.firstName, \" \").concat(user.lastName) : user === null || user === void 0 ? void 0 : user.email.split('@')[0]\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                                                                    lineNumber: 243,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-xs text-slate-400\",\n                                                                                    children: [\n                                                                                        \"ID: \",\n                                                                                        user === null || user === void 0 ? void 0 : user.referralId\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                                                                    lineNumber: 249,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-xs text-red-400 font-medium\",\n                                                                                    children: \"Administrator\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                                                                    lineNumber: 252,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                                                            lineNumber: 242,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                                                    lineNumber: 232,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                                                lineNumber: 231,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"py-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                        href: \"/dashboard\",\n                                                                        onClick: ()=>setUserDropdownOpen(false),\n                                                                        className: \"w-full flex items-center space-x-3 px-4 py-2 text-sm text-slate-300\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowUpDown_Bell_ChevronDown_CreditCard_DollarSign_FileText_LayoutDashboard_LogOut_Mail_Menu_MessageCircle_Settings_Shield_TrendingUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                                                                lineNumber: 266,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"User Dashboard\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                                                                lineNumber: 267,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                                                        lineNumber: 261,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"border-t border-slate-700 my-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                                                        lineNumber: 269,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>{\n                                                                            setUserDropdownOpen(false);\n                                                                            handleLogout();\n                                                                        },\n                                                                        className: \"w-full flex items-center space-x-3 px-4 py-2 text-sm text-red-400\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowUpDown_Bell_ChevronDown_CreditCard_DollarSign_FileText_LayoutDashboard_LogOut_Mail_Menu_MessageCircle_Settings_Shield_TrendingUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                                                                lineNumber: 277,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"Sign Out\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                                                                lineNumber: 278,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                                                        lineNumber: 270,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                                                lineNumber: 260,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                                        lineNumber: 229,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                                lineNumber: 210,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                            lineNumber: 178,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                        lineNumber: 177,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 bg-slate-900 overflow-y-auto relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"fixed inset-0 bg-cover bg-center bg-no-repeat opacity-10 pointer-events-none\",\n                                style: {\n                                    backgroundImage: 'url(/admin_background.jpg)',\n                                    backgroundAttachment: 'fixed',\n                                    zIndex: 0\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                lineNumber: 292,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative z-10 px-4 sm:px-6 lg:px-8 xl:px-12 py-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-w-7xl mx-auto\",\n                                    children: children\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                    lineNumber: 302,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                lineNumber: 301,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                        lineNumber: 290,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                lineNumber: 175,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n        lineNumber: 82,\n        columnNumber: 5\n    }, undefined);\n};\n_s(AdminLayout, \"DxOEgSB4GnaNtXD2PifQU3JJyCQ=\", false, function() {\n    return [\n        _hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__.useAuth\n    ];\n});\n_c = AdminLayout;\nvar _c;\n$RefreshReg$(_c, \"AdminLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/AdminLayout.tsx\n"));

/***/ })

});