'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardHeader, CardTitle, CardContent, Button, Input } from '@/components/ui';
import {
  Zap,
  Users,
  DollarSign,
  TrendingUp,
  Clock,
  Search,
  Filter,
  Eye,
  Calendar,
  Activity,
  BarChart3,
  RefreshCw
} from 'lucide-react';
import { formatCurrency, formatDateTime, formatTHS } from '@/lib/utils';

interface MiningUnit {
  id: string;
  userId: string;
  user: {
    firstName: string;
    lastName: string;
    email: string;
    referralId: string;
  };
  thsAmount: number;
  investmentAmount: number;
  totalEarned: number;
  dailyROI: number;
  status: 'ACTIVE' | 'EXPIRED';
  purchaseDate: string;
  expiryDate: string;
  progressPercentage: number;
}

interface MiningStats {
  totalActiveUnits: number;
  totalActiveTHS: number;
  totalInvestment: number;
  totalEarningsDistributed: number;
  averageDailyROI: number;
  activeUsers: number;
}

interface MiningManagementProps {}

export const MiningManagement: React.FC<MiningManagementProps> = () => {
  const [miningUnits, setMiningUnits] = useState<MiningUnit[]>([]);
  const [stats, setStats] = useState<MiningStats>({
    totalActiveUnits: 0,
    totalActiveTHS: 0,
    totalInvestment: 0,
    totalEarningsDistributed: 0,
    averageDailyROI: 0,
    activeUsers: 0,
  });
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'ALL' | 'ACTIVE' | 'EXPIRED'>('ALL');
  const [selectedUnit, setSelectedUnit] = useState<MiningUnit | null>(null);

  useEffect(() => {
    fetchMiningData();
  }, [searchTerm, statusFilter]);

  const fetchMiningData = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams();
      if (searchTerm) params.append('search', searchTerm);
      if (statusFilter !== 'ALL') params.append('status', statusFilter);
      
      const response = await fetch(`/api/admin/mining?${params.toString()}`, {
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        setMiningUnits(data.units || []);
        setStats(data.stats || stats);
      }
    } catch (error) {
      console.error('Failed to fetch mining data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'EXPIRED':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getProgressColor = (percentage: number) => {
    if (percentage >= 80) return 'bg-red-500';
    if (percentage >= 60) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="h-8 w-8 animate-spin text-blue-500" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-white">Mining Management</h2>
          <p className="text-slate-400 mt-1">Monitor and manage all mining units across the platform</p>
        </div>
        <Button
          onClick={fetchMiningData}
          className="flex items-center gap-2"
        >
          <RefreshCw className="h-4 w-4" />
          Refresh
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
        <Card className="bg-slate-800 border-slate-700">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-slate-400 text-sm">Active Units</p>
                <p className="text-2xl font-bold text-white">{stats.totalActiveUnits}</p>
              </div>
              <Activity className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-slate-800 border-slate-700">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-slate-400 text-sm">Total TH/s</p>
                <p className="text-2xl font-bold text-white">{formatTHS(stats.totalActiveTHS)}</p>
              </div>
              <Zap className="h-8 w-8 text-yellow-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-slate-800 border-slate-700">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-slate-400 text-sm">Total Investment</p>
                <p className="text-2xl font-bold text-white">{formatCurrency(stats.totalInvestment)}</p>
              </div>
              <DollarSign className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-slate-800 border-slate-700">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-slate-400 text-sm">Earnings Paid</p>
                <p className="text-2xl font-bold text-white">{formatCurrency(stats.totalEarningsDistributed)}</p>
              </div>
              <TrendingUp className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-slate-800 border-slate-700">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-slate-400 text-sm">Avg Daily ROI</p>
                <p className="text-2xl font-bold text-white">{stats.averageDailyROI.toFixed(2)}%</p>
              </div>
              <BarChart3 className="h-8 w-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-slate-800 border-slate-700">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-slate-400 text-sm">Active Users</p>
                <p className="text-2xl font-bold text-white">{stats.activeUsers}</p>
              </div>
              <Users className="h-8 w-8 text-indigo-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card className="bg-slate-800 border-slate-700">
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4" />
                <Input
                  placeholder="Search by user name, email, or referral ID..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400"
                />
              </div>
            </div>
            <div className="sm:w-48">
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value as 'ALL' | 'ACTIVE' | 'EXPIRED')}
                className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="ALL">All Status</option>
                <option value="ACTIVE">Active</option>
                <option value="EXPIRED">Expired</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Mining Units Table */}
      <Card className="bg-slate-800 border-slate-700">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <Zap className="h-5 w-5" />
            Mining Units ({miningUnits.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {miningUnits.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="w-full text-sm">
                <thead>
                  <tr className="border-b border-slate-700">
                    <th className="text-left py-3 px-4 text-slate-300 font-medium">User</th>
                    <th className="text-left py-3 px-4 text-slate-300 font-medium">TH/s</th>
                    <th className="text-left py-3 px-4 text-slate-300 font-medium">Investment</th>
                    <th className="text-left py-3 px-4 text-slate-300 font-medium">Earned</th>
                    <th className="text-left py-3 px-4 text-slate-300 font-medium">Daily ROI</th>
                    <th className="text-left py-3 px-4 text-slate-300 font-medium">Progress</th>
                    <th className="text-left py-3 px-4 text-slate-300 font-medium">Status</th>
                    <th className="text-left py-3 px-4 text-slate-300 font-medium">Purchase Date</th>
                    <th className="text-left py-3 px-4 text-slate-300 font-medium">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {miningUnits.map((unit) => (
                    <tr key={unit.id} className="border-b border-slate-700 hover:bg-slate-700/50">
                      <td className="py-3 px-4">
                        <div>
                          <div className="text-white font-medium">
                            {unit.user.firstName} {unit.user.lastName}
                          </div>
                          <div className="text-slate-400 text-xs">{unit.user.email}</div>
                          <div className="text-slate-400 text-xs">ID: {unit.user.referralId}</div>
                        </div>
                      </td>
                      <td className="py-3 px-4 text-white font-mono">{formatTHS(unit.thsAmount)}</td>
                      <td className="py-3 px-4 text-white">{formatCurrency(unit.investmentAmount)}</td>
                      <td className="py-3 px-4 text-green-400">{formatCurrency(unit.totalEarned)}</td>
                      <td className="py-3 px-4 text-white">{unit.dailyROI.toFixed(2)}%</td>
                      <td className="py-3 px-4">
                        <div className="flex items-center gap-2">
                          <div className="flex-1 bg-slate-700 rounded-full h-2">
                            <div
                              className={`h-2 rounded-full ${getProgressColor(unit.progressPercentage)}`}
                              style={{ width: `${Math.min(unit.progressPercentage, 100)}%` }}
                            />
                          </div>
                          <span className="text-xs text-slate-400 w-12">
                            {unit.progressPercentage.toFixed(0)}%
                          </span>
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(unit.status)}`}>
                          {unit.status}
                        </span>
                      </td>
                      <td className="py-3 px-4 text-slate-300 text-xs">
                        {formatDateTime(unit.purchaseDate)}
                      </td>
                      <td className="py-3 px-4">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setSelectedUnit(unit)}
                          className="border-slate-600 text-slate-300 hover:bg-slate-700 hover:text-white"
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="text-center py-12">
              <Zap className="h-12 w-12 text-slate-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-white mb-2">No Mining Units Found</h3>
              <p className="text-slate-400">
                {searchTerm || statusFilter !== 'ALL'
                  ? 'Try adjusting your search or filter criteria.'
                  : 'No mining units have been purchased yet.'}
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
