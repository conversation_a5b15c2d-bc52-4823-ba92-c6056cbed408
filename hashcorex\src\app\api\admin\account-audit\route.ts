import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest, isAdmin } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { ErrorLogger } from '@/lib/errorLogger';

// GET - Fetch account audit data
export async function GET(request: NextRequest) {
  try {
    const { authenticated, user } = await authenticateRequest(request);

    if (!authenticated || !user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    // Check if user is admin
    const userIsAdmin = await isAdmin(user.id);
    if (!userIsAdmin) {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      );
    }

    const url = new URL(request.url);
    const searchTerm = url.searchParams.get('search') || '';
    const discrepanciesOnly = url.searchParams.get('discrepancies') === 'true';
    const startDate = url.searchParams.get('startDate');
    const endDate = url.searchParams.get('endDate');

    // Build date filter
    const dateFilter: any = {};
    if (startDate) {
      dateFilter.gte = new Date(startDate);
    }
    if (endDate) {
      const endDateTime = new Date(endDate);
      endDateTime.setHours(23, 59, 59, 999);
      dateFilter.lte = endDateTime;
    }

    // Get all users with their transaction data
    const users = await prisma.user.findMany({
      where: {
        AND: [
          searchTerm ? {
            OR: [
              { firstName: { contains: searchTerm, mode: 'insensitive' } },
              { lastName: { contains: searchTerm, mode: 'insensitive' } },
              { email: { contains: searchTerm, mode: 'insensitive' } },
              { referralId: { contains: searchTerm, mode: 'insensitive' } },
            ]
          } : {},
        ]
      },
      include: {
        transactions: {
          where: Object.keys(dateFilter).length > 0 ? {
            createdAt: dateFilter
          } : undefined,
          orderBy: { createdAt: 'desc' }
        },
        walletBalance: true,
      },
      orderBy: { createdAt: 'desc' }
    });

    // Calculate audit data for each user
    const auditData = users.map(user => {
      const transactions = user.transactions;

      // Calculate totals from transactions
      const deposits = transactions
        .filter(t => t.type === 'DEPOSIT' && t.status === 'COMPLETED')
        .reduce((sum, t) => sum + t.amount, 0);

      const withdrawals = transactions
        .filter(t => t.type === 'WITHDRAWAL' && t.status === 'COMPLETED')
        .reduce((sum, t) => sum + t.amount, 0);

      const earnings = transactions
        .filter(t => ['MINING_EARNINGS', 'DIRECT_REFERRAL', 'BINARY_BONUS'].includes(t.type) && t.status === 'COMPLETED')
        .reduce((sum, t) => sum + t.amount, 0);

      const purchases = transactions
        .filter(t => t.type === 'PURCHASE' && t.status === 'COMPLETED')
        .reduce((sum, t) => sum + t.amount, 0);

      // Calculate admin adjustments (ADMIN_CREDIT adds, ADMIN_DEBIT subtracts)
      const adminCredits = transactions
        .filter(t => t.type === 'ADMIN_CREDIT' && t.status === 'COMPLETED')
        .reduce((sum, t) => sum + t.amount, 0);

      const adminDebits = transactions
        .filter(t => t.type === 'ADMIN_DEBIT' && t.status === 'COMPLETED')
        .reduce((sum, t) => sum + Math.abs(t.amount), 0); // ADMIN_DEBIT amounts are stored as negative

      // Calculate what the balance should be (including admin adjustments)
      const calculatedBalance = deposits + earnings + adminCredits - withdrawals - purchases - adminDebits;
      
      // Get current balance from wallet
      const currentBalance = user.walletBalance?.availableBalance || 0;
      
      // Calculate mismatch
      const balanceMismatch = currentBalance - calculatedBalance;
      
      // Determine if there are discrepancies (threshold of $0.01)
      const hasDiscrepancies = Math.abs(balanceMismatch) >= 0.01;
      
      // Get last activity
      const lastActivity = transactions.length > 0 
        ? transactions[0].createdAt.toISOString()
        : user.createdAt.toISOString();

      return {
        userId: user.id,
        user: {
          firstName: user.firstName,
          lastName: user.lastName,
          email: user.email,
          referralId: user.referralId,
        },
        totalDeposits: deposits,
        totalWithdrawals: withdrawals,
        totalEarnings: earnings,
        totalPurchases: purchases,
        currentBalance,
        calculatedBalance,
        balanceMismatch,
        transactionCount: transactions.length,
        lastActivity,
        hasDiscrepancies,
      };
    });

    // Filter by discrepancies if requested
    const filteredData = discrepanciesOnly 
      ? auditData.filter(user => user.hasDiscrepancies)
      : auditData;

    // Calculate statistics
    const stats = {
      totalUsers: users.length,
      usersWithDiscrepancies: auditData.filter(user => user.hasDiscrepancies).length,
      totalBalanceMismatch: auditData.reduce((sum, user) => sum + user.balanceMismatch, 0),
      totalTransactions: auditData.reduce((sum, user) => sum + user.transactionCount, 0),
      auditedUsers: auditData.length,
    };

    return NextResponse.json({
      success: true,
      data: {
        users: filteredData,
        stats,
      },
    });

  } catch (error: any) {
    console.error('Account audit API error:', error);
    
    await ErrorLogger.logApiError(
      request,
      error as Error,
      'ACCOUNT_AUDIT_ERROR'
    );

    return NextResponse.json(
      { success: false, error: 'Failed to fetch audit data' },
      { status: 500 }
    );
  }
}
