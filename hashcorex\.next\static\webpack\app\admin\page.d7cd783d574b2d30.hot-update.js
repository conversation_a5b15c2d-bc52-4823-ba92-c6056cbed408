"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./src/app/admin/page.tsx":
/*!********************************!*\
  !*** ./src/app/admin/page.tsx ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdminPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useAuth */ \"(app-pages-browser)/./src/hooks/useAuth.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_admin_AdminLayout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/admin/AdminLayout */ \"(app-pages-browser)/./src/components/admin/AdminLayout.tsx\");\n/* harmony import */ var _components_admin_AdminDashboard__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/admin/AdminDashboard */ \"(app-pages-browser)/./src/components/admin/AdminDashboard.tsx\");\n/* harmony import */ var _components_admin_UserManagement__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/admin/UserManagement */ \"(app-pages-browser)/./src/components/admin/UserManagement.tsx\");\n/* harmony import */ var _components_admin_KYCReview__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/admin/KYCReview */ \"(app-pages-browser)/./src/components/admin/KYCReview.tsx\");\n/* harmony import */ var _components_admin_DepositManagement__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/admin/DepositManagement */ \"(app-pages-browser)/./src/components/admin/DepositManagement.tsx\");\n/* harmony import */ var _components_admin_WithdrawalManagement__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/admin/WithdrawalManagement */ \"(app-pages-browser)/./src/components/admin/WithdrawalManagement.tsx\");\n/* harmony import */ var _components_admin_SupportTicketManagement__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/admin/SupportTicketManagement */ \"(app-pages-browser)/./src/components/admin/SupportTicketManagement.tsx\");\n/* harmony import */ var _components_admin_BinaryPointsManagement__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/admin/BinaryPointsManagement */ \"(app-pages-browser)/./src/components/admin/BinaryPointsManagement.tsx\");\n/* harmony import */ var _components_admin_ReferralCommissionTracking__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/admin/ReferralCommissionTracking */ \"(app-pages-browser)/./src/components/admin/ReferralCommissionTracking.tsx\");\n/* harmony import */ var _components_admin_SystemSettings__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/admin/SystemSettings */ \"(app-pages-browser)/./src/components/admin/SystemSettings.tsx\");\n/* harmony import */ var _components_admin_SystemLogs__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/admin/SystemLogs */ \"(app-pages-browser)/./src/components/admin/SystemLogs.tsx\");\n/* harmony import */ var _components_admin_EmailSettings__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/admin/EmailSettings */ \"(app-pages-browser)/./src/components/admin/EmailSettings.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction AdminPage() {\n    _s();\n    const { user, loading } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('dashboard');\n    const [isAdmin, setIsAdmin] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null); // null = not checked yet\n    const [checkingAdmin, setCheckingAdmin] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Check if user is admin\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminPage.useEffect\": ()=>{\n            const checkAdminStatus = {\n                \"AdminPage.useEffect.checkAdminStatus\": async ()=>{\n                    if (!loading && user) {\n                        try {\n                            const response = await fetch('/api/admin/check', {\n                                credentials: 'include'\n                            });\n                            if (response.ok) {\n                                const data = await response.json();\n                                setIsAdmin(data.isAdmin);\n                            } else {\n                                setIsAdmin(false);\n                            }\n                        } catch (error) {\n                            console.error('Error checking admin status:', error);\n                            setIsAdmin(false);\n                        } finally{\n                            setCheckingAdmin(false);\n                        }\n                    } else if (!loading && !user) {\n                        // If no user, stop checking admin status\n                        setCheckingAdmin(false);\n                        setIsAdmin(false);\n                    }\n                }\n            }[\"AdminPage.useEffect.checkAdminStatus\"];\n            // Only check admin status if we have a user or if loading is complete\n            if (!loading) {\n                checkAdminStatus();\n            }\n        }\n    }[\"AdminPage.useEffect\"], [\n        user,\n        loading\n    ]);\n    // Redirect if not authenticated\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminPage.useEffect\": ()=>{\n            if (!loading && !user) {\n                router.push('/login');\n            }\n        }\n    }[\"AdminPage.useEffect\"], [\n        user,\n        loading,\n        router\n    ]);\n    // Redirect if not admin (only after admin check is complete)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminPage.useEffect\": ()=>{\n            // Only redirect if admin check is complete and user is explicitly not admin\n            if (!checkingAdmin && !loading && user && isAdmin === false) {\n                router.push('/dashboard');\n            }\n        }\n    }[\"AdminPage.useEffect\"], [\n        isAdmin,\n        checkingAdmin,\n        user,\n        loading,\n        router\n    ]);\n    if (loading || checkingAdmin) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_16__.Loading, {\n                size: \"lg\",\n                text: \"Loading admin panel...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 80,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\admin\\\\page.tsx\",\n            lineNumber: 79,\n            columnNumber: 7\n        }, this);\n    }\n    if (!user || isAdmin === false) {\n        return null;\n    }\n    const renderTabContent = ()=>{\n        switch(activeTab){\n            case 'dashboard':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_AdminDashboard__WEBPACK_IMPORTED_MODULE_5__.AdminDashboard, {\n                    onTabChange: setActiveTab\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 92,\n                    columnNumber: 16\n                }, this);\n            case 'users':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_UserManagement__WEBPACK_IMPORTED_MODULE_6__.UserManagement, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 94,\n                    columnNumber: 16\n                }, this);\n            case 'kyc':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_KYCReview__WEBPACK_IMPORTED_MODULE_7__.KYCReview, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 96,\n                    columnNumber: 16\n                }, this);\n            case 'deposits':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_DepositManagement__WEBPACK_IMPORTED_MODULE_8__.DepositManagement, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 98,\n                    columnNumber: 16\n                }, this);\n            case 'withdrawals':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_WithdrawalManagement__WEBPACK_IMPORTED_MODULE_9__.WithdrawalManagement, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 100,\n                    columnNumber: 16\n                }, this);\n            case 'support':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_SupportTicketManagement__WEBPACK_IMPORTED_MODULE_10__.SupportTicketManagement, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 102,\n                    columnNumber: 16\n                }, this);\n            case 'binary-points':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_BinaryPointsManagement__WEBPACK_IMPORTED_MODULE_11__.BinaryPointsManagement, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 104,\n                    columnNumber: 16\n                }, this);\n            case 'referral-commissions':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_ReferralCommissionTracking__WEBPACK_IMPORTED_MODULE_12__.ReferralCommissionTracking, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 106,\n                    columnNumber: 16\n                }, this);\n            case 'email-settings':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_EmailSettings__WEBPACK_IMPORTED_MODULE_15__.EmailSettings, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 108,\n                    columnNumber: 16\n                }, this);\n            case 'settings':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_SystemSettings__WEBPACK_IMPORTED_MODULE_13__.SystemSettings, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 110,\n                    columnNumber: 16\n                }, this);\n            case 'logs':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_SystemLogs__WEBPACK_IMPORTED_MODULE_14__.SystemLogs, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 112,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_AdminDashboard__WEBPACK_IMPORTED_MODULE_5__.AdminDashboard, {\n                    onTabChange: setActiveTab\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 114,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_AdminLayout__WEBPACK_IMPORTED_MODULE_4__.AdminLayout, {\n        activeTab: activeTab,\n        onTabChange: setActiveTab,\n        children: renderTabContent()\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\admin\\\\page.tsx\",\n        lineNumber: 119,\n        columnNumber: 5\n    }, this);\n}\n_s(AdminPage, \"6UBIMFG/BRhOSUF/UBGBUya8Og0=\", false, function() {\n    return [\n        _hooks_useAuth__WEBPACK_IMPORTED_MODULE_2__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = AdminPage;\nvar _c;\n$RefreshReg$(_c, \"AdminPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/page.tsx\n"));

/***/ })

});