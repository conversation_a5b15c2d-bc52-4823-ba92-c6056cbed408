"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./src/components/admin/AdminLayout.tsx":
/*!**********************************************!*\
  !*** ./src/components/admin/AdminLayout.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AdminLayout: () => (/* binding */ AdminLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useAuth */ \"(app-pages-browser)/./src/hooks/useAuth.tsx\");\n/* harmony import */ var _components_layout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/layout */ \"(app-pages-browser)/./src/components/layout/index.ts\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _components_icons__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/icons */ \"(app-pages-browser)/./src/components/icons/index.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowUpDown_Bell_ChevronDown_CreditCard_DollarSign_FileText_LayoutDashboard_LogOut_Mail_Menu_MessageCircle_Settings_Shield_TrendingUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowUpDown,Bell,ChevronDown,CreditCard,DollarSign,FileText,LayoutDashboard,LogOut,Mail,Menu,MessageCircle,Settings,Shield,TrendingUp,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/layout-dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowUpDown_Bell_ChevronDown_CreditCard_DollarSign_FileText_LayoutDashboard_LogOut_Mail_Menu_MessageCircle_Settings_Shield_TrendingUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowUpDown,Bell,ChevronDown,CreditCard,DollarSign,FileText,LayoutDashboard,LogOut,Mail,Menu,MessageCircle,Settings,Shield,TrendingUp,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowUpDown_Bell_ChevronDown_CreditCard_DollarSign_FileText_LayoutDashboard_LogOut_Mail_Menu_MessageCircle_Settings_Shield_TrendingUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowUpDown,Bell,ChevronDown,CreditCard,DollarSign,FileText,LayoutDashboard,LogOut,Mail,Menu,MessageCircle,Settings,Shield,TrendingUp,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowUpDown_Bell_ChevronDown_CreditCard_DollarSign_FileText_LayoutDashboard_LogOut_Mail_Menu_MessageCircle_Settings_Shield_TrendingUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowUpDown,Bell,ChevronDown,CreditCard,DollarSign,FileText,LayoutDashboard,LogOut,Mail,Menu,MessageCircle,Settings,Shield,TrendingUp,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowUpDown_Bell_ChevronDown_CreditCard_DollarSign_FileText_LayoutDashboard_LogOut_Mail_Menu_MessageCircle_Settings_Shield_TrendingUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowUpDown,Bell,ChevronDown,CreditCard,DollarSign,FileText,LayoutDashboard,LogOut,Mail,Menu,MessageCircle,Settings,Shield,TrendingUp,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowUpDown_Bell_ChevronDown_CreditCard_DollarSign_FileText_LayoutDashboard_LogOut_Mail_Menu_MessageCircle_Settings_Shield_TrendingUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowUpDown,Bell,ChevronDown,CreditCard,DollarSign,FileText,LayoutDashboard,LogOut,Mail,Menu,MessageCircle,Settings,Shield,TrendingUp,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowUpDown_Bell_ChevronDown_CreditCard_DollarSign_FileText_LayoutDashboard_LogOut_Mail_Menu_MessageCircle_Settings_Shield_TrendingUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowUpDown,Bell,ChevronDown,CreditCard,DollarSign,FileText,LayoutDashboard,LogOut,Mail,Menu,MessageCircle,Settings,Shield,TrendingUp,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-up-down.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowUpDown_Bell_ChevronDown_CreditCard_DollarSign_FileText_LayoutDashboard_LogOut_Mail_Menu_MessageCircle_Settings_Shield_TrendingUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowUpDown,Bell,ChevronDown,CreditCard,DollarSign,FileText,LayoutDashboard,LogOut,Mail,Menu,MessageCircle,Settings,Shield,TrendingUp,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowUpDown_Bell_ChevronDown_CreditCard_DollarSign_FileText_LayoutDashboard_LogOut_Mail_Menu_MessageCircle_Settings_Shield_TrendingUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowUpDown,Bell,ChevronDown,CreditCard,DollarSign,FileText,LayoutDashboard,LogOut,Mail,Menu,MessageCircle,Settings,Shield,TrendingUp,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowUpDown_Bell_ChevronDown_CreditCard_DollarSign_FileText_LayoutDashboard_LogOut_Mail_Menu_MessageCircle_Settings_Shield_TrendingUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowUpDown,Bell,ChevronDown,CreditCard,DollarSign,FileText,LayoutDashboard,LogOut,Mail,Menu,MessageCircle,Settings,Shield,TrendingUp,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowUpDown_Bell_ChevronDown_CreditCard_DollarSign_FileText_LayoutDashboard_LogOut_Mail_Menu_MessageCircle_Settings_Shield_TrendingUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowUpDown,Bell,ChevronDown,CreditCard,DollarSign,FileText,LayoutDashboard,LogOut,Mail,Menu,MessageCircle,Settings,Shield,TrendingUp,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowUpDown_Bell_ChevronDown_CreditCard_DollarSign_FileText_LayoutDashboard_LogOut_Mail_Menu_MessageCircle_Settings_Shield_TrendingUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowUpDown,Bell,ChevronDown,CreditCard,DollarSign,FileText,LayoutDashboard,LogOut,Mail,Menu,MessageCircle,Settings,Shield,TrendingUp,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowUpDown_Bell_ChevronDown_CreditCard_DollarSign_FileText_LayoutDashboard_LogOut_Mail_Menu_MessageCircle_Settings_Shield_TrendingUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowUpDown,Bell,ChevronDown,CreditCard,DollarSign,FileText,LayoutDashboard,LogOut,Mail,Menu,MessageCircle,Settings,Shield,TrendingUp,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowUpDown_Bell_ChevronDown_CreditCard_DollarSign_FileText_LayoutDashboard_LogOut_Mail_Menu_MessageCircle_Settings_Shield_TrendingUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowUpDown,Bell,ChevronDown,CreditCard,DollarSign,FileText,LayoutDashboard,LogOut,Mail,Menu,MessageCircle,Settings,Shield,TrendingUp,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowUpDown_Bell_ChevronDown_CreditCard_DollarSign_FileText_LayoutDashboard_LogOut_Mail_Menu_MessageCircle_Settings_Shield_TrendingUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowUpDown,Bell,ChevronDown,CreditCard,DollarSign,FileText,LayoutDashboard,LogOut,Mail,Menu,MessageCircle,Settings,Shield,TrendingUp,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowUpDown_Bell_ChevronDown_CreditCard_DollarSign_FileText_LayoutDashboard_LogOut_Mail_Menu_MessageCircle_Settings_Shield_TrendingUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowUpDown,Bell,ChevronDown,CreditCard,DollarSign,FileText,LayoutDashboard,LogOut,Mail,Menu,MessageCircle,Settings,Shield,TrendingUp,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowUpDown_Bell_ChevronDown_CreditCard_DollarSign_FileText_LayoutDashboard_LogOut_Mail_Menu_MessageCircle_Settings_Shield_TrendingUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowUpDown,Bell,ChevronDown,CreditCard,DollarSign,FileText,LayoutDashboard,LogOut,Mail,Menu,MessageCircle,Settings,Shield,TrendingUp,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowUpDown_Bell_ChevronDown_CreditCard_DollarSign_FileText_LayoutDashboard_LogOut_Mail_Menu_MessageCircle_Settings_Shield_TrendingUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowUpDown,Bell,ChevronDown,CreditCard,DollarSign,FileText,LayoutDashboard,LogOut,Mail,Menu,MessageCircle,Settings,Shield,TrendingUp,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* __next_internal_client_entry_do_not_use__ AdminLayout auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst AdminLayout = (param)=>{\n    let { children, activeTab, onTabChange } = param;\n    var _navigationItems_find, _user_firstName, _user_firstName1;\n    _s();\n    const { user, logout } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [userDropdownOpen, setUserDropdownOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const dropdownRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Admin navigation items\n    const navigationItems = [\n        {\n            id: 'dashboard',\n            label: 'Dashboard',\n            icon: _barrel_optimize_names_ArrowLeft_ArrowUpDown_Bell_ChevronDown_CreditCard_DollarSign_FileText_LayoutDashboard_LogOut_Mail_Menu_MessageCircle_Settings_Shield_TrendingUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n        },\n        {\n            id: 'users',\n            label: 'User Management',\n            icon: _barrel_optimize_names_ArrowLeft_ArrowUpDown_Bell_ChevronDown_CreditCard_DollarSign_FileText_LayoutDashboard_LogOut_Mail_Menu_MessageCircle_Settings_Shield_TrendingUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n        },\n        {\n            id: 'kyc',\n            label: 'KYC Review',\n            icon: _barrel_optimize_names_ArrowLeft_ArrowUpDown_Bell_ChevronDown_CreditCard_DollarSign_FileText_LayoutDashboard_LogOut_Mail_Menu_MessageCircle_Settings_Shield_TrendingUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n        },\n        {\n            id: 'deposits',\n            label: 'Deposits',\n            icon: _barrel_optimize_names_ArrowLeft_ArrowUpDown_Bell_ChevronDown_CreditCard_DollarSign_FileText_LayoutDashboard_LogOut_Mail_Menu_MessageCircle_Settings_Shield_TrendingUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n        },\n        {\n            id: 'withdrawals',\n            label: 'Withdrawals',\n            icon: _barrel_optimize_names_ArrowLeft_ArrowUpDown_Bell_ChevronDown_CreditCard_DollarSign_FileText_LayoutDashboard_LogOut_Mail_Menu_MessageCircle_Settings_Shield_TrendingUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n        },\n        {\n            id: 'support',\n            label: 'Support Tickets',\n            icon: _barrel_optimize_names_ArrowLeft_ArrowUpDown_Bell_ChevronDown_CreditCard_DollarSign_FileText_LayoutDashboard_LogOut_Mail_Menu_MessageCircle_Settings_Shield_TrendingUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n        },\n        {\n            id: 'binary-points',\n            label: 'Binary Points',\n            icon: _barrel_optimize_names_ArrowLeft_ArrowUpDown_Bell_ChevronDown_CreditCard_DollarSign_FileText_LayoutDashboard_LogOut_Mail_Menu_MessageCircle_Settings_Shield_TrendingUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n        },\n        {\n            id: 'referral-commissions',\n            label: 'Referral Commissions',\n            icon: _barrel_optimize_names_ArrowLeft_ArrowUpDown_Bell_ChevronDown_CreditCard_DollarSign_FileText_LayoutDashboard_LogOut_Mail_Menu_MessageCircle_Settings_Shield_TrendingUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"]\n        },\n        {\n            id: 'email-settings',\n            label: 'Email Settings',\n            icon: _barrel_optimize_names_ArrowLeft_ArrowUpDown_Bell_ChevronDown_CreditCard_DollarSign_FileText_LayoutDashboard_LogOut_Mail_Menu_MessageCircle_Settings_Shield_TrendingUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"]\n        },\n        {\n            id: 'settings',\n            label: 'System Settings',\n            icon: _barrel_optimize_names_ArrowLeft_ArrowUpDown_Bell_ChevronDown_CreditCard_DollarSign_FileText_LayoutDashboard_LogOut_Mail_Menu_MessageCircle_Settings_Shield_TrendingUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"]\n        },\n        {\n            id: 'logs',\n            label: 'System Logs',\n            icon: _barrel_optimize_names_ArrowLeft_ArrowUpDown_Bell_ChevronDown_CreditCard_DollarSign_FileText_LayoutDashboard_LogOut_Mail_Menu_MessageCircle_Settings_Shield_TrendingUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"]\n        }\n    ];\n    const handleLogout = async ()=>{\n        await logout();\n    };\n    // Close dropdown when clicking outside\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminLayout.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"AdminLayout.useEffect.handleClickOutside\": (event)=>{\n                    if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n                        setUserDropdownOpen(false);\n                    }\n                }\n            }[\"AdminLayout.useEffect.handleClickOutside\"];\n            document.addEventListener('mousedown', handleClickOutside);\n            return ({\n                \"AdminLayout.useEffect\": ()=>{\n                    document.removeEventListener('mousedown', handleClickOutside);\n                }\n            })[\"AdminLayout.useEffect\"];\n        }\n    }[\"AdminLayout.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-slate-900 flex admin-panel\",\n        \"data-admin-panel\": \"true\",\n        children: [\n            sidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-40 bg-black bg-opacity-75 lg:hidden\",\n                onClick: ()=>setSidebarOpen(false)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                lineNumber: 84,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                className: \"\\n        fixed inset-y-0 left-0 z-50 w-64 bg-slate-800 shadow-xl border-r border-slate-700\\n        transform transition-all duration-300 ease-in-out\\n        \".concat(sidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0', \"\\n      \"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col h-screen\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between h-14 px-5 border-b border-slate-700 bg-slate-800 flex-shrink-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/\",\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons__WEBPACK_IMPORTED_MODULE_6__.SolarPanel, {\n                                                className: \"h-5 w-5 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                                lineNumber: 101,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                            lineNumber: 100,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-lg font-bold text-white\",\n                                            children: \"HashCoreX\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                            lineNumber: 103,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                    lineNumber: 99,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setSidebarOpen(false),\n                                    className: \"lg:hidden p-1.5 rounded-lg text-slate-400\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowUpDown_Bell_ChevronDown_CreditCard_DollarSign_FileText_LayoutDashboard_LogOut_Mail_Menu_MessageCircle_Settings_Shield_TrendingUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                        lineNumber: 109,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                    lineNumber: 105,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                            lineNumber: 98,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-3 py-3 bg-red-600 border-b border-slate-700\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 text-white\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowUpDown_Bell_ChevronDown_CreditCard_DollarSign_FileText_LayoutDashboard_LogOut_Mail_Menu_MessageCircle_Settings_Shield_TrendingUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-semibold\",\n                                        children: \"Admin Panel\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                            lineNumber: 114,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"flex-1 px-3 py-4 space-y-1 min-h-0\",\n                            children: navigationItems.map((item)=>{\n                                const Icon = item.icon;\n                                const isActive = activeTab === item.id;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        onTabChange(item.id);\n                                        setSidebarOpen(false);\n                                    },\n                                    className: \"\\n                    w-full flex items-center space-x-3 px-3 py-2.5 rounded-lg text-left group\\n                    \".concat(isActive ? 'bg-blue-600 text-white shadow-md' : 'text-slate-300', \"\\n                  \"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                            className: \"h-4 w-4 \".concat(isActive ? 'text-white' : 'text-slate-400')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium text-sm\",\n                                            children: item.label\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                            lineNumber: 143,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, item.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 17\n                                }, undefined);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-3 py-3 border-t border-slate-700\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/dashboard\",\n                                className: \"w-full flex items-center space-x-3 px-3 py-2.5 rounded-lg text-slate-300 group\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowUpDown_Bell_ChevronDown_CreditCard_DollarSign_FileText_LayoutDashboard_LogOut_Mail_Menu_MessageCircle_Settings_Shield_TrendingUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium text-sm\",\n                                        children: \"Back to Dashboard\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                        lineNumber: 156,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                            lineNumber: 150,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-3 py-3 border-t border-slate-700 bg-slate-900 flex-shrink-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleLogout,\n                                className: \"w-full flex items-center space-x-3 px-3 py-2.5 rounded-lg text-slate-300 group\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowUpDown_Bell_ChevronDown_CreditCard_DollarSign_FileText_LayoutDashboard_LogOut_Mail_Menu_MessageCircle_Settings_Shield_TrendingUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium text-sm\",\n                                        children: \"Logout\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                    lineNumber: 96,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                lineNumber: 91,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col min-w-0 lg:ml-64\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"bg-slate-800 shadow-sm border-b border-slate-700 sticky top-0 z-30\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-4 sm:px-6 lg:px-8 xl:px-12\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout__WEBPACK_IMPORTED_MODULE_4__.Flex, {\n                                justify: \"between\",\n                                align: \"center\",\n                                className: \"h-16\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setSidebarOpen(true),\n                                                className: \"lg:hidden p-2 rounded-lg text-slate-400\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowUpDown_Bell_ChevronDown_CreditCard_DollarSign_FileText_LayoutDashboard_LogOut_Mail_Menu_MessageCircle_Settings_Shield_TrendingUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    className: \"h-6 w-6\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                                    lineNumber: 184,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                                lineNumber: 180,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-2xl font-bold text-white capitalize tracking-wide drop-shadow-lg\",\n                                                        children: ((_navigationItems_find = navigationItems.find((item)=>item.id === activeTab)) === null || _navigationItems_find === void 0 ? void 0 : _navigationItems_find.label) || 'Admin Dashboard'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                                        lineNumber: 187,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-slate-300 hidden sm:block font-medium\",\n                                                        children: \"Manage platform operations and user activities\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                                        lineNumber: 190,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                                lineNumber: 186,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"relative p-2 rounded-lg text-slate-400\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowUpDown_Bell_ChevronDown_CreditCard_DollarSign_FileText_LayoutDashboard_LogOut_Mail_Menu_MessageCircle_Settings_Shield_TrendingUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                                        lineNumber: 199,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"absolute top-1 right-1 h-2 w-2 bg-orange-500 rounded-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                                        lineNumber: 200,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                                lineNumber: 198,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"px-3 py-1.5 rounded-lg text-xs font-semibold border bg-red-600 text-white border-red-500\",\n                                                children: \"ADMIN\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                                lineNumber: 204,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                ref: dropdownRef,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setUserDropdownOpen(!userDropdownOpen),\n                                                        className: \"flex items-center space-x-2 p-1 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_5__.ProfileImage, {\n                                                                src: user === null || user === void 0 ? void 0 : user.profilePicture,\n                                                                alt: \"Profile\",\n                                                                size: 32,\n                                                                className: \"rounded-lg\",\n                                                                fallbackText: (user === null || user === void 0 ? void 0 : (_user_firstName = user.firstName) === null || _user_firstName === void 0 ? void 0 : _user_firstName.charAt(0).toUpperCase()) || (user === null || user === void 0 ? void 0 : user.email.charAt(0).toUpperCase()),\n                                                                fallbackBgColor: \"bg-orange-600\",\n                                                                loading: \"lazy\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                                                lineNumber: 214,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowUpDown_Bell_ChevronDown_CreditCard_DollarSign_FileText_LayoutDashboard_LogOut_Mail_Menu_MessageCircle_Settings_Shield_TrendingUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                className: \"h-4 w-4 text-slate-400 transition-transform \".concat(userDropdownOpen ? 'rotate-180' : '')\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                                                lineNumber: 223,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                                        lineNumber: 210,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    userDropdownOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute right-0 mt-2 w-64 bg-slate-800 rounded-xl shadow-lg border border-slate-700 py-2 z-50\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"px-4 py-3 border-b border-slate-700\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_5__.ProfileImage, {\n                                                                            src: user === null || user === void 0 ? void 0 : user.profilePicture,\n                                                                            alt: \"Profile\",\n                                                                            size: 40,\n                                                                            className: \"rounded-lg\",\n                                                                            fallbackText: (user === null || user === void 0 ? void 0 : (_user_firstName1 = user.firstName) === null || _user_firstName1 === void 0 ? void 0 : _user_firstName1.charAt(0).toUpperCase()) || (user === null || user === void 0 ? void 0 : user.email.charAt(0).toUpperCase()),\n                                                                            fallbackBgColor: \"bg-orange-600\",\n                                                                            loading: \"lazy\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                                                            lineNumber: 232,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex-1 min-w-0\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm font-semibold text-white truncate\",\n                                                                                    children: (user === null || user === void 0 ? void 0 : user.firstName) && (user === null || user === void 0 ? void 0 : user.lastName) ? \"\".concat(user.firstName, \" \").concat(user.lastName) : user === null || user === void 0 ? void 0 : user.email.split('@')[0]\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                                                                    lineNumber: 242,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-xs text-slate-400\",\n                                                                                    children: [\n                                                                                        \"ID: \",\n                                                                                        user === null || user === void 0 ? void 0 : user.referralId\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                                                                    lineNumber: 248,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-xs text-red-400 font-medium\",\n                                                                                    children: \"Administrator\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                                                                    lineNumber: 251,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                                                            lineNumber: 241,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                                                    lineNumber: 231,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                                                lineNumber: 230,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"py-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                        href: \"/dashboard\",\n                                                                        onClick: ()=>setUserDropdownOpen(false),\n                                                                        className: \"w-full flex items-center space-x-3 px-4 py-2 text-sm text-slate-300\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowUpDown_Bell_ChevronDown_CreditCard_DollarSign_FileText_LayoutDashboard_LogOut_Mail_Menu_MessageCircle_Settings_Shield_TrendingUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                                                                lineNumber: 265,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"User Dashboard\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                                                                lineNumber: 266,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                                                        lineNumber: 260,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"border-t border-slate-700 my-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                                                        lineNumber: 268,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>{\n                                                                            setUserDropdownOpen(false);\n                                                                            handleLogout();\n                                                                        },\n                                                                        className: \"w-full flex items-center space-x-3 px-4 py-2 text-sm text-red-400\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowUpDown_Bell_ChevronDown_CreditCard_DollarSign_FileText_LayoutDashboard_LogOut_Mail_Menu_MessageCircle_Settings_Shield_TrendingUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                                                                lineNumber: 276,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"Sign Out\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                                                                lineNumber: 277,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                                                        lineNumber: 269,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                                                lineNumber: 259,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                                        lineNumber: 228,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                                lineNumber: 209,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                        lineNumber: 196,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                lineNumber: 178,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                        lineNumber: 176,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 bg-slate-900 overflow-y-auto relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"fixed inset-0 bg-cover bg-center bg-no-repeat opacity-10 pointer-events-none\",\n                                style: {\n                                    backgroundImage: 'url(/admin_background.jpg)',\n                                    backgroundAttachment: 'fixed',\n                                    zIndex: 0\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                lineNumber: 291,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative z-10 px-4 sm:px-6 lg:px-8 xl:px-12 py-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-w-7xl mx-auto\",\n                                    children: children\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                    lineNumber: 301,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                lineNumber: 300,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                        lineNumber: 289,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                lineNumber: 174,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\AdminLayout.tsx\",\n        lineNumber: 81,\n        columnNumber: 5\n    }, undefined);\n};\n_s(AdminLayout, \"DxOEgSB4GnaNtXD2PifQU3JJyCQ=\", false, function() {\n    return [\n        _hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__.useAuth\n    ];\n});\n_c = AdminLayout;\nvar _c;\n$RefreshReg$(_c, \"AdminLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2FkbWluL0FkbWluTGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFMkQ7QUFDOUI7QUFFYTtBQUNZO0FBQ0M7QUFDUDtBQW9CMUI7QUFRZixNQUFNMkIsY0FBMEM7UUFBQyxFQUN0REMsUUFBUSxFQUNSQyxTQUFTLEVBQ1RDLFdBQVcsRUFDWjtRQW1Kb0JDLHVCQStCZUMsaUJBa0JNQTs7SUFuTXhDLE1BQU0sRUFBRUEsSUFBSSxFQUFFQyxNQUFNLEVBQUUsR0FBRzVCLHVEQUFPQTtJQUNoQyxNQUFNLENBQUM2QixhQUFhQyxlQUFlLEdBQUdsQywrQ0FBUUEsQ0FBQztJQUMvQyxNQUFNLENBQUNtQyxrQkFBa0JDLG9CQUFvQixHQUFHcEMsK0NBQVFBLENBQUM7SUFDekQsTUFBTXFDLGNBQWNwQyw2Q0FBTUEsQ0FBaUI7SUFFM0MseUJBQXlCO0lBQ3pCLE1BQU02QixrQkFBa0I7UUFDdEI7WUFBRVEsSUFBSTtZQUFhQyxPQUFPO1lBQWFDLE1BQU1oQyx3T0FBZUE7UUFBQztRQUM3RDtZQUFFOEIsSUFBSTtZQUFTQyxPQUFPO1lBQW1CQyxNQUFNL0Isd09BQUtBO1FBQUM7UUFDckQ7WUFBRTZCLElBQUk7WUFBT0MsT0FBTztZQUFjQyxNQUFNOUIsd09BQU1BO1FBQUM7UUFDL0M7WUFBRTRCLElBQUk7WUFBWUMsT0FBTztZQUFZQyxNQUFNbkIseU9BQVVBO1FBQUM7UUFDdEQ7WUFBRWlCLElBQUk7WUFBZUMsT0FBTztZQUFlQyxNQUFNN0IseU9BQVVBO1FBQUM7UUFDNUQ7WUFBRTJCLElBQUk7WUFBV0MsT0FBTztZQUFtQkMsTUFBTWxCLHlPQUFhQTtRQUFDO1FBQy9EO1lBQUVnQixJQUFJO1lBQWlCQyxPQUFPO1lBQWlCQyxNQUFNakIseU9BQVdBO1FBQUM7UUFDakU7WUFBRWUsSUFBSTtZQUF3QkMsT0FBTztZQUF3QkMsTUFBTWhCLHlPQUFVQTtRQUFDO1FBQzlFO1lBQUVjLElBQUk7WUFBa0JDLE9BQU87WUFBa0JDLE1BQU1mLHlPQUFJQTtRQUFDO1FBQzVEO1lBQUVhLElBQUk7WUFBWUMsT0FBTztZQUFtQkMsTUFBTTVCLHlPQUFRQTtRQUFDO1FBQzNEO1lBQUUwQixJQUFJO1lBQVFDLE9BQU87WUFBZUMsTUFBTTNCLHlPQUFRQTtRQUFDO0tBQ3BEO0lBRUQsTUFBTTRCLGVBQWU7UUFDbkIsTUFBTVQ7SUFDUjtJQUVBLHVDQUF1QztJQUN2QzlCLGdEQUFTQTtpQ0FBQztZQUNSLE1BQU13Qzs0REFBcUIsQ0FBQ0M7b0JBQzFCLElBQUlOLFlBQVlPLE9BQU8sSUFBSSxDQUFDUCxZQUFZTyxPQUFPLENBQUNDLFFBQVEsQ0FBQ0YsTUFBTUcsTUFBTSxHQUFXO3dCQUM5RVYsb0JBQW9CO29CQUN0QjtnQkFDRjs7WUFFQVcsU0FBU0MsZ0JBQWdCLENBQUMsYUFBYU47WUFDdkM7eUNBQU87b0JBQ0xLLFNBQVNFLG1CQUFtQixDQUFDLGFBQWFQO2dCQUM1Qzs7UUFDRjtnQ0FBRyxFQUFFO0lBRUwscUJBQ0UsOERBQUNRO1FBQUlDLFdBQVU7UUFBNkNDLG9CQUFpQjs7WUFFMUVuQiw2QkFDQyw4REFBQ2lCO2dCQUNDQyxXQUFVO2dCQUNWRSxTQUFTLElBQU1uQixlQUFlOzs7Ozs7MEJBS2xDLDhEQUFDb0I7Z0JBQU1ILFdBQVcsbUtBR3VELE9BQXJFbEIsY0FBYyxrQkFBa0Isc0NBQXFDOzBCQUV2RSw0RUFBQ2lCO29CQUFJQyxXQUFVOztzQ0FFYiw4REFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDaEQsa0RBQUlBO29DQUFDb0QsTUFBSztvQ0FBSUosV0FBVTs7c0RBQ3ZCLDhEQUFDRDs0Q0FBSUMsV0FBVTtzREFDYiw0RUFBQzVDLHlEQUFVQTtnREFBQzRDLFdBQVU7Ozs7Ozs7Ozs7O3NEQUV4Qiw4REFBQ0s7NENBQUtMLFdBQVU7c0RBQStCOzs7Ozs7Ozs7Ozs7OENBRWpELDhEQUFDTTtvQ0FDQ0osU0FBUyxJQUFNbkIsZUFBZTtvQ0FDOUJpQixXQUFVOzhDQUVWLDRFQUFDbkMseU9BQUNBO3dDQUFDbUMsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBS2pCLDhEQUFDRDs0QkFBSUMsV0FBVTtzQ0FDYiw0RUFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDekMsd09BQU1BO3dDQUFDeUMsV0FBVTs7Ozs7O2tEQUNsQiw4REFBQ0s7d0NBQUtMLFdBQVU7a0RBQXdCOzs7Ozs7Ozs7Ozs7Ozs7OztzQ0FLNUMsOERBQUNPOzRCQUFJUCxXQUFVO3NDQUNackIsZ0JBQWdCNkIsR0FBRyxDQUFDLENBQUNDO2dDQUNwQixNQUFNQyxPQUFPRCxLQUFLcEIsSUFBSTtnQ0FDdEIsTUFBTXNCLFdBQVdsQyxjQUFjZ0MsS0FBS3RCLEVBQUU7Z0NBRXRDLHFCQUNFLDhEQUFDbUI7b0NBRUNKLFNBQVM7d0NBQ1B4QixZQUFZK0IsS0FBS3RCLEVBQUU7d0NBQ25CSixlQUFlO29DQUNqQjtvQ0FDQWlCLFdBQVcsd0hBS1IsT0FIQ1csV0FDRSxxQ0FDQSxrQkFDSDs7c0RBR0gsOERBQUNEOzRDQUFLVixXQUFXLFdBQXNELE9BQTNDVyxXQUFXLGVBQWU7Ozs7OztzREFDdEQsOERBQUNOOzRDQUFLTCxXQUFVO3NEQUF1QlMsS0FBS3JCLEtBQUs7Ozs7Ozs7bUNBZDVDcUIsS0FBS3RCLEVBQUU7Ozs7OzRCQWlCbEI7Ozs7OztzQ0FJRiw4REFBQ1k7NEJBQUlDLFdBQVU7c0NBQ2IsNEVBQUNoRCxrREFBSUE7Z0NBQ0hvRCxNQUFLO2dDQUNMSixXQUFVOztrREFFViw4REFBQy9CLHlPQUFTQTt3Q0FBQytCLFdBQVU7Ozs7OztrREFDckIsOERBQUNLO3dDQUFLTCxXQUFVO2tEQUFzQjs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBSzFDLDhEQUFDRDs0QkFBSUMsV0FBVTtzQ0FDYiw0RUFBQ007Z0NBQ0NKLFNBQVNaO2dDQUNUVSxXQUFVOztrREFFViw4REFBQ3JDLHlPQUFNQTt3Q0FBQ3FDLFdBQVU7Ozs7OztrREFDbEIsOERBQUNLO3dDQUFLTCxXQUFVO2tEQUFzQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFPOUMsOERBQUNEO2dCQUFJQyxXQUFVOztrQ0FFYiw4REFBQ1k7d0JBQU9aLFdBQVU7a0NBQ2hCLDRFQUFDRDs0QkFBSUMsV0FBVTtzQ0FDYiw0RUFBQzlDLG9EQUFJQTtnQ0FBQzJELFNBQVE7Z0NBQVVDLE9BQU07Z0NBQVNkLFdBQVU7O2tEQUMvQyw4REFBQ0Q7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDTTtnREFDQ0osU0FBUyxJQUFNbkIsZUFBZTtnREFDOUJpQixXQUFVOzBEQUVWLDRFQUFDcEMseU9BQUlBO29EQUFDb0MsV0FBVTs7Ozs7Ozs7Ozs7MERBRWxCLDhEQUFDRDs7a0VBQ0MsOERBQUNnQjt3REFBR2YsV0FBVTtrRUFDWHJCLEVBQUFBLHdCQUFBQSxnQkFBZ0JxQyxJQUFJLENBQUNQLENBQUFBLE9BQVFBLEtBQUt0QixFQUFFLEtBQUtWLHdCQUF6Q0UsNENBQUFBLHNCQUFxRFMsS0FBSyxLQUFJOzs7Ozs7a0VBRWpFLDhEQUFDNkI7d0RBQUVqQixXQUFVO2tFQUFxRDs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tEQU10RSw4REFBQ0Q7d0NBQUlDLFdBQVU7OzBEQUViLDhEQUFDTTtnREFBT04sV0FBVTs7a0VBQ2hCLDhEQUFDbEMseU9BQUlBO3dEQUFDa0MsV0FBVTs7Ozs7O2tFQUNoQiw4REFBQ0s7d0RBQUtMLFdBQVU7Ozs7Ozs7Ozs7OzswREFJbEIsOERBQUNEO2dEQUFJQyxXQUFVOzBEQUEyRjs7Ozs7OzBEQUsxRyw4REFBQ0Q7Z0RBQUlDLFdBQVU7Z0RBQVdrQixLQUFLaEM7O2tFQUM3Qiw4REFBQ29CO3dEQUNDSixTQUFTLElBQU1qQixvQkFBb0IsQ0FBQ0Q7d0RBQ3BDZ0IsV0FBVTs7MEVBRVYsOERBQUM3Qyx3REFBWUE7Z0VBQ1hnRSxHQUFHLEVBQUV2QyxpQkFBQUEsMkJBQUFBLEtBQU13QyxjQUFjO2dFQUN6QkMsS0FBSTtnRUFDSkMsTUFBTTtnRUFDTnRCLFdBQVU7Z0VBQ1Z1QixjQUFjM0MsQ0FBQUEsaUJBQUFBLDRCQUFBQSxrQkFBQUEsS0FBTTRDLFNBQVMsY0FBZjVDLHNDQUFBQSxnQkFBaUI2QyxNQUFNLENBQUMsR0FBR0MsV0FBVyxRQUFNOUMsaUJBQUFBLDJCQUFBQSxLQUFNK0MsS0FBSyxDQUFDRixNQUFNLENBQUMsR0FBR0MsV0FBVztnRUFDM0ZFLGlCQUFnQjtnRUFDaEJDLFNBQVE7Ozs7OzswRUFFViw4REFBQzlELHlPQUFXQTtnRUFBQ2lDLFdBQVcsK0NBQW9GLE9BQXJDaEIsbUJBQW1CLGVBQWU7Ozs7Ozs7Ozs7OztvREFJMUdBLGtDQUNDLDhEQUFDZTt3REFBSUMsV0FBVTs7MEVBRWIsOERBQUNEO2dFQUFJQyxXQUFVOzBFQUNiLDRFQUFDRDtvRUFBSUMsV0FBVTs7c0ZBQ2IsOERBQUM3Qyx3REFBWUE7NEVBQ1hnRSxHQUFHLEVBQUV2QyxpQkFBQUEsMkJBQUFBLEtBQU13QyxjQUFjOzRFQUN6QkMsS0FBSTs0RUFDSkMsTUFBTTs0RUFDTnRCLFdBQVU7NEVBQ1Z1QixjQUFjM0MsQ0FBQUEsaUJBQUFBLDRCQUFBQSxtQkFBQUEsS0FBTTRDLFNBQVMsY0FBZjVDLHVDQUFBQSxpQkFBaUI2QyxNQUFNLENBQUMsR0FBR0MsV0FBVyxRQUFNOUMsaUJBQUFBLDJCQUFBQSxLQUFNK0MsS0FBSyxDQUFDRixNQUFNLENBQUMsR0FBR0MsV0FBVzs0RUFDM0ZFLGlCQUFnQjs0RUFDaEJDLFNBQVE7Ozs7OztzRkFFViw4REFBQzlCOzRFQUFJQyxXQUFVOzs4RkFDYiw4REFBQ2lCO29GQUFFakIsV0FBVTs4RkFDVnBCLENBQUFBLGlCQUFBQSwyQkFBQUEsS0FBTTRDLFNBQVMsTUFBSTVDLGlCQUFBQSwyQkFBQUEsS0FBTWtELFFBQVEsSUFDOUIsR0FBcUJsRCxPQUFsQkEsS0FBSzRDLFNBQVMsRUFBQyxLQUFpQixPQUFkNUMsS0FBS2tELFFBQVEsSUFDbENsRCxpQkFBQUEsMkJBQUFBLEtBQU0rQyxLQUFLLENBQUNJLEtBQUssQ0FBQyxJQUFJLENBQUMsRUFBRTs7Ozs7OzhGQUcvQiw4REFBQ2Q7b0ZBQUVqQixXQUFVOzt3RkFBeUI7d0ZBQy9CcEIsaUJBQUFBLDJCQUFBQSxLQUFNb0QsVUFBVTs7Ozs7Ozs4RkFFdkIsOERBQUNmO29GQUFFakIsV0FBVTs4RkFBbUM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBFQVF0RCw4REFBQ0Q7Z0VBQUlDLFdBQVU7O2tGQUNiLDhEQUFDaEQsa0RBQUlBO3dFQUNIb0QsTUFBSzt3RUFDTEYsU0FBUyxJQUFNakIsb0JBQW9CO3dFQUNuQ2UsV0FBVTs7MEZBRVYsOERBQUNoQyx5T0FBSUE7Z0ZBQUNnQyxXQUFVOzs7Ozs7MEZBQ2hCLDhEQUFDSzswRkFBSzs7Ozs7Ozs7Ozs7O2tGQUVSLDhEQUFDTjt3RUFBSUMsV0FBVTs7Ozs7O2tGQUNmLDhEQUFDTTt3RUFDQ0osU0FBUzs0RUFDUGpCLG9CQUFvQjs0RUFDcEJLO3dFQUNGO3dFQUNBVSxXQUFVOzswRkFFViw4REFBQ3JDLHlPQUFNQTtnRkFBQ3FDLFdBQVU7Ozs7OzswRkFDbEIsOERBQUNLOzBGQUFLOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQVl4Qiw4REFBQzRCO3dCQUFLakMsV0FBVTs7MENBRWQsOERBQUNEO2dDQUNDQyxXQUFVO2dDQUNWa0MsT0FBTztvQ0FDTEMsaUJBQWlCO29DQUNqQkMsc0JBQXNCO29DQUN0QkMsUUFBUTtnQ0FDVjs7Ozs7OzBDQUdGLDhEQUFDdEM7Z0NBQUlDLFdBQVU7MENBQ2IsNEVBQUNEO29DQUFJQyxXQUFVOzhDQUNaeEI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBT2YsRUFBRTtHQWhSV0Q7O1FBS2N0QixtREFBT0E7OztLQUxyQnNCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGRyZWFtXFxEZXNrdG9wXFxIYXNoX01pbmluZ3NcXGhhc2hjb3JleFxcc3JjXFxjb21wb25lbnRzXFxhZG1pblxcQWRtaW5MYXlvdXQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IFJlYWN0LCB7IHVzZVN0YXRlLCB1c2VSZWYsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCBMaW5rIGZyb20gJ25leHQvbGluayc7XG5pbXBvcnQgSW1hZ2UgZnJvbSAnbmV4dC9pbWFnZSc7XG5pbXBvcnQgeyB1c2VBdXRoIH0gZnJvbSAnQC9ob29rcy91c2VBdXRoJztcbmltcG9ydCB7IENvbnRhaW5lciwgRmxleCB9IGZyb20gJ0AvY29tcG9uZW50cy9sYXlvdXQnO1xuaW1wb3J0IHsgQnV0dG9uLCBQcm9maWxlSW1hZ2UgfSBmcm9tICdAL2NvbXBvbmVudHMvdWknO1xuaW1wb3J0IHsgU29sYXJQYW5lbCB9IGZyb20gJ0AvY29tcG9uZW50cy9pY29ucyc7XG5pbXBvcnQge1xuICBMYXlvdXREYXNoYm9hcmQsXG4gIFVzZXJzLFxuICBTaGllbGQsXG4gIENyZWRpdENhcmQsXG4gIFNldHRpbmdzLFxuICBGaWxlVGV4dCxcbiAgTG9nT3V0LFxuICBNZW51LFxuICBYLFxuICBCZWxsLFxuICBDaGV2cm9uRG93bixcbiAgVXNlcixcbiAgQXJyb3dMZWZ0LFxuICBEb2xsYXJTaWduLFxuICBNZXNzYWdlQ2lyY2xlLFxuICBBcnJvd1VwRG93bixcbiAgVHJlbmRpbmdVcCxcbiAgTWFpbFxufSBmcm9tICdsdWNpZGUtcmVhY3QnO1xuXG5pbnRlcmZhY2UgQWRtaW5MYXlvdXRQcm9wcyB7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGU7XG4gIGFjdGl2ZVRhYjogc3RyaW5nO1xuICBvblRhYkNoYW5nZTogKHRhYjogc3RyaW5nKSA9PiB2b2lkO1xufVxuXG5leHBvcnQgY29uc3QgQWRtaW5MYXlvdXQ6IFJlYWN0LkZDPEFkbWluTGF5b3V0UHJvcHM+ID0gKHtcbiAgY2hpbGRyZW4sXG4gIGFjdGl2ZVRhYixcbiAgb25UYWJDaGFuZ2UsXG59KSA9PiB7XG4gIGNvbnN0IHsgdXNlciwgbG9nb3V0IH0gPSB1c2VBdXRoKCk7XG4gIGNvbnN0IFtzaWRlYmFyT3Blbiwgc2V0U2lkZWJhck9wZW5dID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbdXNlckRyb3Bkb3duT3Blbiwgc2V0VXNlckRyb3Bkb3duT3Blbl0gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IGRyb3Bkb3duUmVmID0gdXNlUmVmPEhUTUxEaXZFbGVtZW50PihudWxsKTtcblxuICAvLyBBZG1pbiBuYXZpZ2F0aW9uIGl0ZW1zXG4gIGNvbnN0IG5hdmlnYXRpb25JdGVtcyA9IFtcbiAgICB7IGlkOiAnZGFzaGJvYXJkJywgbGFiZWw6ICdEYXNoYm9hcmQnLCBpY29uOiBMYXlvdXREYXNoYm9hcmQgfSxcbiAgICB7IGlkOiAndXNlcnMnLCBsYWJlbDogJ1VzZXIgTWFuYWdlbWVudCcsIGljb246IFVzZXJzIH0sXG4gICAgeyBpZDogJ2t5YycsIGxhYmVsOiAnS1lDIFJldmlldycsIGljb246IFNoaWVsZCB9LFxuICAgIHsgaWQ6ICdkZXBvc2l0cycsIGxhYmVsOiAnRGVwb3NpdHMnLCBpY29uOiBEb2xsYXJTaWduIH0sXG4gICAgeyBpZDogJ3dpdGhkcmF3YWxzJywgbGFiZWw6ICdXaXRoZHJhd2FscycsIGljb246IENyZWRpdENhcmQgfSxcbiAgICB7IGlkOiAnc3VwcG9ydCcsIGxhYmVsOiAnU3VwcG9ydCBUaWNrZXRzJywgaWNvbjogTWVzc2FnZUNpcmNsZSB9LFxuICAgIHsgaWQ6ICdiaW5hcnktcG9pbnRzJywgbGFiZWw6ICdCaW5hcnkgUG9pbnRzJywgaWNvbjogQXJyb3dVcERvd24gfSxcbiAgICB7IGlkOiAncmVmZXJyYWwtY29tbWlzc2lvbnMnLCBsYWJlbDogJ1JlZmVycmFsIENvbW1pc3Npb25zJywgaWNvbjogVHJlbmRpbmdVcCB9LFxuICAgIHsgaWQ6ICdlbWFpbC1zZXR0aW5ncycsIGxhYmVsOiAnRW1haWwgU2V0dGluZ3MnLCBpY29uOiBNYWlsIH0sXG4gICAgeyBpZDogJ3NldHRpbmdzJywgbGFiZWw6ICdTeXN0ZW0gU2V0dGluZ3MnLCBpY29uOiBTZXR0aW5ncyB9LFxuICAgIHsgaWQ6ICdsb2dzJywgbGFiZWw6ICdTeXN0ZW0gTG9ncycsIGljb246IEZpbGVUZXh0IH0sXG4gIF07XG5cbiAgY29uc3QgaGFuZGxlTG9nb3V0ID0gYXN5bmMgKCkgPT4ge1xuICAgIGF3YWl0IGxvZ291dCgpO1xuICB9O1xuXG4gIC8vIENsb3NlIGRyb3Bkb3duIHdoZW4gY2xpY2tpbmcgb3V0c2lkZVxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IGhhbmRsZUNsaWNrT3V0c2lkZSA9IChldmVudDogTW91c2VFdmVudCkgPT4ge1xuICAgICAgaWYgKGRyb3Bkb3duUmVmLmN1cnJlbnQgJiYgIWRyb3Bkb3duUmVmLmN1cnJlbnQuY29udGFpbnMoZXZlbnQudGFyZ2V0IGFzIE5vZGUpKSB7XG4gICAgICAgIHNldFVzZXJEcm9wZG93bk9wZW4oZmFsc2UpO1xuICAgICAgfVxuICAgIH07XG5cbiAgICBkb2N1bWVudC5hZGRFdmVudExpc3RlbmVyKCdtb3VzZWRvd24nLCBoYW5kbGVDbGlja091dHNpZGUpO1xuICAgIHJldHVybiAoKSA9PiB7XG4gICAgICBkb2N1bWVudC5yZW1vdmVFdmVudExpc3RlbmVyKCdtb3VzZWRvd24nLCBoYW5kbGVDbGlja091dHNpZGUpO1xuICAgIH07XG4gIH0sIFtdKTtcblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGJnLXNsYXRlLTkwMCBmbGV4IGFkbWluLXBhbmVsXCIgZGF0YS1hZG1pbi1wYW5lbD1cInRydWVcIj5cbiAgICAgIHsvKiBNb2JpbGUgc2lkZWJhciBvdmVybGF5ICovfVxuICAgICAge3NpZGViYXJPcGVuICYmIChcbiAgICAgICAgPGRpdlxuICAgICAgICAgIGNsYXNzTmFtZT1cImZpeGVkIGluc2V0LTAgei00MCBiZy1ibGFjayBiZy1vcGFjaXR5LTc1IGxnOmhpZGRlblwiXG4gICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2lkZWJhck9wZW4oZmFsc2UpfVxuICAgICAgICAvPlxuICAgICAgKX1cblxuICAgICAgey8qIEZpeGVkIFNpZGViYXIgKi99XG4gICAgICA8YXNpZGUgY2xhc3NOYW1lPXtgXG4gICAgICAgIGZpeGVkIGluc2V0LXktMCBsZWZ0LTAgei01MCB3LTY0IGJnLXNsYXRlLTgwMCBzaGFkb3cteGwgYm9yZGVyLXIgYm9yZGVyLXNsYXRlLTcwMFxuICAgICAgICB0cmFuc2Zvcm0gdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwIGVhc2UtaW4tb3V0XG4gICAgICAgICR7c2lkZWJhck9wZW4gPyAndHJhbnNsYXRlLXgtMCcgOiAnLXRyYW5zbGF0ZS14LWZ1bGwgbGc6dHJhbnNsYXRlLXgtMCd9XG4gICAgICBgfT5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIGgtc2NyZWVuXCI+XG4gICAgICAgICAgey8qIExvZ28gSGVhZGVyICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIGgtMTQgcHgtNSBib3JkZXItYiBib3JkZXItc2xhdGUtNzAwIGJnLXNsYXRlLTgwMCBmbGV4LXNocmluay0wXCI+XG4gICAgICAgICAgICA8TGluayBocmVmPVwiL1wiIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctOCBoLTggYmctYmx1ZS02MDAgcm91bmRlZC1sZyBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgIDxTb2xhclBhbmVsIGNsYXNzTmFtZT1cImgtNSB3LTUgdGV4dC13aGl0ZVwiIC8+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtYm9sZCB0ZXh0LXdoaXRlXCI+SGFzaENvcmVYPC9zcGFuPlxuICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTaWRlYmFyT3BlbihmYWxzZSl9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImxnOmhpZGRlbiBwLTEuNSByb3VuZGVkLWxnIHRleHQtc2xhdGUtNDAwXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPFggY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIHsvKiBBZG1pbiBCYWRnZSAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInB4LTMgcHktMyBiZy1yZWQtNjAwIGJvcmRlci1iIGJvcmRlci1zbGF0ZS03MDBcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yIHRleHQtd2hpdGVcIj5cbiAgICAgICAgICAgICAgPFNoaWVsZCBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LXNlbWlib2xkXCI+QWRtaW4gUGFuZWw8L3NwYW4+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIHsvKiBOYXZpZ2F0aW9uIE1lbnUgKi99XG4gICAgICAgICAgPG5hdiBjbGFzc05hbWU9XCJmbGV4LTEgcHgtMyBweS00IHNwYWNlLXktMSBtaW4taC0wXCI+XG4gICAgICAgICAgICB7bmF2aWdhdGlvbkl0ZW1zLm1hcCgoaXRlbSkgPT4ge1xuICAgICAgICAgICAgICBjb25zdCBJY29uID0gaXRlbS5pY29uO1xuICAgICAgICAgICAgICBjb25zdCBpc0FjdGl2ZSA9IGFjdGl2ZVRhYiA9PT0gaXRlbS5pZDtcblxuICAgICAgICAgICAgICByZXR1cm4gKFxuICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgIGtleT17aXRlbS5pZH1cbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgb25UYWJDaGFuZ2UoaXRlbS5pZCk7XG4gICAgICAgICAgICAgICAgICAgIHNldFNpZGViYXJPcGVuKGZhbHNlKTtcbiAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BcbiAgICAgICAgICAgICAgICAgICAgdy1mdWxsIGZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMyBweC0zIHB5LTIuNSByb3VuZGVkLWxnIHRleHQtbGVmdCBncm91cFxuICAgICAgICAgICAgICAgICAgICAke2lzQWN0aXZlXG4gICAgICAgICAgICAgICAgICAgICAgPyAnYmctYmx1ZS02MDAgdGV4dC13aGl0ZSBzaGFkb3ctbWQnXG4gICAgICAgICAgICAgICAgICAgICAgOiAndGV4dC1zbGF0ZS0zMDAnXG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgIGB9XG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgPEljb24gY2xhc3NOYW1lPXtgaC00IHctNCAke2lzQWN0aXZlID8gJ3RleHQtd2hpdGUnIDogJ3RleHQtc2xhdGUtNDAwJ31gfSAvPlxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gdGV4dC1zbVwiPntpdGVtLmxhYmVsfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgKTtcbiAgICAgICAgICAgIH0pfVxuICAgICAgICAgIDwvbmF2PlxuXG4gICAgICAgICAgey8qIEJhY2sgdG8gRGFzaGJvYXJkICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicHgtMyBweS0zIGJvcmRlci10IGJvcmRlci1zbGF0ZS03MDBcIj5cbiAgICAgICAgICAgIDxMaW5rXG4gICAgICAgICAgICAgIGhyZWY9XCIvZGFzaGJvYXJkXCJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMyBweC0zIHB5LTIuNSByb3VuZGVkLWxnIHRleHQtc2xhdGUtMzAwIGdyb3VwXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPEFycm93TGVmdCBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gdGV4dC1zbVwiPkJhY2sgdG8gRGFzaGJvYXJkPC9zcGFuPlxuICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qIFNpZGViYXIgRm9vdGVyICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicHgtMyBweS0zIGJvcmRlci10IGJvcmRlci1zbGF0ZS03MDAgYmctc2xhdGUtOTAwIGZsZXgtc2hyaW5rLTBcIj5cbiAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlTG9nb3V0fVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0zIHB4LTMgcHktMi41IHJvdW5kZWQtbGcgdGV4dC1zbGF0ZS0zMDAgZ3JvdXBcIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8TG9nT3V0IGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJmb250LW1lZGl1bSB0ZXh0LXNtXCI+TG9nb3V0PC9zcGFuPlxuICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9hc2lkZT5cblxuICAgICAgey8qIE1haW4gQ29udGVudCBBcmVhICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgZmxleCBmbGV4LWNvbCBtaW4tdy0wIGxnOm1sLTY0XCI+XG4gICAgICAgIHsvKiBUb3AgTmF2aWdhdGlvbiBCYXIgKi99XG4gICAgICAgIDxoZWFkZXIgY2xhc3NOYW1lPVwiYmctc2xhdGUtODAwIHNoYWRvdy1zbSBib3JkZXItYiBib3JkZXItc2xhdGUtNzAwIHN0aWNreSB0b3AtMCB6LTMwXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJweC00IHNtOnB4LTYgbGc6cHgtOCB4bDpweC0xMlwiPlxuICAgICAgICAgICAgPEZsZXgganVzdGlmeT1cImJldHdlZW5cIiBhbGlnbj1cImNlbnRlclwiIGNsYXNzTmFtZT1cImgtMTZcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTRcIj5cbiAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTaWRlYmFyT3Blbih0cnVlKX1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImxnOmhpZGRlbiBwLTIgcm91bmRlZC1sZyB0ZXh0LXNsYXRlLTQwMFwiXG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgPE1lbnUgY2xhc3NOYW1lPVwiaC02IHctNlwiIC8+XG4gICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC13aGl0ZSBjYXBpdGFsaXplIHRyYWNraW5nLXdpZGUgZHJvcC1zaGFkb3ctbGdcIj5cbiAgICAgICAgICAgICAgICAgICAge25hdmlnYXRpb25JdGVtcy5maW5kKGl0ZW0gPT4gaXRlbS5pZCA9PT0gYWN0aXZlVGFiKT8ubGFiZWwgfHwgJ0FkbWluIERhc2hib2FyZCd9XG4gICAgICAgICAgICAgICAgICA8L2gxPlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LXNsYXRlLTMwMCBoaWRkZW4gc206YmxvY2sgZm9udC1tZWRpdW1cIj5cbiAgICAgICAgICAgICAgICAgICAgTWFuYWdlIHBsYXRmb3JtIG9wZXJhdGlvbnMgYW5kIHVzZXIgYWN0aXZpdGllc1xuICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtM1wiPlxuICAgICAgICAgICAgICAgIHsvKiBOb3RpZmljYXRpb25zICovfVxuICAgICAgICAgICAgICAgIDxidXR0b24gY2xhc3NOYW1lPVwicmVsYXRpdmUgcC0yIHJvdW5kZWQtbGcgdGV4dC1zbGF0ZS00MDBcIj5cbiAgICAgICAgICAgICAgICAgIDxCZWxsIGNsYXNzTmFtZT1cImgtNSB3LTVcIiAvPlxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiYWJzb2x1dGUgdG9wLTEgcmlnaHQtMSBoLTIgdy0yIGJnLW9yYW5nZS01MDAgcm91bmRlZC1mdWxsXCI+PC9zcGFuPlxuICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuXG4gICAgICAgICAgICAgICAgey8qIEFkbWluIEJhZGdlICovfVxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicHgtMyBweS0xLjUgcm91bmRlZC1sZyB0ZXh0LXhzIGZvbnQtc2VtaWJvbGQgYm9yZGVyIGJnLXJlZC02MDAgdGV4dC13aGl0ZSBib3JkZXItcmVkLTUwMFwiPlxuICAgICAgICAgICAgICAgICAgQURNSU5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgIHsvKiBVc2VyIERyb3Bkb3duICovfVxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmVcIiByZWY9e2Ryb3Bkb3duUmVmfT5cbiAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0VXNlckRyb3Bkb3duT3BlbighdXNlckRyb3Bkb3duT3Blbil9XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMiBwLTEgcm91bmRlZC1sZ1wiXG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIDxQcm9maWxlSW1hZ2VcbiAgICAgICAgICAgICAgICAgICAgICBzcmM9e3VzZXI/LnByb2ZpbGVQaWN0dXJlfVxuICAgICAgICAgICAgICAgICAgICAgIGFsdD1cIlByb2ZpbGVcIlxuICAgICAgICAgICAgICAgICAgICAgIHNpemU9ezMyfVxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInJvdW5kZWQtbGdcIlxuICAgICAgICAgICAgICAgICAgICAgIGZhbGxiYWNrVGV4dD17dXNlcj8uZmlyc3ROYW1lPy5jaGFyQXQoMCkudG9VcHBlckNhc2UoKSB8fCB1c2VyPy5lbWFpbC5jaGFyQXQoMCkudG9VcHBlckNhc2UoKX1cbiAgICAgICAgICAgICAgICAgICAgICBmYWxsYmFja0JnQ29sb3I9XCJiZy1vcmFuZ2UtNjAwXCJcbiAgICAgICAgICAgICAgICAgICAgICBsb2FkaW5nPVwibGF6eVwiXG4gICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgIDxDaGV2cm9uRG93biBjbGFzc05hbWU9e2BoLTQgdy00IHRleHQtc2xhdGUtNDAwIHRyYW5zaXRpb24tdHJhbnNmb3JtICR7dXNlckRyb3Bkb3duT3BlbiA/ICdyb3RhdGUtMTgwJyA6ICcnfWB9IC8+XG4gICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cblxuICAgICAgICAgICAgICAgICAgey8qIERyb3Bkb3duIE1lbnUgKi99XG4gICAgICAgICAgICAgICAgICB7dXNlckRyb3Bkb3duT3BlbiAmJiAoXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgcmlnaHQtMCBtdC0yIHctNjQgYmctc2xhdGUtODAwIHJvdW5kZWQteGwgc2hhZG93LWxnIGJvcmRlciBib3JkZXItc2xhdGUtNzAwIHB5LTIgei01MFwiPlxuICAgICAgICAgICAgICAgICAgICAgIHsvKiBVc2VyIEluZm8gKi99XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJweC00IHB5LTMgYm9yZGVyLWIgYm9yZGVyLXNsYXRlLTcwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTNcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPFByb2ZpbGVJbWFnZVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNyYz17dXNlcj8ucHJvZmlsZVBpY3R1cmV9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgYWx0PVwiUHJvZmlsZVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc2l6ZT17NDB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicm91bmRlZC1sZ1wiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZmFsbGJhY2tUZXh0PXt1c2VyPy5maXJzdE5hbWU/LmNoYXJBdCgwKS50b1VwcGVyQ2FzZSgpIHx8IHVzZXI/LmVtYWlsLmNoYXJBdCgwKS50b1VwcGVyQ2FzZSgpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZhbGxiYWNrQmdDb2xvcj1cImJnLW9yYW5nZS02MDBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxvYWRpbmc9XCJsYXp5XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgbWluLXctMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1zZW1pYm9sZCB0ZXh0LXdoaXRlIHRydW5jYXRlXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7dXNlcj8uZmlyc3ROYW1lICYmIHVzZXI/Lmxhc3ROYW1lXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gYCR7dXNlci5maXJzdE5hbWV9ICR7dXNlci5sYXN0TmFtZX1gXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogdXNlcj8uZW1haWwuc3BsaXQoJ0AnKVswXVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtc2xhdGUtNDAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBJRDoge3VzZXI/LnJlZmVycmFsSWR9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1yZWQtNDAwIGZvbnQtbWVkaXVtXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBBZG1pbmlzdHJhdG9yXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgICAgey8qIE1lbnUgSXRlbXMgKi99XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJweS0xXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8TGlua1xuICAgICAgICAgICAgICAgICAgICAgICAgICBocmVmPVwiL2Rhc2hib2FyZFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFVzZXJEcm9wZG93bk9wZW4oZmFsc2UpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0zIHB4LTQgcHktMiB0ZXh0LXNtIHRleHQtc2xhdGUtMzAwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPFVzZXIgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPlVzZXIgRGFzaGJvYXJkPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJib3JkZXItdCBib3JkZXItc2xhdGUtNzAwIG15LTFcIj48L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNldFVzZXJEcm9wZG93bk9wZW4oZmFsc2UpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhhbmRsZUxvZ291dCgpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0zIHB4LTQgcHktMiB0ZXh0LXNtIHRleHQtcmVkLTQwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxMb2dPdXQgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPlNpZ24gT3V0PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L0ZsZXg+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvaGVhZGVyPlxuXG4gICAgICAgIHsvKiBNYWluIENvbnRlbnQgKi99XG4gICAgICAgIDxtYWluIGNsYXNzTmFtZT1cImZsZXgtMSBiZy1zbGF0ZS05MDAgb3ZlcmZsb3cteS1hdXRvIHJlbGF0aXZlXCI+XG4gICAgICAgICAgey8qIEZpeGVkIEJhY2tncm91bmQgSW1hZ2UgKi99XG4gICAgICAgICAgPGRpdlxuICAgICAgICAgICAgY2xhc3NOYW1lPVwiZml4ZWQgaW5zZXQtMCBiZy1jb3ZlciBiZy1jZW50ZXIgYmctbm8tcmVwZWF0IG9wYWNpdHktMTAgcG9pbnRlci1ldmVudHMtbm9uZVwiXG4gICAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgICBiYWNrZ3JvdW5kSW1hZ2U6ICd1cmwoL2FkbWluX2JhY2tncm91bmQuanBnKScsXG4gICAgICAgICAgICAgIGJhY2tncm91bmRBdHRhY2htZW50OiAnZml4ZWQnLFxuICAgICAgICAgICAgICB6SW5kZXg6IDAsXG4gICAgICAgICAgICB9fVxuICAgICAgICAgIC8+XG4gICAgICAgICAgey8qIENvbnRlbnQgT3ZlcmxheSAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIHotMTAgcHgtNCBzbTpweC02IGxnOnB4LTggeGw6cHgtMTIgcHktNlwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy03eGwgbXgtYXV0b1wiPlxuICAgICAgICAgICAgICB7Y2hpbGRyZW59XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9tYWluPlxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gICk7XG59O1xuIl0sIm5hbWVzIjpbIlJlYWN0IiwidXNlU3RhdGUiLCJ1c2VSZWYiLCJ1c2VFZmZlY3QiLCJMaW5rIiwidXNlQXV0aCIsIkZsZXgiLCJQcm9maWxlSW1hZ2UiLCJTb2xhclBhbmVsIiwiTGF5b3V0RGFzaGJvYXJkIiwiVXNlcnMiLCJTaGllbGQiLCJDcmVkaXRDYXJkIiwiU2V0dGluZ3MiLCJGaWxlVGV4dCIsIkxvZ091dCIsIk1lbnUiLCJYIiwiQmVsbCIsIkNoZXZyb25Eb3duIiwiVXNlciIsIkFycm93TGVmdCIsIkRvbGxhclNpZ24iLCJNZXNzYWdlQ2lyY2xlIiwiQXJyb3dVcERvd24iLCJUcmVuZGluZ1VwIiwiTWFpbCIsIkFkbWluTGF5b3V0IiwiY2hpbGRyZW4iLCJhY3RpdmVUYWIiLCJvblRhYkNoYW5nZSIsIm5hdmlnYXRpb25JdGVtcyIsInVzZXIiLCJsb2dvdXQiLCJzaWRlYmFyT3BlbiIsInNldFNpZGViYXJPcGVuIiwidXNlckRyb3Bkb3duT3BlbiIsInNldFVzZXJEcm9wZG93bk9wZW4iLCJkcm9wZG93blJlZiIsImlkIiwibGFiZWwiLCJpY29uIiwiaGFuZGxlTG9nb3V0IiwiaGFuZGxlQ2xpY2tPdXRzaWRlIiwiZXZlbnQiLCJjdXJyZW50IiwiY29udGFpbnMiLCJ0YXJnZXQiLCJkb2N1bWVudCIsImFkZEV2ZW50TGlzdGVuZXIiLCJyZW1vdmVFdmVudExpc3RlbmVyIiwiZGl2IiwiY2xhc3NOYW1lIiwiZGF0YS1hZG1pbi1wYW5lbCIsIm9uQ2xpY2siLCJhc2lkZSIsImhyZWYiLCJzcGFuIiwiYnV0dG9uIiwibmF2IiwibWFwIiwiaXRlbSIsIkljb24iLCJpc0FjdGl2ZSIsImhlYWRlciIsImp1c3RpZnkiLCJhbGlnbiIsImgxIiwiZmluZCIsInAiLCJyZWYiLCJzcmMiLCJwcm9maWxlUGljdHVyZSIsImFsdCIsInNpemUiLCJmYWxsYmFja1RleHQiLCJmaXJzdE5hbWUiLCJjaGFyQXQiLCJ0b1VwcGVyQ2FzZSIsImVtYWlsIiwiZmFsbGJhY2tCZ0NvbG9yIiwibG9hZGluZyIsImxhc3ROYW1lIiwic3BsaXQiLCJyZWZlcnJhbElkIiwibWFpbiIsInN0eWxlIiwiYmFja2dyb3VuZEltYWdlIiwiYmFja2dyb3VuZEF0dGFjaG1lbnQiLCJ6SW5kZXgiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/AdminLayout.tsx\n"));

/***/ })

});