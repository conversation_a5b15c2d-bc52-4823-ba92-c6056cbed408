"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./src/components/admin/DepositManagement.tsx":
/*!****************************************************!*\
  !*** ./src/components/admin/DepositManagement.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DepositManagement: () => (/* binding */ DepositManagement)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Check_CheckCircle_Clock_Copy_DollarSign_Eye_Filter_Info_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Check,CheckCircle,Clock,Copy,DollarSign,Eye,Filter,Info,RefreshCw,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Check_CheckCircle_Clock_Copy_DollarSign_Eye_Filter_Info_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Check,CheckCircle,Clock,Copy,DollarSign,Eye,Filter,Info,RefreshCw,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Check_CheckCircle_Clock_Copy_DollarSign_Eye_Filter_Info_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Check,CheckCircle,Clock,Copy,DollarSign,Eye,Filter,Info,RefreshCw,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Check_CheckCircle_Clock_Copy_DollarSign_Eye_Filter_Info_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Check,CheckCircle,Clock,Copy,DollarSign,Eye,Filter,Info,RefreshCw,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Check_CheckCircle_Clock_Copy_DollarSign_Eye_Filter_Info_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Check,CheckCircle,Clock,Copy,DollarSign,Eye,Filter,Info,RefreshCw,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Check_CheckCircle_Clock_Copy_DollarSign_Eye_Filter_Info_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Check,CheckCircle,Clock,Copy,DollarSign,Eye,Filter,Info,RefreshCw,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Check_CheckCircle_Clock_Copy_DollarSign_Eye_Filter_Info_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Check,CheckCircle,Clock,Copy,DollarSign,Eye,Filter,Info,RefreshCw,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/funnel.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Check_CheckCircle_Clock_Copy_DollarSign_Eye_Filter_Info_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Check,CheckCircle,Clock,Copy,DollarSign,Eye,Filter,Info,RefreshCw,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Check_CheckCircle_Clock_Copy_DollarSign_Eye_Filter_Info_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Check,CheckCircle,Clock,Copy,DollarSign,Eye,Filter,Info,RefreshCw,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Check_CheckCircle_Clock_Copy_DollarSign_Eye_Filter_Info_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Check,CheckCircle,Clock,Copy,DollarSign,Eye,Filter,Info,RefreshCw,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Check_CheckCircle_Clock_Copy_DollarSign_Eye_Filter_Info_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Check,CheckCircle,Clock,Copy,DollarSign,Eye,Filter,Info,RefreshCw,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ DepositManagement auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst DepositManagement = ()=>{\n    var _selectedDeposit_user, _selectedDeposit_user1;\n    _s();\n    const [deposits, setDeposits] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalDeposits: 0,\n        totalAmount: 0,\n        pendingDeposits: 0\n    });\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedStatus, setSelectedStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('ALL');\n    const [selectedDeposit, setSelectedDeposit] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [actionLoading, setActionLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [copiedField, setCopiedField] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const fetchDeposits = async ()=>{\n        try {\n            setLoading(true);\n            const params = new URLSearchParams();\n            if (selectedStatus !== 'ALL') {\n                params.append('status', selectedStatus);\n            }\n            params.append('limit', '50');\n            const response = await fetch(\"/api/admin/deposits?\".concat(params), {\n                credentials: 'include'\n            });\n            if (!response.ok) {\n                throw new Error('Failed to fetch deposits');\n            }\n            const data = await response.json();\n            if (data.success) {\n                setDeposits(data.data.deposits);\n                setStats(data.data.stats);\n            } else {\n                throw new Error(data.error || 'Failed to fetch deposits');\n            }\n        } catch (err) {\n            setError(err instanceof Error ? err.message : 'An error occurred');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleCopyToClipboard = async (text, fieldName)=>{\n        try {\n            await navigator.clipboard.writeText(text);\n            setCopiedField(fieldName);\n            setTimeout(()=>setCopiedField(null), 2000);\n        } catch (err) {\n            console.error('Failed to copy to clipboard:', err);\n        }\n    };\n    const handleDepositAction = async (transactionId, action, reason)=>{\n        try {\n            setActionLoading(transactionId);\n            const response = await fetch('/api/admin/deposits', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                credentials: 'include',\n                body: JSON.stringify({\n                    action,\n                    transactionId,\n                    reason\n                })\n            });\n            if (!response.ok) {\n                throw new Error('Failed to process deposit action');\n            }\n            const data = await response.json();\n            if (data.success) {\n                // Refresh deposits list\n                await fetchDeposits();\n                setSelectedDeposit(null);\n            } else {\n                throw new Error(data.error || 'Failed to process deposit action');\n            }\n        } catch (err) {\n            setError(err instanceof Error ? err.message : 'An error occurred');\n        } finally{\n            setActionLoading(null);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DepositManagement.useEffect\": ()=>{\n            fetchDeposits();\n        }\n    }[\"DepositManagement.useEffect\"], [\n        selectedStatus\n    ]);\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case 'COMPLETED':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Check_CheckCircle_Clock_Copy_DollarSign_Eye_Filter_Info_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"w-4 h-4 text-green-400\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                    lineNumber: 128,\n                    columnNumber: 16\n                }, undefined);\n            case 'PENDING':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Check_CheckCircle_Clock_Copy_DollarSign_Eye_Filter_Info_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"w-4 h-4 text-yellow-400\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                    lineNumber: 130,\n                    columnNumber: 16\n                }, undefined);\n            case 'FAILED':\n            case 'REJECTED':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Check_CheckCircle_Clock_Copy_DollarSign_Eye_Filter_Info_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"w-4 h-4 text-red-400\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                    lineNumber: 133,\n                    columnNumber: 16\n                }, undefined);\n            case 'VERIFYING':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Check_CheckCircle_Clock_Copy_DollarSign_Eye_Filter_Info_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"w-4 h-4 text-blue-400\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                    lineNumber: 135,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Check_CheckCircle_Clock_Copy_DollarSign_Eye_Filter_Info_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"w-4 h-4 text-gray-400\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                    lineNumber: 137,\n                    columnNumber: 16\n                }, undefined);\n        }\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case 'COMPLETED':\n                return 'text-green-400 bg-green-400/10';\n            case 'PENDING':\n                return 'text-yellow-400 bg-yellow-400/10';\n            case 'FAILED':\n            case 'REJECTED':\n                return 'text-red-400 bg-red-400/10';\n            case 'VERIFYING':\n                return 'text-blue-400 bg-blue-400/10';\n            default:\n                return 'text-gray-400 bg-gray-400/10';\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Check_CheckCircle_Clock_Copy_DollarSign_Eye_Filter_Info_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                className: \"w-8 h-8 animate-spin text-blue-400\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                lineNumber: 160,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n            lineNumber: 159,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-end\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: fetchDeposits,\n                    className: \"flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Check_CheckCircle_Clock_Copy_DollarSign_Eye_Filter_Info_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            className: \"w-4 h-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                            lineNumber: 173,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"hidden sm:inline\",\n                            children: \"Refresh\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                            lineNumber: 174,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                    lineNumber: 169,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                lineNumber: 168,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"bg-gray-800 border-gray-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-400 text-sm\",\n                                                children: \"Total Deposits\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                lineNumber: 184,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-white\",\n                                                children: stats.totalDeposits\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                lineNumber: 185,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Check_CheckCircle_Clock_Copy_DollarSign_Eye_Filter_Info_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-8 h-8 text-green-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                            lineNumber: 181,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"bg-gray-800 border-gray-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-400 text-sm\",\n                                                children: \"Total Amount\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                lineNumber: 196,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-white\",\n                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.formatCurrency)(stats.totalAmount)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                lineNumber: 197,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Check_CheckCircle_Clock_Copy_DollarSign_Eye_Filter_Info_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-8 h-8 text-blue-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                        lineNumber: 199,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                            lineNumber: 193,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                        lineNumber: 192,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"bg-gray-800 border-gray-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-400 text-sm\",\n                                                children: \"Pending Deposits\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-white\",\n                                                children: stats.pendingDeposits\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                lineNumber: 209,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Check_CheckCircle_Clock_Copy_DollarSign_Eye_Filter_Info_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-8 h-8 text-yellow-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                            lineNumber: 205,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                        lineNumber: 204,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                lineNumber: 179,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"bg-gray-800 border-gray-700\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"p-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row items-start sm:items-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Check_CheckCircle_Clock_Copy_DollarSign_Eye_Filter_Info_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"w-5 h-5 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-400 text-sm font-medium\",\n                                        children: \"Filter by Status:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                        lineNumber: 223,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                value: selectedStatus,\n                                onChange: (e)=>setSelectedStatus(e.target.value),\n                                className: \"w-full sm:w-auto bg-gray-700 border border-gray-600 text-white rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"ALL\",\n                                        children: \"All Status\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                        lineNumber: 230,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"PENDING\",\n                                        children: \"Pending\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                        lineNumber: 231,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"VERIFYING\",\n                                        children: \"Verifying\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                        lineNumber: 232,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"CONFIRMED\",\n                                        children: \"Confirmed\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"COMPLETED\",\n                                        children: \"Completed\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                        lineNumber: 234,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"FAILED\",\n                                        children: \"Failed\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                        lineNumber: 235,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"REJECTED\",\n                                        children: \"Rejected\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                lineNumber: 225,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                        lineNumber: 220,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                    lineNumber: 219,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                lineNumber: 218,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"bg-gray-800 border-gray-700\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"text-white\",\n                            children: \"Recent Deposits\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                            lineNumber: 245,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                        lineNumber: 244,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: [\n                            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4 p-4 bg-red-900/20 border border-red-500 rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-400\",\n                                    children: error\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                    lineNumber: 250,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden lg:block overflow-x-auto\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                    className: \"w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                className: \"border-b border-gray-700\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-left py-3 px-4 text-gray-400 font-medium\",\n                                                        children: \"User\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                        lineNumber: 259,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-left py-3 px-4 text-gray-400 font-medium\",\n                                                        children: \"Amount\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                        lineNumber: 260,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-left py-3 px-4 text-gray-400 font-medium\",\n                                                        children: \"Status\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                        lineNumber: 261,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-left py-3 px-4 text-gray-400 font-medium\",\n                                                        children: \"Transaction ID\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                        lineNumber: 262,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-left py-3 px-4 text-gray-400 font-medium\",\n                                                        children: \"Date\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                        lineNumber: 263,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-left py-3 px-4 text-gray-400 font-medium\",\n                                                        children: \"Actions\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                        lineNumber: 264,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                lineNumber: 258,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                            lineNumber: 257,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                            children: deposits.map((deposit)=>{\n                                                var _deposit_user;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"border-b border-gray-700/50 hover:bg-gray-700/30\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-white\",\n                                                                    children: deposit.user ? \"\".concat(deposit.user.firstName, \" \").concat(deposit.user.lastName) : 'Unknown'\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                                    lineNumber: 271,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm text-gray-400\",\n                                                                    children: ((_deposit_user = deposit.user) === null || _deposit_user === void 0 ? void 0 : _deposit_user.email) || 'No email'\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                                    lineNumber: 274,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                            lineNumber: 270,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-white font-medium\",\n                                                                    children: [\n                                                                        (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.formatCurrency)(deposit.usdtAmount),\n                                                                        \" USDT\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                                    lineNumber: 279,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm text-gray-400\",\n                                                                    children: [\n                                                                        deposit.confirmations,\n                                                                        \" confirmations\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                                    lineNumber: 282,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                            lineNumber: 278,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"inline-flex items-center space-x-2 px-2 py-1 rounded-full text-xs font-medium \".concat(getStatusColor(deposit.status)),\n                                                                children: [\n                                                                    getStatusIcon(deposit.status),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: deposit.status\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                                        lineNumber: 289,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                                lineNumber: 287,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                            lineNumber: 286,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-white font-mono text-sm\",\n                                                                children: [\n                                                                    deposit.transactionId.slice(0, 8),\n                                                                    \"...\",\n                                                                    deposit.transactionId.slice(-8)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                                lineNumber: 293,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                            lineNumber: 292,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4 text-gray-300\",\n                                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.formatDateTime)(deposit.createdAt)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                            lineNumber: 297,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>setSelectedDeposit(deposit),\n                                                                className: \"flex items-center space-x-1 px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white rounded text-sm transition-colors\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Check_CheckCircle_Clock_Copy_DollarSign_Eye_Filter_Info_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        className: \"w-4 h-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                                        lineNumber: 305,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"View\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                                        lineNumber: 306,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                                lineNumber: 301,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                            lineNumber: 300,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, deposit.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                    lineNumber: 269,\n                                                    columnNumber: 19\n                                                }, undefined);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                            lineNumber: 267,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                    lineNumber: 256,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:hidden space-y-4\",\n                                children: deposits.map((deposit)=>{\n                                    var _deposit_user;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-700 rounded-lg p-4 border border-gray-600\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-white font-medium\",\n                                                                children: deposit.user ? \"\".concat(deposit.user.firstName, \" \").concat(deposit.user.lastName) : 'Unknown'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                                lineNumber: 322,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-400\",\n                                                                children: ((_deposit_user = deposit.user) === null || _deposit_user === void 0 ? void 0 : _deposit_user.email) || 'No email'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                                lineNumber: 325,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                        lineNumber: 321,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"inline-flex items-center space-x-2 px-2 py-1 rounded-full text-xs font-medium \".concat(getStatusColor(deposit.status)),\n                                                        children: [\n                                                            getStatusIcon(deposit.status),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: deposit.status\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                                lineNumber: 331,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                        lineNumber: 329,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                lineNumber: 320,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-2 gap-3 text-sm mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-gray-400 text-xs mb-1\",\n                                                                children: \"Amount\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                                lineNumber: 338,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-white font-medium\",\n                                                                children: [\n                                                                    (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.formatCurrency)(deposit.usdtAmount),\n                                                                    \" USDT\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                                lineNumber: 339,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-400\",\n                                                                children: [\n                                                                    deposit.confirmations,\n                                                                    \" confirmations\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                                lineNumber: 342,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                        lineNumber: 337,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-gray-400 text-xs mb-1\",\n                                                                children: \"Date\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                                lineNumber: 347,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-gray-300 text-xs\",\n                                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.formatDateTime)(deposit.createdAt)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                                lineNumber: 348,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                        lineNumber: 346,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                lineNumber: 336,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-gray-400 text-xs mb-1\",\n                                                        children: \"Transaction ID\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                        lineNumber: 356,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-white font-mono text-xs bg-gray-800 p-2 rounded border\",\n                                                        children: [\n                                                            deposit.transactionId.slice(0, 8),\n                                                            \"...\",\n                                                            deposit.transactionId.slice(-8)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                        lineNumber: 357,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                lineNumber: 355,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setSelectedDeposit(deposit),\n                                                className: \"w-full flex items-center justify-center space-x-2 px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded text-sm transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Check_CheckCircle_Clock_Copy_DollarSign_Eye_Filter_Info_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                        lineNumber: 367,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"View Details\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                        lineNumber: 368,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                lineNumber: 363,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, deposit.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                        lineNumber: 318,\n                                        columnNumber: 15\n                                    }, undefined);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                lineNumber: 316,\n                                columnNumber: 11\n                            }, undefined),\n                            deposits.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8 text-gray-400\",\n                                children: \"No deposits found\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                lineNumber: 375,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                        lineNumber: 247,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                lineNumber: 243,\n                columnNumber: 7\n            }, undefined),\n            selectedDeposit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gray-800 border border-gray-700 rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-bold text-white\",\n                                    children: \"Deposit Details\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                    lineNumber: 387,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setSelectedDeposit(null),\n                                    className: \"text-gray-400 hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Check_CheckCircle_Clock_Copy_DollarSign_Eye_Filter_Info_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"w-6 h-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                        lineNumber: 392,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                    lineNumber: 388,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                            lineNumber: 386,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"text-gray-400 text-sm\",\n                                                    children: \"User\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                    lineNumber: 399,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-white\",\n                                                    children: selectedDeposit.user ? \"\".concat(selectedDeposit.user.firstName, \" \").concat(selectedDeposit.user.lastName) : 'Unknown'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                    lineNumber: 400,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-400 text-sm flex-1\",\n                                                            children: (_selectedDeposit_user = selectedDeposit.user) === null || _selectedDeposit_user === void 0 ? void 0 : _selectedDeposit_user.email\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                            lineNumber: 404,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        ((_selectedDeposit_user1 = selectedDeposit.user) === null || _selectedDeposit_user1 === void 0 ? void 0 : _selectedDeposit_user1.email) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>{\n                                                                var _selectedDeposit_user;\n                                                                return handleCopyToClipboard(((_selectedDeposit_user = selectedDeposit.user) === null || _selectedDeposit_user === void 0 ? void 0 : _selectedDeposit_user.email) || '', 'email');\n                                                            },\n                                                            className: \"p-1 text-gray-400 hover:text-white transition-colors\",\n                                                            title: \"Copy email\",\n                                                            children: copiedField === 'email' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Check_CheckCircle_Clock_Copy_DollarSign_Eye_Filter_Info_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"w-4 h-4 text-green-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                                lineNumber: 412,\n                                                                columnNumber: 27\n                                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Check_CheckCircle_Clock_Copy_DollarSign_Eye_Filter_Info_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                                lineNumber: 414,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                            lineNumber: 406,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                    lineNumber: 403,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                            lineNumber: 398,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"text-gray-400 text-sm\",\n                                                    children: \"Amount\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                    lineNumber: 421,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-white font-medium\",\n                                                    children: [\n                                                        (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.formatCurrency)(selectedDeposit.usdtAmount),\n                                                        \" USDT\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                    lineNumber: 422,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                            lineNumber: 420,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                    lineNumber: 397,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"text-gray-400 text-sm\",\n                                                    children: \"Status\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                    lineNumber: 428,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"inline-flex items-center space-x-2 px-2 py-1 rounded-full text-xs font-medium \".concat(getStatusColor(selectedDeposit.status)),\n                                                    children: [\n                                                        getStatusIcon(selectedDeposit.status),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: selectedDeposit.status\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                            lineNumber: 431,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                    lineNumber: 429,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                            lineNumber: 427,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"text-gray-400 text-sm\",\n                                                    children: \"Confirmations\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                    lineNumber: 435,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-white\",\n                                                    children: selectedDeposit.confirmations\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                    lineNumber: 436,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                            lineNumber: 434,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                    lineNumber: 426,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"text-gray-400 text-sm\",\n                                            children: \"Transaction ID\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                            lineNumber: 441,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-white font-mono text-sm break-all flex-1\",\n                                                    children: selectedDeposit.transactionId\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                    lineNumber: 443,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleCopyToClipboard(selectedDeposit.transactionId, 'transactionId'),\n                                                    className: \"p-1 text-gray-400 hover:text-white transition-colors flex-shrink-0\",\n                                                    title: \"Copy transaction ID\",\n                                                    children: copiedField === 'transactionId' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Check_CheckCircle_Clock_Copy_DollarSign_Eye_Filter_Info_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"w-4 h-4 text-green-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                        lineNumber: 450,\n                                                        columnNumber: 23\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Check_CheckCircle_Clock_Copy_DollarSign_Eye_Filter_Info_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                        lineNumber: 452,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                    lineNumber: 444,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                            lineNumber: 442,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                    lineNumber: 440,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"text-gray-400 text-sm\",\n                                                    children: \"Sender Address\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                    lineNumber: 460,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-white font-mono text-sm break-all\",\n                                                    children: selectedDeposit.senderAddress || 'N/A'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                    lineNumber: 461,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                            lineNumber: 459,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"text-gray-400 text-sm\",\n                                                    children: \"Deposit Address\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                    lineNumber: 466,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-white font-mono text-sm break-all\",\n                                                    children: selectedDeposit.tronAddress\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                    lineNumber: 467,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                            lineNumber: 465,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                    lineNumber: 458,\n                                    columnNumber: 15\n                                }, undefined),\n                                selectedDeposit.failureReason && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"text-gray-400 text-sm\",\n                                            children: \"Failure Reason\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                            lineNumber: 473,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-red-400\",\n                                            children: selectedDeposit.failureReason\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                            lineNumber: 474,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                    lineNumber: 472,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"text-gray-400 text-sm\",\n                                                    children: \"Created At\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                    lineNumber: 480,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-white\",\n                                                    children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.formatDateTime)(selectedDeposit.createdAt)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                    lineNumber: 481,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                            lineNumber: 479,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"text-gray-400 text-sm\",\n                                                    children: \"Processed At\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                    lineNumber: 484,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-white\",\n                                                    children: selectedDeposit.processedAt ? (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.formatDateTime)(selectedDeposit.processedAt) : 'Not processed'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                    lineNumber: 485,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                            lineNumber: 483,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                    lineNumber: 478,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                            lineNumber: 396,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6 pt-6 border-t border-gray-700\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-900/50 border border-blue-700 rounded-lg p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Check_CheckCircle_Clock_Copy_DollarSign_Eye_Filter_Info_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"w-5 h-5 text-blue-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                            lineNumber: 496,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-blue-300 font-medium\",\n                                                    children: \"Automated Processing\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                    lineNumber: 498,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-blue-200 text-sm mt-1\",\n                                                    children: \"Deposits are now processed automatically. The system verifies transactions and credits wallets once confirmations are met.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                    lineNumber: 499,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                            lineNumber: 497,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                    lineNumber: 495,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                lineNumber: 494,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                            lineNumber: 493,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                    lineNumber: 385,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                lineNumber: 384,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n        lineNumber: 166,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DepositManagement, \"WvKeujTlw9xddF3J5qUG41AMNmw=\");\n_c = DepositManagement;\nvar _c;\n$RefreshReg$(_c, \"DepositManagement\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/DepositManagement.tsx\n"));

/***/ })

});