"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./src/app/admin/page.tsx":
/*!********************************!*\
  !*** ./src/app/admin/page.tsx ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdminPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useAuth */ \"(app-pages-browser)/./src/hooks/useAuth.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_admin_AdminLayout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/admin/AdminLayout */ \"(app-pages-browser)/./src/components/admin/AdminLayout.tsx\");\n/* harmony import */ var _components_admin_AdminDashboard__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/admin/AdminDashboard */ \"(app-pages-browser)/./src/components/admin/AdminDashboard.tsx\");\n/* harmony import */ var _components_admin_UserManagement__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/admin/UserManagement */ \"(app-pages-browser)/./src/components/admin/UserManagement.tsx\");\n/* harmony import */ var _components_admin_KYCReview__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/admin/KYCReview */ \"(app-pages-browser)/./src/components/admin/KYCReview.tsx\");\n/* harmony import */ var _components_admin_DepositManagement__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/admin/DepositManagement */ \"(app-pages-browser)/./src/components/admin/DepositManagement.tsx\");\n/* harmony import */ var _components_admin_WithdrawalManagement__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/admin/WithdrawalManagement */ \"(app-pages-browser)/./src/components/admin/WithdrawalManagement.tsx\");\n/* harmony import */ var _components_admin_SupportTicketManagement__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/admin/SupportTicketManagement */ \"(app-pages-browser)/./src/components/admin/SupportTicketManagement.tsx\");\n/* harmony import */ var _components_admin_BinaryPointsManagement__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/admin/BinaryPointsManagement */ \"(app-pages-browser)/./src/components/admin/BinaryPointsManagement.tsx\");\n/* harmony import */ var _components_admin_ReferralCommissionTracking__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/admin/ReferralCommissionTracking */ \"(app-pages-browser)/./src/components/admin/ReferralCommissionTracking.tsx\");\n/* harmony import */ var _components_admin_SystemSettings__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/admin/SystemSettings */ \"(app-pages-browser)/./src/components/admin/SystemSettings.tsx\");\n/* harmony import */ var _components_admin_SystemLogs__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/admin/SystemLogs */ \"(app-pages-browser)/./src/components/admin/SystemLogs.tsx\");\n/* harmony import */ var _components_admin_EmailSettings__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/admin/EmailSettings */ \"(app-pages-browser)/./src/components/admin/EmailSettings.tsx\");\n/* harmony import */ var _components_admin_MiningManagement__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/admin/MiningManagement */ \"(app-pages-browser)/./src/components/admin/MiningManagement.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction AdminPage() {\n    _s();\n    const { user, loading } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('dashboard');\n    const [isAdmin, setIsAdmin] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null); // null = not checked yet\n    const [checkingAdmin, setCheckingAdmin] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Check if user is admin\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminPage.useEffect\": ()=>{\n            const checkAdminStatus = {\n                \"AdminPage.useEffect.checkAdminStatus\": async ()=>{\n                    if (!loading && user) {\n                        try {\n                            const response = await fetch('/api/admin/check', {\n                                credentials: 'include'\n                            });\n                            if (response.ok) {\n                                const data = await response.json();\n                                setIsAdmin(data.isAdmin);\n                            } else {\n                                setIsAdmin(false);\n                            }\n                        } catch (error) {\n                            console.error('Error checking admin status:', error);\n                            setIsAdmin(false);\n                        } finally{\n                            setCheckingAdmin(false);\n                        }\n                    } else if (!loading && !user) {\n                        // If no user, stop checking admin status\n                        setCheckingAdmin(false);\n                        setIsAdmin(false);\n                    }\n                }\n            }[\"AdminPage.useEffect.checkAdminStatus\"];\n            // Only check admin status if we have a user or if loading is complete\n            if (!loading) {\n                checkAdminStatus();\n            }\n        }\n    }[\"AdminPage.useEffect\"], [\n        user,\n        loading\n    ]);\n    // Redirect if not authenticated\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminPage.useEffect\": ()=>{\n            if (!loading && !user) {\n                router.push('/login');\n            }\n        }\n    }[\"AdminPage.useEffect\"], [\n        user,\n        loading,\n        router\n    ]);\n    // Redirect if not admin (only after admin check is complete)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminPage.useEffect\": ()=>{\n            // Only redirect if admin check is complete and user is explicitly not admin\n            if (!checkingAdmin && !loading && user && isAdmin === false) {\n                router.push('/dashboard');\n            }\n        }\n    }[\"AdminPage.useEffect\"], [\n        isAdmin,\n        checkingAdmin,\n        user,\n        loading,\n        router\n    ]);\n    if (loading || checkingAdmin) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_17__.Loading, {\n                size: \"lg\",\n                text: \"Loading admin panel...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 80,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\admin\\\\page.tsx\",\n            lineNumber: 79,\n            columnNumber: 7\n        }, this);\n    }\n    if (!user || isAdmin === false) {\n        return null;\n    }\n    const renderTabContent = ()=>{\n        switch(activeTab){\n            case 'dashboard':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_AdminDashboard__WEBPACK_IMPORTED_MODULE_5__.AdminDashboard, {\n                    onTabChange: setActiveTab\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 92,\n                    columnNumber: 16\n                }, this);\n            case 'users':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_UserManagement__WEBPACK_IMPORTED_MODULE_6__.UserManagement, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 94,\n                    columnNumber: 16\n                }, this);\n            case 'kyc':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_KYCReview__WEBPACK_IMPORTED_MODULE_7__.KYCReview, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 96,\n                    columnNumber: 16\n                }, this);\n            case 'deposits':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_DepositManagement__WEBPACK_IMPORTED_MODULE_8__.DepositManagement, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 98,\n                    columnNumber: 16\n                }, this);\n            case 'withdrawals':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_WithdrawalManagement__WEBPACK_IMPORTED_MODULE_9__.WithdrawalManagement, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 100,\n                    columnNumber: 16\n                }, this);\n            case 'mining-management':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_MiningManagement__WEBPACK_IMPORTED_MODULE_16__.MiningManagement, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 102,\n                    columnNumber: 16\n                }, this);\n            case 'support':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_SupportTicketManagement__WEBPACK_IMPORTED_MODULE_10__.SupportTicketManagement, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 104,\n                    columnNumber: 16\n                }, this);\n            case 'binary-points':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_BinaryPointsManagement__WEBPACK_IMPORTED_MODULE_11__.BinaryPointsManagement, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 106,\n                    columnNumber: 16\n                }, this);\n            case 'referral-commissions':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_ReferralCommissionTracking__WEBPACK_IMPORTED_MODULE_12__.ReferralCommissionTracking, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 108,\n                    columnNumber: 16\n                }, this);\n            case 'email-settings':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_EmailSettings__WEBPACK_IMPORTED_MODULE_15__.EmailSettings, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 110,\n                    columnNumber: 16\n                }, this);\n            case 'settings':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_SystemSettings__WEBPACK_IMPORTED_MODULE_13__.SystemSettings, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 112,\n                    columnNumber: 16\n                }, this);\n            case 'logs':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_SystemLogs__WEBPACK_IMPORTED_MODULE_14__.SystemLogs, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 114,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_AdminDashboard__WEBPACK_IMPORTED_MODULE_5__.AdminDashboard, {\n                    onTabChange: setActiveTab\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 116,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_AdminLayout__WEBPACK_IMPORTED_MODULE_4__.AdminLayout, {\n        activeTab: activeTab,\n        onTabChange: setActiveTab,\n        children: renderTabContent()\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\admin\\\\page.tsx\",\n        lineNumber: 121,\n        columnNumber: 5\n    }, this);\n}\n_s(AdminPage, \"6UBIMFG/BRhOSUF/UBGBUya8Og0=\", false, function() {\n    return [\n        _hooks_useAuth__WEBPACK_IMPORTED_MODULE_2__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = AdminPage;\nvar _c;\n$RefreshReg$(_c, \"AdminPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/admin/MiningManagement.tsx":
/*!***************************************************!*\
  !*** ./src/components/admin/MiningManagement.tsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MiningManagement: () => (/* binding */ MiningManagement)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_DollarSign_Eye_RefreshCw_Search_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,DollarSign,Eye,RefreshCw,Search,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_DollarSign_Eye_RefreshCw_Search_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,DollarSign,Eye,RefreshCw,Search,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_DollarSign_Eye_RefreshCw_Search_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,DollarSign,Eye,RefreshCw,Search,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_DollarSign_Eye_RefreshCw_Search_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,DollarSign,Eye,RefreshCw,Search,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_DollarSign_Eye_RefreshCw_Search_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,DollarSign,Eye,RefreshCw,Search,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_DollarSign_Eye_RefreshCw_Search_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,DollarSign,Eye,RefreshCw,Search,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_DollarSign_Eye_RefreshCw_Search_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,DollarSign,Eye,RefreshCw,Search,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_DollarSign_Eye_RefreshCw_Search_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,DollarSign,Eye,RefreshCw,Search,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_DollarSign_Eye_RefreshCw_Search_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,DollarSign,Eye,RefreshCw,Search,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ MiningManagement auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst MiningManagement = ()=>{\n    _s();\n    const [miningUnits, setMiningUnits] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalActiveUnits: 0,\n        totalActiveTHS: 0,\n        totalInvestment: 0,\n        totalEarningsDistributed: 0,\n        averageDailyROI: 0,\n        activeUsers: 0\n    });\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('ALL');\n    const [selectedUnit, setSelectedUnit] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MiningManagement.useEffect\": ()=>{\n            fetchMiningData();\n        }\n    }[\"MiningManagement.useEffect\"], [\n        searchTerm,\n        statusFilter\n    ]);\n    const fetchMiningData = async ()=>{\n        try {\n            setLoading(true);\n            const params = new URLSearchParams();\n            if (searchTerm) params.append('search', searchTerm);\n            if (statusFilter !== 'ALL') params.append('status', statusFilter);\n            const response = await fetch(\"/api/admin/mining?\".concat(params.toString()), {\n                credentials: 'include'\n            });\n            if (response.ok) {\n                const data = await response.json();\n                setMiningUnits(data.units || []);\n                setStats(data.stats || stats);\n            }\n        } catch (error) {\n            console.error('Failed to fetch mining data:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case 'ACTIVE':\n                return 'bg-green-100 text-green-800 border-green-200';\n            case 'EXPIRED':\n                return 'bg-gray-100 text-gray-800 border-gray-200';\n            default:\n                return 'bg-gray-100 text-gray-800 border-gray-200';\n        }\n    };\n    const getProgressColor = (percentage)=>{\n        if (percentage >= 80) return 'bg-red-500';\n        if (percentage >= 60) return 'bg-yellow-500';\n        return 'bg-green-500';\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_DollarSign_Eye_RefreshCw_Search_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                className: \"h-8 w-8 animate-spin text-blue-500\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                lineNumber: 113,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n            lineNumber: 112,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-white\",\n                                children: \"Mining Management\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-slate-400 mt-1\",\n                                children: \"Monitor and manage all mining units across the platform\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        onClick: fetchMiningData,\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_DollarSign_Eye_RefreshCw_Search_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                                lineNumber: 130,\n                                columnNumber: 11\n                            }, undefined),\n                            \"Refresh\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                lineNumber: 121,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"bg-slate-800 border-slate-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-slate-400 text-sm\",\n                                                children: \"Active Units\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-white\",\n                                                children: stats.totalActiveUnits\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                                                lineNumber: 142,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_DollarSign_Eye_RefreshCw_Search_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"h-8 w-8 text-green-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                                lineNumber: 139,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"bg-slate-800 border-slate-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-slate-400 text-sm\",\n                                                children: \"Total TH/s\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                                                lineNumber: 153,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-white\",\n                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.formatTHS)(stats.totalActiveTHS)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                                                lineNumber: 154,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_DollarSign_Eye_RefreshCw_Search_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"h-8 w-8 text-yellow-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                                        lineNumber: 156,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                            lineNumber: 150,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"bg-slate-800 border-slate-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-slate-400 text-sm\",\n                                                children: \"Total Investment\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                                                lineNumber: 165,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-white\",\n                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.formatCurrency)(stats.totalInvestment)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                                                lineNumber: 166,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_DollarSign_Eye_RefreshCw_Search_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-8 w-8 text-blue-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                            lineNumber: 162,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"bg-slate-800 border-slate-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-slate-400 text-sm\",\n                                                children: \"Earnings Paid\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                                                lineNumber: 177,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-white\",\n                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.formatCurrency)(stats.totalEarningsDistributed)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                                                lineNumber: 178,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_DollarSign_Eye_RefreshCw_Search_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"h-8 w-8 text-green-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                            lineNumber: 174,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                        lineNumber: 173,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"bg-slate-800 border-slate-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-slate-400 text-sm\",\n                                                children: \"Avg Daily ROI\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                                                lineNumber: 189,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-white\",\n                                                children: [\n                                                    stats.averageDailyROI.toFixed(2),\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                                        lineNumber: 188,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_DollarSign_Eye_RefreshCw_Search_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-8 w-8 text-purple-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                                        lineNumber: 192,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                            lineNumber: 186,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                        lineNumber: 185,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"bg-slate-800 border-slate-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-slate-400 text-sm\",\n                                                children: \"Active Users\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                                                lineNumber: 201,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-white\",\n                                                children: stats.activeUsers\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_DollarSign_Eye_RefreshCw_Search_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-8 w-8 text-indigo-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                            lineNumber: 198,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                        lineNumber: 197,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                lineNumber: 136,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"bg-slate-800 border-slate-700\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"p-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_DollarSign_Eye_RefreshCw_Search_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                                            lineNumber: 216,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                            placeholder: \"Search by user name, email, or referral ID...\",\n                                            value: searchTerm,\n                                            onChange: (e)=>setSearchTerm(e.target.value),\n                                            className: \"pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                                    lineNumber: 215,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                                lineNumber: 214,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"sm:w-48\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: statusFilter,\n                                    onChange: (e)=>setStatusFilter(e.target.value),\n                                    className: \"w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"ALL\",\n                                            children: \"All Status\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                                            lineNumber: 231,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"ACTIVE\",\n                                            children: \"Active\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                                            lineNumber: 232,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"EXPIRED\",\n                                            children: \"Expired\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                                    lineNumber: 226,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                                lineNumber: 225,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                        lineNumber: 213,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                    lineNumber: 212,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                lineNumber: 211,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"bg-slate-800 border-slate-700\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"text-white flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_DollarSign_Eye_RefreshCw_Search_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                                    lineNumber: 244,\n                                    columnNumber: 13\n                                }, undefined),\n                                \"Mining Units (\",\n                                miningUnits.length,\n                                \")\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                            lineNumber: 243,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                        lineNumber: 242,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: miningUnits.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"overflow-x-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                className: \"w-full text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            className: \"border-b border-slate-700\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"text-left py-3 px-4 text-slate-300 font-medium\",\n                                                    children: \"User\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                                                    lineNumber: 254,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"text-left py-3 px-4 text-slate-300 font-medium\",\n                                                    children: \"TH/s\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                                                    lineNumber: 255,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"text-left py-3 px-4 text-slate-300 font-medium\",\n                                                    children: \"Investment\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                                                    lineNumber: 256,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"text-left py-3 px-4 text-slate-300 font-medium\",\n                                                    children: \"Earned\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                                                    lineNumber: 257,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"text-left py-3 px-4 text-slate-300 font-medium\",\n                                                    children: \"Daily ROI\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                                                    lineNumber: 258,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"text-left py-3 px-4 text-slate-300 font-medium\",\n                                                    children: \"Progress\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                                                    lineNumber: 259,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"text-left py-3 px-4 text-slate-300 font-medium\",\n                                                    children: \"Status\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                                                    lineNumber: 260,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"text-left py-3 px-4 text-slate-300 font-medium\",\n                                                    children: \"Purchase Date\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                                                    lineNumber: 261,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"text-left py-3 px-4 text-slate-300 font-medium\",\n                                                    children: \"Actions\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                                                    lineNumber: 262,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                                            lineNumber: 253,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                        children: miningUnits.map((unit)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                className: \"border-b border-slate-700 hover:bg-slate-700/50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"py-3 px-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-white font-medium\",\n                                                                    children: [\n                                                                        unit.user.firstName,\n                                                                        \" \",\n                                                                        unit.user.lastName\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                                                                    lineNumber: 270,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-slate-400 text-xs\",\n                                                                    children: unit.user.email\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                                                                    lineNumber: 273,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-slate-400 text-xs\",\n                                                                    children: [\n                                                                        \"ID: \",\n                                                                        unit.user.referralId\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                                                                    lineNumber: 274,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                                                            lineNumber: 269,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                                                        lineNumber: 268,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"py-3 px-4 text-white font-mono\",\n                                                        children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.formatTHS)(unit.thsAmount)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                                                        lineNumber: 277,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"py-3 px-4 text-white\",\n                                                        children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.formatCurrency)(unit.investmentAmount)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                                                        lineNumber: 278,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"py-3 px-4 text-green-400\",\n                                                        children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.formatCurrency)(unit.totalEarned)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                                                        lineNumber: 279,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"py-3 px-4 text-white\",\n                                                        children: [\n                                                            unit.dailyROI.toFixed(2),\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                                                        lineNumber: 280,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"py-3 px-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1 bg-slate-700 rounded-full h-2\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"h-2 rounded-full \".concat(getProgressColor(unit.progressPercentage)),\n                                                                        style: {\n                                                                            width: \"\".concat(Math.min(unit.progressPercentage, 100), \"%\")\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                                                                        lineNumber: 284,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                                                                    lineNumber: 283,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs text-slate-400 w-12\",\n                                                                    children: [\n                                                                        unit.progressPercentage.toFixed(0),\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                                                                    lineNumber: 289,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                                                            lineNumber: 282,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                                                        lineNumber: 281,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"py-3 px-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"px-2 py-1 rounded-full text-xs font-medium border \".concat(getStatusColor(unit.status)),\n                                                            children: unit.status\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                                                            lineNumber: 295,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                                                        lineNumber: 294,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"py-3 px-4 text-slate-300 text-xs\",\n                                                        children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.formatDateTime)(unit.purchaseDate)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                                                        lineNumber: 299,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"py-3 px-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            onClick: ()=>setSelectedUnit(unit),\n                                                            className: \"border-slate-600 text-slate-300 hover:bg-slate-700 hover:text-white\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_DollarSign_Eye_RefreshCw_Search_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                                                                lineNumber: 309,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                                                            lineNumber: 303,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                                                        lineNumber: 302,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, unit.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                                                lineNumber: 267,\n                                                columnNumber: 21\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                                        lineNumber: 265,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                            lineNumber: 250,\n                            columnNumber: 13\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_DollarSign_Eye_RefreshCw_Search_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"h-12 w-12 text-slate-400 mx-auto mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                                    lineNumber: 319,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-white mb-2\",\n                                    children: \"No Mining Units Found\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                                    lineNumber: 320,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-slate-400\",\n                                    children: searchTerm || statusFilter !== 'ALL' ? 'Try adjusting your search or filter criteria.' : 'No mining units have been purchased yet.'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                                    lineNumber: 321,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                            lineNumber: 318,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                        lineNumber: 248,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n                lineNumber: 241,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\MiningManagement.tsx\",\n        lineNumber: 119,\n        columnNumber: 5\n    }, undefined);\n};\n_s(MiningManagement, \"IEKxtMqFEEr4peVUH+hv8zi19Lo=\");\n_c = MiningManagement;\nvar _c;\n$RefreshReg$(_c, \"MiningManagement\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/MiningManagement.tsx\n"));

/***/ })

});