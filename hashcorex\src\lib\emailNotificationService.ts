import { emailService } from './email';
import { emailLogDb, userDb } from './database';

export interface EmailNotificationData {
  userId: string;
  email: string;
  firstName: string;
  lastName: string;
}

export interface DepositNotificationData extends EmailNotificationData {
  amount: number;
  transactionId: string;
  currency: string;
}

export interface KYCNotificationData extends EmailNotificationData {
  status: 'APPROVED' | 'REJECTED';
  rejectionReason?: string;
}

export interface WithdrawalNotificationData extends EmailNotificationData {
  amount: number;
  status: 'APPROVED' | 'REJECTED' | 'COMPLETED' | 'FAILED';
  transactionHash?: string;
  rejectionReason?: string;
  usdtAddress?: string;
}

export interface MiningUnitPurchaseNotificationData extends EmailNotificationData {
  thsAmount: number;
  investmentAmount: number;
  dailyROI: number;
  purchaseDate: string;
  expiryDate: string;
}

export interface MiningUnitExpiryNotificationData extends EmailNotificationData {
  thsAmount: number;
  investmentAmount: number;
  totalEarned: number;
  purchaseDate: string;
  expiryDate: string;
  expiryReason: 'TIME_LIMIT' | 'RETURN_LIMIT';
}

class EmailNotificationService {
  /**
   * Send deposit success notification
   */
  async sendDepositSuccessNotification(data: DepositNotificationData): Promise<boolean> {
    try {
      const template = await emailService.getEmailTemplate('deposit_success');

      if (!template) {
        console.warn('Email template "deposit_success" not found. Using default template.');
      }

      const subject = template?.subject || 'Deposit Confirmed - HashCoreX';
      let html = template?.htmlContent || this.getDefaultDepositSuccessTemplate();
      let text = template?.textContent || '';

      // Replace variables
      html = this.replaceVariables(html, {
        firstName: data.firstName,
        lastName: data.lastName,
        email: data.email,
        amount: data.amount.toString(),
        transactionId: data.transactionId,
        currency: data.currency,
      });

      text = this.replaceVariables(text, {
        firstName: data.firstName,
        lastName: data.lastName,
        email: data.email,
        amount: data.amount.toString(),
        transactionId: data.transactionId,
        currency: data.currency,
      });

      // Create email log entry
      const emailLog = await emailLogDb.create({
        to: data.email,
        subject,
        template: 'deposit_success',
        status: 'PENDING',
      });

      const emailSent = await emailService.sendEmail({
        to: data.email,
        subject,
        html,
        text: text || `Deposit Confirmed\n\nAmount: ${data.amount} ${data.currency}\nTransaction ID: ${data.transactionId}`,
      });

      if (emailSent) {
        await emailLogDb.updateStatus(emailLog.id, 'SENT');
        return true;
      } else {
        await emailLogDb.updateStatus(emailLog.id, 'FAILED', 'Email service error');
        return false;
      }
    } catch (error) {
      console.error('Error sending deposit success notification:', error);
      return false;
    }
  }

  /**
   * Send KYC status notification
   */
  async sendKYCStatusNotification(data: KYCNotificationData): Promise<boolean> {
    try {
      const templateName = data.status === 'APPROVED' ? 'kyc_approved' : 'kyc_rejected';
      const template = await emailService.getEmailTemplate(templateName);

      if (!template) {
        console.warn(`Email template "${templateName}" not found. Using default template.`);
      }

      const subject = template?.subject || `KYC ${data.status === 'APPROVED' ? 'Approved' : 'Rejected'} - HashCoreX`;
      let html = template?.htmlContent || this.getDefaultKYCTemplate(data.status);
      let text = template?.textContent || '';

      // Replace variables
      html = this.replaceVariables(html, {
        firstName: data.firstName,
        lastName: data.lastName,
        email: data.email,
        status: data.status,
        rejectionReason: data.rejectionReason || '',
      });

      text = this.replaceVariables(text, {
        firstName: data.firstName,
        lastName: data.lastName,
        email: data.email,
        status: data.status,
        rejectionReason: data.rejectionReason || '',
      });

      // Create email log entry
      const emailLog = await emailLogDb.create({
        to: data.email,
        subject,
        template: templateName,
        status: 'PENDING',
      });

      const emailSent = await emailService.sendEmail({
        to: data.email,
        subject,
        html,
        text: text || `KYC ${data.status}\n\n${data.rejectionReason ? `Reason: ${data.rejectionReason}` : 'Your KYC has been approved.'}`,
      });

      if (emailSent) {
        await emailLogDb.updateStatus(emailLog.id, 'SENT');
        return true;
      } else {
        await emailLogDb.updateStatus(emailLog.id, 'FAILED', 'Email service error');
        return false;
      }
    } catch (error) {
      console.error('Error sending KYC status notification:', error);
      return false;
    }
  }

  /**
   * Send withdrawal status notification
   */
  async sendWithdrawalStatusNotification(data: WithdrawalNotificationData): Promise<boolean> {
    try {
      const templateName = this.getWithdrawalTemplateName(data.status);
      const template = await emailService.getEmailTemplate(templateName);

      if (!template) {
        console.warn(`Email template "${templateName}" not found. Using default template.`);
      }

      const subject = template?.subject || `Withdrawal ${this.getWithdrawalStatusText(data.status)} - HashCoreX`;
      let html = template?.htmlContent || this.getDefaultWithdrawalTemplate(data.status);
      let text = template?.textContent || '';

      // Replace variables
      html = this.replaceVariables(html, {
        firstName: data.firstName,
        lastName: data.lastName,
        email: data.email,
        amount: data.amount.toString(),
        status: data.status,
        transactionHash: data.transactionHash || '',
        rejectionReason: data.rejectionReason || '',
        usdtAddress: data.usdtAddress || '',
      });

      text = this.replaceVariables(text, {
        firstName: data.firstName,
        lastName: data.lastName,
        email: data.email,
        amount: data.amount.toString(),
        status: data.status,
        transactionHash: data.transactionHash || '',
        rejectionReason: data.rejectionReason || '',
        usdtAddress: data.usdtAddress || '',
      });

      // Create email log entry
      const emailLog = await emailLogDb.create({
        to: data.email,
        subject,
        template: templateName,
        status: 'PENDING',
      });

      const emailSent = await emailService.sendEmail({
        to: data.email,
        subject,
        html,
        text: text || this.getDefaultWithdrawalText(data),
      });

      if (emailSent) {
        await emailLogDb.updateStatus(emailLog.id, 'SENT');
        return true;
      } else {
        await emailLogDb.updateStatus(emailLog.id, 'FAILED', 'Email service error');
        return false;
      }
    } catch (error) {
      console.error('Error sending withdrawal status notification:', error);
      return false;
    }
  }

  /**
   * Replace template variables
   */
  private replaceVariables(template: string, variables: Record<string, string>): string {
    let result = template;
    for (const [key, value] of Object.entries(variables)) {
      const regex = new RegExp(`{{${key}}}`, 'g');
      result = result.replace(regex, value);
    }
    return result;
  }

  /**
   * Get withdrawal template name based on status
   */
  private getWithdrawalTemplateName(status: string): string {
    switch (status) {
      case 'APPROVED':
        return 'withdrawal_approved';
      case 'REJECTED':
        return 'withdrawal_rejected';
      case 'COMPLETED':
        return 'withdrawal_completed';
      case 'FAILED':
        return 'withdrawal_failed';
      default:
        return 'withdrawal_status';
    }
  }

  /**
   * Get withdrawal status text
   */
  private getWithdrawalStatusText(status: string): string {
    switch (status) {
      case 'APPROVED':
        return 'Approved';
      case 'REJECTED':
        return 'Rejected';
      case 'COMPLETED':
        return 'Completed';
      case 'FAILED':
        return 'Failed';
      default:
        return 'Updated';
    }
  }

  /**
   * Default deposit success template
   */
  private getDefaultDepositSuccessTemplate(): string {
    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #ffd60a 0%, #10b981 100%); padding: 20px; text-align: center;">
          <h1 style="color: white; margin: 0;">HashCoreX</h1>
        </div>
        <div style="padding: 30px; background: #f9f9f9;">
          <h2 style="color: #333;">Deposit Confirmed!</h2>
          <p>Hello {{firstName}},</p>
          <p>Great news! Your deposit has been successfully confirmed and credited to your account.</p>
          <div style="background: white; padding: 20px; margin: 20px 0; border-radius: 8px; border-left: 4px solid #10b981;">
            <h3 style="margin: 0 0 10px 0; color: #10b981;">Deposit Details</h3>
            <p><strong>Amount:</strong> {{amount}} {{currency}}</p>
            <p><strong>Transaction ID:</strong> {{transactionId}}</p>
            <p><strong>Status:</strong> Confirmed</p>
          </div>
          <p>Your funds are now available in your wallet and you can start using them immediately.</p>
          <p>Best regards,<br>The HashCoreX Team</p>
        </div>
        <div style="background: #333; color: white; padding: 20px; text-align: center; font-size: 12px;">
          <p>&copy; 2024 HashCoreX. All rights reserved.</p>
        </div>
      </div>
    `;
  }

  /**
   * Default KYC template
   */
  private getDefaultKYCTemplate(status: 'APPROVED' | 'REJECTED'): string {
    const isApproved = status === 'APPROVED';
    const color = isApproved ? '#10b981' : '#ef4444';
    const title = isApproved ? 'KYC Approved!' : 'KYC Rejected';
    const message = isApproved 
      ? 'Congratulations! Your KYC verification has been approved. You now have full access to all platform features.'
      : 'Unfortunately, your KYC verification has been rejected. Please review the reason below and resubmit with correct documents.';

    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #ffd60a 0%, ${color} 100%); padding: 20px; text-align: center;">
          <h1 style="color: white; margin: 0;">HashCoreX</h1>
        </div>
        <div style="padding: 30px; background: #f9f9f9;">
          <h2 style="color: #333;">${title}</h2>
          <p>Hello {{firstName}},</p>
          <p>${message}</p>
          ${!isApproved ? '<div style="background: white; padding: 20px; margin: 20px 0; border-radius: 8px; border-left: 4px solid #ef4444;"><p><strong>Rejection Reason:</strong> {{rejectionReason}}</p></div>' : ''}
          <p>Best regards,<br>The HashCoreX Team</p>
        </div>
        <div style="background: #333; color: white; padding: 20px; text-align: center; font-size: 12px;">
          <p>&copy; 2024 HashCoreX. All rights reserved.</p>
        </div>
      </div>
    `;
  }

  /**
   * Default withdrawal template
   */
  private getDefaultWithdrawalTemplate(status: string): string {
    const getStatusColor = (status: string) => {
      switch (status) {
        case 'APPROVED': return '#10b981';
        case 'COMPLETED': return '#10b981';
        case 'REJECTED': return '#ef4444';
        case 'FAILED': return '#ef4444';
        default: return '#6b7280';
      }
    };

    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #ffd60a 0%, ${getStatusColor(status)} 100%); padding: 20px; text-align: center;">
          <h1 style="color: white; margin: 0;">HashCoreX</h1>
        </div>
        <div style="padding: 30px; background: #f9f9f9;">
          <h2 style="color: #333;">Withdrawal {{status}}</h2>
          <p>Hello {{firstName}},</p>
          <p>Your withdrawal request has been {{status}}.</p>
          <div style="background: white; padding: 20px; margin: 20px 0; border-radius: 8px; border-left: 4px solid ${getStatusColor(status)};">
            <h3 style="margin: 0 0 10px 0; color: ${getStatusColor(status)};">Withdrawal Details</h3>
            <p><strong>Amount:</strong> {{amount}} USDT</p>
            <p><strong>Status:</strong> {{status}}</p>
            {{#if usdtAddress}}<p><strong>Address:</strong> {{usdtAddress}}</p>{{/if}}
            {{#if transactionHash}}<p><strong>Transaction Hash:</strong> {{transactionHash}}</p>{{/if}}
            {{#if rejectionReason}}<p><strong>Reason:</strong> {{rejectionReason}}</p>{{/if}}
          </div>
          <p>Best regards,<br>The HashCoreX Team</p>
        </div>
        <div style="background: #333; color: white; padding: 20px; text-align: center; font-size: 12px;">
          <p>&copy; 2024 HashCoreX. All rights reserved.</p>
        </div>
      </div>
    `;
  }

  /**
   * Default withdrawal text
   */
  private getDefaultWithdrawalText(data: WithdrawalNotificationData): string {
    let text = `Withdrawal ${data.status}\n\nAmount: ${data.amount} USDT\nStatus: ${data.status}`;
    if (data.usdtAddress) text += `\nAddress: ${data.usdtAddress}`;
    if (data.transactionHash) text += `\nTransaction Hash: ${data.transactionHash}`;
    if (data.rejectionReason) text += `\nReason: ${data.rejectionReason}`;
    return text;
  }

  /**
   * Send mining unit purchase notification
   */
  async sendMiningUnitPurchaseNotification(data: MiningUnitPurchaseNotificationData): Promise<boolean> {
    try {
      const template = await emailService.getEmailTemplate('mining_unit_purchase');

      if (!template) {
        console.warn('Email template "mining_unit_purchase" not found. Using default template.');
      }

      const subject = template?.subject || 'Mining Unit Purchase Confirmed - HashCoreX';
      let html = template?.htmlContent || this.getDefaultMiningUnitPurchaseTemplate();
      let text = template?.textContent || this.getDefaultMiningUnitPurchaseText(data);

      // Replace variables
      html = this.replaceVariables(html, {
        firstName: data.firstName,
        lastName: data.lastName,
        email: data.email,
        thsAmount: data.thsAmount.toString(),
        investmentAmount: data.investmentAmount.toString(),
        dailyROI: data.dailyROI.toFixed(2),
        purchaseDate: new Date(data.purchaseDate).toLocaleDateString(),
        expiryDate: new Date(data.expiryDate).toLocaleDateString(),
      });

      text = this.replaceVariables(text, {
        firstName: data.firstName,
        lastName: data.lastName,
        email: data.email,
        thsAmount: data.thsAmount.toString(),
        investmentAmount: data.investmentAmount.toString(),
        dailyROI: data.dailyROI.toFixed(2),
        purchaseDate: new Date(data.purchaseDate).toLocaleDateString(),
        expiryDate: new Date(data.expiryDate).toLocaleDateString(),
      });

      // Create email log entry
      const emailLog = await emailLogDb.create({
        to: data.email,
        subject,
        template: 'mining_unit_purchase',
        status: 'PENDING',
      });

      // Send email
      const sent = await emailService.sendEmail({
        to: data.email,
        subject,
        html,
        text,
      });

      // Update email log status
      await emailLogDb.update(emailLog.id, {
        status: sent ? 'SENT' : 'FAILED',
        sentAt: sent ? new Date().toISOString() : undefined,
      });

      return sent;
    } catch (error) {
      console.error('Failed to send mining unit purchase notification:', error);
      return false;
    }
  }

  /**
   * Send mining unit expiry notification
   */
  async sendMiningUnitExpiryNotification(data: MiningUnitExpiryNotificationData): Promise<boolean> {
    try {
      const template = await emailService.getEmailTemplate('mining_unit_expiry');

      if (!template) {
        console.warn('Email template "mining_unit_expiry" not found. Using default template.');
      }

      const subject = template?.subject || 'Mining Unit Expired - HashCoreX';
      let html = template?.htmlContent || this.getDefaultMiningUnitExpiryTemplate();
      let text = template?.textContent || this.getDefaultMiningUnitExpiryText(data);

      // Replace variables
      html = this.replaceVariables(html, {
        firstName: data.firstName,
        lastName: data.lastName,
        email: data.email,
        thsAmount: data.thsAmount.toString(),
        investmentAmount: data.investmentAmount.toString(),
        totalEarned: data.totalEarned.toString(),
        purchaseDate: new Date(data.purchaseDate).toLocaleDateString(),
        expiryDate: new Date(data.expiryDate).toLocaleDateString(),
        expiryReason: data.expiryReason === 'TIME_LIMIT' ? '24-month time limit reached' : '5x return limit achieved',
      });

      text = this.replaceVariables(text, {
        firstName: data.firstName,
        lastName: data.lastName,
        email: data.email,
        thsAmount: data.thsAmount.toString(),
        investmentAmount: data.investmentAmount.toString(),
        totalEarned: data.totalEarned.toString(),
        purchaseDate: new Date(data.purchaseDate).toLocaleDateString(),
        expiryDate: new Date(data.expiryDate).toLocaleDateString(),
        expiryReason: data.expiryReason === 'TIME_LIMIT' ? '24-month time limit reached' : '5x return limit achieved',
      });

      // Create email log entry
      const emailLog = await emailLogDb.create({
        to: data.email,
        subject,
        template: 'mining_unit_expiry',
        status: 'PENDING',
      });

      // Send email
      const sent = await emailService.sendEmail({
        to: data.email,
        subject,
        html,
        text,
      });

      // Update email log status
      await emailLogDb.update(emailLog.id, {
        status: sent ? 'SENT' : 'FAILED',
        sentAt: sent ? new Date().toISOString() : undefined,
      });

      return sent;
    } catch (error) {
      console.error('Failed to send mining unit expiry notification:', error);
      return false;
    }
  }

  /**
   * Default mining unit purchase template
   */
  private getDefaultMiningUnitPurchaseTemplate(): string {
    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #ffd60a 0%, #10b981 100%); padding: 20px; text-align: center;">
          <h1 style="color: white; margin: 0;">HashCoreX</h1>
        </div>
        <div style="padding: 30px; background: #f9f9f9;">
          <h2 style="color: #333;">Mining Unit Purchase Confirmed!</h2>
          <p>Hello {{firstName}},</p>
          <p>Congratulations! Your mining unit purchase has been successfully processed and is now active.</p>
          <div style="background: white; padding: 20px; margin: 20px 0; border-radius: 8px; border-left: 4px solid #10b981;">
            <h3 style="margin: 0 0 10px 0; color: #10b981;">Mining Unit Details</h3>
            <p><strong>TH/s Amount:</strong> {{thsAmount}} TH/s</p>
            <p><strong>Investment Amount:</strong> ${{investmentAmount}} USDT</p>
            <p><strong>Daily ROI:</strong> {{dailyROI}}%</p>
            <p><strong>Purchase Date:</strong> {{purchaseDate}}</p>
            <p><strong>Expiry Date:</strong> {{expiryDate}}</p>
          </div>
          <div style="background: #e3f2fd; padding: 15px; margin: 20px 0; border-radius: 8px;">
            <h4 style="margin: 0 0 10px 0; color: #1976d2;">Important Information</h4>
            <ul style="margin: 0; padding-left: 20px;">
              <li>Your mining unit will start generating daily returns immediately</li>
              <li>Earnings are paid out weekly on Sundays at 00:00 AM GMT+5:30</li>
              <li>Mining units expire after 24 months or when they reach 5x return, whichever comes first</li>
              <li>You can track your earnings in the Mining section of your dashboard</li>
            </ul>
          </div>
          <p>Thank you for choosing HashCoreX for your mining investment!</p>
          <p>Best regards,<br>The HashCoreX Team</p>
        </div>
        <div style="background: #333; color: white; padding: 20px; text-align: center; font-size: 12px;">
          <p>&copy; 2024 HashCoreX. All rights reserved.</p>
        </div>
      </div>
    `;
  }

  /**
   * Default mining unit expiry template
   */
  private getDefaultMiningUnitExpiryTemplate(): string {
    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #ffd60a 0%, #ff9800 100%); padding: 20px; text-align: center;">
          <h1 style="color: white; margin: 0;">HashCoreX</h1>
        </div>
        <div style="padding: 30px; background: #f9f9f9;">
          <h2 style="color: #333;">Mining Unit Expired</h2>
          <p>Hello {{firstName}},</p>
          <p>We're writing to inform you that one of your mining units has reached its expiry condition.</p>
          <div style="background: white; padding: 20px; margin: 20px 0; border-radius: 8px; border-left: 4px solid #ff9800;">
            <h3 style="margin: 0 0 10px 0; color: #ff9800;">Expired Mining Unit Details</h3>
            <p><strong>TH/s Amount:</strong> {{thsAmount}} TH/s</p>
            <p><strong>Original Investment:</strong> ${{investmentAmount}} USDT</p>
            <p><strong>Total Earned:</strong> ${{totalEarned}} USDT</p>
            <p><strong>Purchase Date:</strong> {{purchaseDate}}</p>
            <p><strong>Expiry Date:</strong> {{expiryDate}}</p>
            <p><strong>Expiry Reason:</strong> {{expiryReason}}</p>
          </div>
          <div style="background: #e8f5e8; padding: 15px; margin: 20px 0; border-radius: 8px;">
            <h4 style="margin: 0 0 10px 0; color: #2e7d32;">What Happens Next?</h4>
            <ul style="margin: 0; padding-left: 20px;">
              <li>All accumulated earnings have been credited to your wallet</li>
              <li>This mining unit will no longer generate daily returns</li>
              <li>You can view the complete history in your Mining section</li>
              <li>Consider purchasing new mining units to continue earning</li>
            </ul>
          </div>
          <p>Thank you for your continued trust in HashCoreX!</p>
          <p>Best regards,<br>The HashCoreX Team</p>
        </div>
        <div style="background: #333; color: white; padding: 20px; text-align: center; font-size: 12px;">
          <p>&copy; 2024 HashCoreX. All rights reserved.</p>
        </div>
      </div>
    `;
  }

  /**
   * Default mining unit purchase text
   */
  private getDefaultMiningUnitPurchaseText(data: MiningUnitPurchaseNotificationData): string {
    return `Mining Unit Purchase Confirmed!

Hello ${data.firstName},

Your mining unit purchase has been successfully processed:

TH/s Amount: ${data.thsAmount} TH/s
Investment Amount: $${data.investmentAmount} USDT
Daily ROI: ${data.dailyROI.toFixed(2)}%
Purchase Date: ${new Date(data.purchaseDate).toLocaleDateString()}
Expiry Date: ${new Date(data.expiryDate).toLocaleDateString()}

Your mining unit will start generating daily returns immediately.

Best regards,
The HashCoreX Team`;
  }

  /**
   * Default mining unit expiry text
   */
  private getDefaultMiningUnitExpiryText(data: MiningUnitExpiryNotificationData): string {
    return `Mining Unit Expired

Hello ${data.firstName},

One of your mining units has expired:

TH/s Amount: ${data.thsAmount} TH/s
Original Investment: $${data.investmentAmount} USDT
Total Earned: $${data.totalEarned} USDT
Purchase Date: ${new Date(data.purchaseDate).toLocaleDateString()}
Expiry Date: ${new Date(data.expiryDate).toLocaleDateString()}
Expiry Reason: ${data.expiryReason === 'TIME_LIMIT' ? '24-month time limit reached' : '5x return limit achieved'}

All earnings have been credited to your wallet.

Best regards,
The HashCoreX Team`;
  }
}

export const emailNotificationService = new EmailNotificationService();
