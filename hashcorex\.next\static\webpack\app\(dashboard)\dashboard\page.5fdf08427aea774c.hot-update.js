"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/SupportCenter.tsx":
/*!****************************************************!*\
  !*** ./src/components/dashboard/SupportCenter.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SupportCenter: () => (/* binding */ SupportCenter)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BookOpen,CheckCircle,Clock,HelpCircle,MessageCircle,Plus,Send,Settings,Shield,TrendingUp,Users,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BookOpen,CheckCircle,Clock,HelpCircle,MessageCircle,Plus,Send,Settings,Shield,TrendingUp,Users,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wallet.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BookOpen,CheckCircle,Clock,HelpCircle,MessageCircle,Plus,Send,Settings,Shield,TrendingUp,Users,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BookOpen,CheckCircle,Clock,HelpCircle,MessageCircle,Plus,Send,Settings,Shield,TrendingUp,Users,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BookOpen,CheckCircle,Clock,HelpCircle,MessageCircle,Plus,Send,Settings,Shield,TrendingUp,Users,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BookOpen,CheckCircle,Clock,HelpCircle,MessageCircle,Plus,Send,Settings,Shield,TrendingUp,Users,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BookOpen,CheckCircle,Clock,HelpCircle,MessageCircle,Plus,Send,Settings,Shield,TrendingUp,Users,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BookOpen,CheckCircle,Clock,HelpCircle,MessageCircle,Plus,Send,Settings,Shield,TrendingUp,Users,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BookOpen,CheckCircle,Clock,HelpCircle,MessageCircle,Plus,Send,Settings,Shield,TrendingUp,Users,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BookOpen,CheckCircle,Clock,HelpCircle,MessageCircle,Plus,Send,Settings,Shield,TrendingUp,Users,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BookOpen,CheckCircle,Clock,HelpCircle,MessageCircle,Plus,Send,Settings,Shield,TrendingUp,Users,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BookOpen,CheckCircle,Clock,HelpCircle,MessageCircle,Plus,Send,Settings,Shield,TrendingUp,Users,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-question-mark.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BookOpen,CheckCircle,Clock,HelpCircle,MessageCircle,Plus,Send,Settings,Shield,TrendingUp,Users,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ SupportCenter auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n// FAQ Data\nconst faqData = [\n    {\n        id: '1',\n        category: 'Getting Started',\n        question: 'How do I start mining with HashCoreX?',\n        answer: \"To start mining with HashCoreX, follow these simple steps:\\n\\n1. Complete KYC Verification: First, complete your KYC verification in the Profile section. This is required for all mining activities.\\n\\n2. Deposit Funds: Go to your Wallet and deposit USDT (TRC20) to fund your account. The minimum deposit amount is configured by the admin.\\n\\n3. Purchase Mining Units: Navigate to the Mining section and purchase mining units. Choose your desired TH/s amount and investment level.\\n\\n4. Start Earning: Your mining units will start generating daily returns automatically. Earnings are calculated based on your TH/s amount and current market conditions.\\n\\n5. Track Progress: Monitor your earnings in the Earnings section and watch your mining units progress toward their 5x investment return limit.\\n\\nRemember: Mining units expire after 24 months or when they reach 5x their investment amount, whichever comes first.\",\n        icon: _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        tags: [\n            'mining',\n            'getting started',\n            'kyc',\n            'deposit'\n        ]\n    },\n    {\n        id: '2',\n        category: 'Mining Units',\n        question: 'How are daily returns calculated?',\n        answer: \"Daily returns are calculated using a dynamic system based on your mining unit's TH/s amount:\\n\\nDynamic ROI System:\\n• Different TH/s ranges have different return rates\\n• Returns are calculated as: (Investment Amount \\xd7 Daily ROI%) \\xf7 100\\n• ROI percentages are determined by admin-configured ranges\\n\\nExample Ranges:\\n• 0-10 TH/s: 0.3% - 0.5% daily\\n• 10-50 TH/s: 0.4% - 0.6% daily\\n• 50+ TH/s: 0.5% - 0.7% daily\\n\\nImportant Notes:\\n• Returns are credited daily but paid out weekly (Sundays at 00:00 AM GMT+5:30)\\n• Mining units expire when they reach 5x their investment amount\\n• FIFO system: Oldest units receive earnings first\\n• All earnings are subject to market conditions and admin configuration\",\n        icon: _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        tags: [\n            'mining',\n            'returns',\n            'calculation',\n            'roi'\n        ]\n    },\n    {\n        id: '3',\n        category: 'Wallet & Payments',\n        question: 'How do deposits and withdrawals work?',\n        answer: \"Deposits:\\n• Only USDT (TRC20) deposits are accepted\\n• Minimum deposit amount is set by admin (typically $50)\\n• Deposits are automatically verified on the Tron blockchain\\n• Funds are available immediately after confirmation\\n\\nWithdrawals:\\n• Minimum withdrawal: $50\\n• Fixed fee: $3 + 1% of withdrawal amount\\n• Processing time: Up to 3 business days\\n• Only to verified TRC20 addresses\\n\\nWallet Balance:\\n• Available Balance: Funds ready for use or withdrawal\\n• Pending Balance: Earnings waiting for weekly distribution\\n• Total Earnings: Cumulative earnings from all sources\\n\\nImportant:\\n• Complete KYC verification before making withdrawals\\n• Ensure your TRC20 address is correct before submitting\\n• Withdrawal fees are deducted from your balance\",\n        icon: _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        tags: [\n            'wallet',\n            'deposit',\n            'withdrawal',\n            'usdt',\n            'trc20'\n        ]\n    },\n    {\n        id: '4',\n        category: 'Referral System',\n        question: 'How does the binary referral system work?',\n        answer: \"HashCoreX uses a binary tree referral system with three placement types:\\n\\nPlacement Types:\\n1. General Referral: Placed in weaker leg automatically\\n2. Left-Side Referral: Specifically placed on left side\\n3. Right-Side Referral: Specifically placed on right side\\n\\nEarning Structure:\\n• Direct Referral Commission: 10% one-time bonus when your referral purchases mining units\\n• Binary Matching Bonus: Weekly matching of binary points (1 point = $10)\\n\\nBinary Points:\\n• Earned when your referrals purchase mining units\\n• $150 purchase = 1.5 points (supports 2 decimal places)\\n• Points are matched weekly between left and right sides\\n• Excess points reset to 0 after matching\\n\\nRequirements:\\n• You must have active mining units to earn commissions\\n• Binary matching occurs weekly at 15:00 UTC\\n• All earnings are allocated to your mining units using FIFO system\",\n        icon: _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        tags: [\n            'referral',\n            'binary',\n            'commission',\n            'points',\n            'matching'\n        ]\n    },\n    {\n        id: '5',\n        category: 'Account & Security',\n        question: 'What is KYC and why is it required?',\n        answer: \"KYC (Know Your Customer) Verification:\\n\\nKYC is a mandatory verification process required for all HashCoreX users to ensure compliance with financial regulations.\\n\\nKYC Process:\\n1. Personal Information: Provide accurate personal details\\n2. ID Document: Upload government-issued ID (passport, driver's license, etc.)\\n3. Selfie Verification: Take a selfie holding your ID document\\n4. Admin Review: Our team reviews your submission (typically 24-48 hours)\\n\\nKYC Status:\\n• Not Submitted: KYC documents not yet uploaded\\n• Pending: Under admin review\\n• Approved: Verification complete - full access granted\\n• Rejected: Resubmission required with correct documents\\n\\nWhy KYC is Required:\\n• Legal compliance with financial regulations\\n• Account security and fraud prevention\\n• Enables withdrawals and full platform access\\n• Protects both users and the platform\\n\\nImportant: Profile name fields become read-only after KYC submission to prevent fraud.\",\n        icon: _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        tags: [\n            'kyc',\n            'verification',\n            'security',\n            'compliance',\n            'identity'\n        ]\n    },\n    {\n        id: '6',\n        category: 'Technical Support',\n        question: 'What should I do if I encounter technical issues?',\n        answer: \"Common Solutions:\\n\\nLogin Issues:\\n• Clear browser cache and cookies\\n• Try incognito/private browsing mode\\n• Ensure you're using the correct email address\\n• Check if your account is active\\n\\nTransaction Issues:\\n• Verify transaction hash on Tron blockchain explorer\\n• Check if you're using the correct network (Mainnet/Testnet)\\n• Ensure sufficient balance for fees\\n• Wait for blockchain confirmations\\n\\nDisplay Issues:\\n• Refresh the page\\n• Try a different browser\\n• Disable browser extensions temporarily\\n• Check your internet connection\\n\\nStill Need Help?\\n1. Create a support ticket with detailed information\\n2. Include screenshots if possible\\n3. Provide transaction hashes for payment issues\\n4. Specify your browser and device type\\n\\nResponse Times:\\n• General inquiries: 24-48 hours\\n• Technical issues: 12-24 hours\\n• Payment issues: Priority handling within 12 hours\",\n        icon: _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        tags: [\n            'technical',\n            'support',\n            'troubleshooting',\n            'login',\n            'transactions'\n        ]\n    },\n    {\n        id: '7',\n        category: 'Mining Units',\n        question: 'What happens when mining units expire?',\n        answer: 'Mining units expire under two conditions:\\n\\nExpiry Conditions:\\n• After 24 months from purchase date\\n• When they reach 5x their investment amount (whichever comes first)\\n\\nFIFO Expiry System:\\n• Oldest mining units expire first\\n• Earnings are allocated to oldest units first\\n• This ensures fair distribution and maximum returns\\n\\nWhat Happens at Expiry:\\n• Unit stops generating daily returns\\n• All accumulated earnings are credited to your wallet\\n• Unit status changes to \"Expired\"\\n• No further earnings from that specific unit\\n\\nImportant Notes:\\n• You can track each unit\\'s progress toward 5x return\\n• Expired units are shown in your mining history\\n• Purchase new units to continue earning\\n• All earnings are subject to weekly payout schedule',\n        icon: _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        tags: [\n            'mining',\n            'expiry',\n            'fifo',\n            'returns',\n            'units'\n        ]\n    },\n    {\n        id: '8',\n        category: 'Wallet & Payments',\n        question: 'How do I set up my TRC20 wallet address?',\n        answer: \"Setting up your TRC20 wallet address for withdrawals:\\n\\nSupported Wallets:\\n• TronLink (Browser extension)\\n• Trust Wallet (Mobile app)\\n• Atomic Wallet (Desktop/Mobile)\\n• Any wallet supporting TRC20 tokens\\n\\nSetup Process:\\n1. Go to Profile Settings → Billing & Payments\\n2. Enter your TRC20 wallet address\\n3. Verify the address is correct (double-check!)\\n4. Save the address for future withdrawals\\n\\nImportant Security Tips:\\n• Always copy-paste addresses, never type manually\\n• Test with a small amount first\\n• Ensure your wallet supports USDT TRC20\\n• Keep your private keys secure and never share them\\n• Use only your own wallet addresses\\n\\nNetwork Information:\\n• Network: Tron (TRX)\\n• Token Standard: TRC20\\n• Supported Token: USDT\\n• Withdrawal fees apply as per platform settings\",\n        icon: _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        tags: [\n            'wallet',\n            'trc20',\n            'setup',\n            'withdrawal',\n            'address'\n        ]\n    },\n    {\n        id: '9',\n        category: 'Referral System',\n        question: 'How can I maximize my referral earnings?',\n        answer: \"Strategies to maximize your referral earnings:\\n\\nBuilding Your Network:\\n• Share your referral links on social media\\n• Explain the benefits of HashCoreX to friends and family\\n• Use different placement types strategically\\n• Focus on quality referrals who will be active\\n\\nPlacement Strategy:\\n• Use general referrals for automatic balancing\\n• Use left/right specific placements for strategic building\\n• Monitor your binary tree balance regularly\\n• Help your referrals understand the system\\n\\nEarning Optimization:\\n• Maintain active mining units to earn commissions\\n• Focus on both direct referrals and binary matching\\n• Track your binary points weekly\\n• Reinvest earnings to purchase more mining units\\n\\nBest Practices:\\n• Provide support to your referrals\\n• Share educational content about mining\\n• Be transparent about risks and rewards\\n• Build long-term relationships, not just quick referrals\\n\\nRemember: You must have active mining units to earn any referral commissions.\",\n        icon: _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        tags: [\n            'referral',\n            'earnings',\n            'strategy',\n            'network',\n            'optimization'\n        ]\n    },\n    {\n        id: '10',\n        category: 'Account & Security',\n        question: 'How do I reset my password?',\n        answer: \"Password reset process:\\n\\nStep-by-Step Process:\\n1. Go to the login page\\n2. Click \\\"Forgot Password\\\" link\\n3. Enter your registered email address\\n4. Check your email for OTP verification code\\n5. Enter the OTP code when prompted\\n6. Create a new strong password\\n7. Confirm your new password\\n8. You'll be automatically redirected to dashboard\\n\\nPassword Requirements:\\n• Minimum 8 characters\\n• Include uppercase and lowercase letters\\n• Include at least one number\\n• Include at least one special character\\n• Avoid common passwords\\n\\nSecurity Tips:\\n• Use a unique password for HashCoreX\\n• Consider using a password manager\\n• Don't share your password with anyone\\n• Change your password regularly\\n• Log out from shared devices\\n\\nTroubleshooting:\\n• Check spam folder for OTP email\\n• Ensure email address is correct\\n• Wait a few minutes for email delivery\\n• Contact support if issues persist\",\n        icon: _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        tags: [\n            'password',\n            'reset',\n            'security',\n            'otp',\n            'login'\n        ]\n    },\n    {\n        id: '11',\n        category: 'Getting Started',\n        question: 'What are the minimum requirements to start?',\n        answer: \"Minimum requirements to start mining with HashCoreX:\\n\\nFinancial Requirements:\\n• Minimum deposit: $50 USDT (TRC20)\\n• Minimum mining unit purchase: As configured by admin\\n• Transaction fees: Network fees for deposits/withdrawals\\n\\nAccount Requirements:\\n• Valid email address for registration\\n• Complete KYC verification (mandatory)\\n• TRC20 wallet for deposits and withdrawals\\n• Active status (maintained by having mining units)\\n\\nTechnical Requirements:\\n• Modern web browser (Chrome, Firefox, Safari, Edge)\\n• Stable internet connection\\n• Email access for verification and notifications\\n• Basic understanding of cryptocurrency transactions\\n\\nGetting Started Checklist:\\n1. ✓ Register with valid email\\n2. ✓ Verify email address\\n3. ✓ Complete KYC verification\\n4. ✓ Set up TRC20 wallet\\n5. ✓ Make first deposit ($50+ USDT)\\n6. ✓ Purchase first mining units\\n7. ✓ Start earning daily returns\\n\\nTime Investment:\\n• Initial setup: 30-60 minutes\\n• Daily monitoring: 5-10 minutes\\n• Weekly review: 15-30 minutes\",\n        icon: _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        tags: [\n            'requirements',\n            'minimum',\n            'getting started',\n            'checklist',\n            'setup'\n        ]\n    },\n    {\n        id: '12',\n        category: 'Mining Units',\n        question: 'How do I track my mining performance?',\n        answer: \"Tracking your mining performance effectively:\\n\\nDashboard Overview:\\n• Total active TH/s amount\\n• Daily earnings summary\\n• Pending vs available balance\\n• Mining units status overview\\n\\nDetailed Tracking:\\n• Individual unit performance\\n• Progress toward 5x return limit\\n• Daily ROI percentages (7-day average)\\n• Earnings allocation per unit\\n\\nKey Metrics to Monitor:\\n• Total investment amount\\n• Total earnings to date\\n• Average daily return percentage\\n• Time remaining until expiry\\n• FIFO allocation progress\\n\\nPerformance Analysis:\\n• Compare actual vs expected returns\\n• Monitor market condition impacts\\n• Track weekly payout schedules\\n• Review binary earnings allocation\\n\\nOptimization Tips:\\n• Reinvest earnings for compound growth\\n• Monitor unit expiry dates\\n• Balance investment across different TH/s ranges\\n• Keep track of referral earnings contribution\\n\\nReports Available:\\n• Transaction history\\n• Earnings breakdown\\n• Mining unit details\\n• Referral performance summary\",\n        icon: _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        tags: [\n            'tracking',\n            'performance',\n            'monitoring',\n            'analytics',\n            'optimization'\n        ]\n    }\n];\nconst SupportCenter = ()=>{\n    _s();\n    const [tickets, setTickets] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showNewTicketModal, setShowNewTicketModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedTicket, setSelectedTicket] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedFAQ, setSelectedFAQ] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [faqSearchTerm, setFaqSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('All');\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const itemsPerPage = 6;\n    const [newTicketForm, setNewTicketForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        subject: '',\n        message: '',\n        priority: 'MEDIUM'\n    });\n    const [newResponse, setNewResponse] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [submitting, setSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SupportCenter.useEffect\": ()=>{\n            fetchTickets();\n        }\n    }[\"SupportCenter.useEffect\"], []);\n    // Get unique categories for filtering\n    const categories = [\n        'All',\n        ...Array.from(new Set(faqData.map((faq)=>faq.category)))\n    ];\n    // Filter FAQs based on search term and category\n    const filteredFAQs = faqData.filter((faq)=>{\n        const matchesSearch = faq.question.toLowerCase().includes(faqSearchTerm.toLowerCase()) || faq.answer.toLowerCase().includes(faqSearchTerm.toLowerCase()) || faq.tags.some((tag)=>tag.toLowerCase().includes(faqSearchTerm.toLowerCase()));\n        const matchesCategory = selectedCategory === 'All' || faq.category === selectedCategory;\n        return matchesSearch && matchesCategory;\n    });\n    // Calculate pagination\n    const totalPages = Math.ceil(filteredFAQs.length / itemsPerPage);\n    const startIndex = (currentPage - 1) * itemsPerPage;\n    const endIndex = startIndex + itemsPerPage;\n    const currentFAQs = filteredFAQs.slice(startIndex, endIndex);\n    // Reset to first page when filters change\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect({\n        \"SupportCenter.useEffect\": ()=>{\n            setCurrentPage(1);\n        }\n    }[\"SupportCenter.useEffect\"], [\n        faqSearchTerm,\n        selectedCategory\n    ]);\n    const fetchTickets = async ()=>{\n        try {\n            const response = await fetch('/api/support/tickets', {\n                credentials: 'include'\n            });\n            if (response.ok) {\n                const data = await response.json();\n                if (data.success) {\n                    setTickets(data.data);\n                }\n            }\n        } catch (error) {\n            console.error('Failed to fetch tickets:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const createTicket = async (e)=>{\n        e.preventDefault();\n        setSubmitting(true);\n        try {\n            const response = await fetch('/api/support/tickets', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                credentials: 'include',\n                body: JSON.stringify(newTicketForm)\n            });\n            if (response.ok) {\n                setNewTicketForm({\n                    subject: '',\n                    message: '',\n                    priority: 'MEDIUM'\n                });\n                setShowNewTicketModal(false);\n                fetchTickets();\n            }\n        } catch (error) {\n            console.error('Failed to create ticket:', error);\n        } finally{\n            setSubmitting(false);\n        }\n    };\n    const addResponse = async (ticketId)=>{\n        if (!newResponse.trim()) return;\n        try {\n            const response = await fetch(\"/api/support/tickets/\".concat(ticketId, \"/responses\"), {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                credentials: 'include',\n                body: JSON.stringify({\n                    message: newResponse\n                })\n            });\n            if (response.ok) {\n                setNewResponse('');\n                fetchTickets();\n                // Update selected ticket\n                const updatedTickets = await fetch('/api/support/tickets', {\n                    credentials: 'include'\n                });\n                if (updatedTickets.ok) {\n                    const data = await updatedTickets.json();\n                    const updatedTicket = data.data.find((t)=>t.id === ticketId);\n                    if (updatedTicket) {\n                        setSelectedTicket(updatedTicket);\n                    }\n                }\n            }\n        } catch (error) {\n            console.error('Failed to add response:', error);\n        }\n    };\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case 'OPEN':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    className: \"h-4 w-4 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                    lineNumber: 573,\n                    columnNumber: 16\n                }, undefined);\n            case 'IN_PROGRESS':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"h-4 w-4 text-solar-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                    lineNumber: 575,\n                    columnNumber: 16\n                }, undefined);\n            case 'RESOLVED':\n            case 'CLOSED':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    className: \"h-4 w-4 text-eco-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                    lineNumber: 578,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    className: \"h-4 w-4 text-gray-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                    lineNumber: 580,\n                    columnNumber: 16\n                }, undefined);\n        }\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case 'OPEN':\n                return 'bg-red-100 text-red-700';\n            case 'IN_PROGRESS':\n                return 'bg-solar-100 text-solar-700';\n            case 'RESOLVED':\n            case 'CLOSED':\n                return 'bg-eco-100 text-eco-700';\n            default:\n                return 'bg-gray-100 text-gray-700';\n        }\n    };\n    const getPriorityColor = (priority)=>{\n        switch(priority){\n            case 'URGENT':\n                return 'bg-red-100 text-red-700';\n            case 'HIGH':\n                return 'bg-orange-100 text-orange-700';\n            case 'MEDIUM':\n                return 'bg-solar-100 text-solar-700';\n            case 'LOW':\n                return 'bg-gray-100 text-gray-700';\n            default:\n                return 'bg-gray-100 text-gray-700';\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-pulse\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-8 bg-gray-200 rounded w-1/4 mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                        lineNumber: 617,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-64 bg-gray-200 rounded\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                        lineNumber: 618,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                lineNumber: 616,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n            lineNumber: 615,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: \"Support Center\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                lineNumber: 629,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mt-1\",\n                                children: \"Get help and manage support tickets\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                lineNumber: 630,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                        lineNumber: 628,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        onClick: ()=>setShowNewTicketModal(true),\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                lineNumber: 636,\n                                columnNumber: 11\n                            }, undefined),\n                            \"New Ticket\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                        lineNumber: 632,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                lineNumber: 627,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                        lineNumber: 646,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    \"Support Tickets\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                lineNumber: 645,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                            lineNumber: 644,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            children: tickets.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: tickets.map((ticket)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        onClick: ()=>setSelectedTicket(ticket),\n                                        className: \"p-4 border border-gray-200 rounded-lg cursor-pointer\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start justify-between mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-gray-900 truncate flex-1\",\n                                                        children: ticket.subject\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                        lineNumber: 660,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2 ml-2\",\n                                                        children: [\n                                                            getStatusIcon(ticket.status),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs px-2 py-1 rounded-full \".concat(getStatusColor(ticket.status)),\n                                                                children: ticket.status\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                                lineNumber: 665,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                        lineNumber: 663,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                lineNumber: 659,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600 mb-2 line-clamp-2\",\n                                                children: ticket.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                lineNumber: 670,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between text-xs text-gray-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-2 py-1 rounded-full \".concat(getPriorityColor(ticket.priority)),\n                                                        children: ticket.priority\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                        lineNumber: 674,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.formatDateTime)(ticket.createdAt)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                        lineNumber: 677,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                lineNumber: 673,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, ticket.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                        lineNumber: 654,\n                                        columnNumber: 21\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                lineNumber: 652,\n                                columnNumber: 17\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                        lineNumber: 684,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                                        children: \"No Support Tickets\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                        lineNumber: 685,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"You haven't created any support tickets yet.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                        lineNumber: 686,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                lineNumber: 683,\n                                columnNumber: 17\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                            lineNumber: 650,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                    lineNumber: 643,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                lineNumber: 642,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                        lineNumber: 698,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    \"Help Tutorials & FAQ\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                lineNumber: 697,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                            lineNumber: 696,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6 space-y-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col sm:flex-row gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                                    placeholder: \"Search FAQs...\",\n                                                    value: faqSearchTerm,\n                                                    onChange: (e)=>setFaqSearchTerm(e.target.value),\n                                                    className: \"w-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                    lineNumber: 707,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                lineNumber: 706,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"sm:w-48\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: selectedCategory,\n                                                    onChange: (e)=>setSelectedCategory(e.target.value),\n                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-solar-500 focus:border-transparent\",\n                                                    children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: category,\n                                                            children: category\n                                                        }, category, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                            lineNumber: 721,\n                                                            columnNumber: 23\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                    lineNumber: 715,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                lineNumber: 714,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                        lineNumber: 705,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                    lineNumber: 704,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6\",\n                                    children: currentFAQs.map((faq)=>{\n                                        const IconComponent = faq.icon;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            onClick: ()=>setSelectedFAQ(faq),\n                                            className: \"p-6 border border-gray-200 rounded-lg cursor-pointer hover:border-solar-300 hover:shadow-md transition-all duration-200 bg-white\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start gap-3 mb-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-2 bg-solar-100 rounded-lg\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                                className: \"h-5 w-5 text-solar-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                                lineNumber: 740,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                            lineNumber: 739,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs font-medium text-solar-600 bg-solar-50 px-2 py-1 rounded-full\",\n                                                                children: faq.category\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                                lineNumber: 743,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                            lineNumber: 742,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                    lineNumber: 738,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-semibold text-gray-900 mb-2 line-clamp-2\",\n                                                    children: faq.question\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                    lineNumber: 748,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600 line-clamp-3 mb-3\",\n                                                    children: faq.answer.split('\\n')[0]\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                    lineNumber: 751,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-wrap gap-1\",\n                                                    children: [\n                                                        faq.tags.slice(0, 3).map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded\",\n                                                                children: tag\n                                                            }, tag, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                                lineNumber: 756,\n                                                                columnNumber: 25\n                                                            }, undefined)),\n                                                        faq.tags.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-gray-500\",\n                                                            children: [\n                                                                \"+\",\n                                                                faq.tags.length - 3,\n                                                                \" more\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                            lineNumber: 761,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                    lineNumber: 754,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, faq.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                            lineNumber: 733,\n                                            columnNumber: 19\n                                        }, undefined);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                    lineNumber: 729,\n                                    columnNumber: 13\n                                }, undefined),\n                                filteredFAQs.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-12 col-span-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                            lineNumber: 771,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-gray-900 mb-2\",\n                                            children: \"No FAQs Found\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                            lineNumber: 772,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: faqSearchTerm || selectedCategory !== 'All' ? 'Try adjusting your search or filter criteria.' : 'FAQ content is being updated.'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                            lineNumber: 773,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                    lineNumber: 770,\n                                    columnNumber: 15\n                                }, undefined),\n                                filteredFAQs.length > itemsPerPage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"col-span-full flex items-center justify-between mt-6 pt-6 border-t border-gray-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: [\n                                                \"Showing \",\n                                                startIndex + 1,\n                                                \" to \",\n                                                Math.min(endIndex, filteredFAQs.length),\n                                                \" of \",\n                                                filteredFAQs.length,\n                                                \" articles\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                            lineNumber: 784,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setCurrentPage((prev)=>Math.max(prev - 1, 1)),\n                                                    disabled: currentPage === 1,\n                                                    className: \"px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                    children: \"Previous\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                    lineNumber: 788,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                Array.from({\n                                                    length: totalPages\n                                                }, (_, i)=>i + 1).map((page)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setCurrentPage(page),\n                                                        className: \"px-3 py-2 text-sm font-medium rounded-md \".concat(currentPage === page ? 'bg-solar-600 text-white' : 'text-gray-500 bg-white border border-gray-300 hover:bg-gray-50'),\n                                                        children: page\n                                                    }, page, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                        lineNumber: 798,\n                                                        columnNumber: 21\n                                                    }, undefined)),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setCurrentPage((prev)=>Math.min(prev + 1, totalPages)),\n                                                    disabled: currentPage === totalPages,\n                                                    className: \"px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                    children: \"Next\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                    lineNumber: 811,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                            lineNumber: 787,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                    lineNumber: 783,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                            lineNumber: 702,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                    lineNumber: 695,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                lineNumber: 694,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Modal, {\n                isOpen: showNewTicketModal,\n                onClose: ()=>setShowNewTicketModal(false),\n                title: \"Create Support Ticket\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: createTicket,\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                            label: \"Subject\",\n                            value: newTicketForm.subject,\n                            onChange: (e)=>setNewTicketForm((prev)=>({\n                                        ...prev,\n                                        subject: e.target.value\n                                    })),\n                            placeholder: \"Brief description of your issue\",\n                            required: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                            lineNumber: 832,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"Priority\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                    lineNumber: 841,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: newTicketForm.priority,\n                                    onChange: (e)=>setNewTicketForm((prev)=>({\n                                                ...prev,\n                                                priority: e.target.value\n                                            })),\n                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-solar-500 focus:border-transparent\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"LOW\",\n                                            children: \"Low\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                            lineNumber: 849,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"MEDIUM\",\n                                            children: \"Medium\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                            lineNumber: 850,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"HIGH\",\n                                            children: \"High\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                            lineNumber: 851,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"URGENT\",\n                                            children: \"Urgent\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                            lineNumber: 852,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                    lineNumber: 844,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                            lineNumber: 840,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"Message\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                    lineNumber: 857,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                    value: newTicketForm.message,\n                                    onChange: (e)=>setNewTicketForm((prev)=>({\n                                                ...prev,\n                                                message: e.target.value\n                                            })),\n                                    placeholder: \"Describe your issue in detail...\",\n                                    rows: 4,\n                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-solar-500 focus:border-transparent\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                    lineNumber: 860,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                            lineNumber: 856,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    type: \"button\",\n                                    variant: \"outline\",\n                                    onClick: ()=>setShowNewTicketModal(false),\n                                    className: \"flex-1\",\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                    lineNumber: 871,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    type: \"submit\",\n                                    loading: submitting,\n                                    className: \"flex-1\",\n                                    children: \"Create Ticket\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                    lineNumber: 879,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                            lineNumber: 870,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                    lineNumber: 831,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                lineNumber: 826,\n                columnNumber: 7\n            }, undefined),\n            selectedTicket && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Modal, {\n                isOpen: !!selectedTicket,\n                onClose: ()=>setSelectedTicket(null),\n                title: \"Ticket: \".concat(selectedTicket.subject),\n                size: \"xl\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                getStatusIcon(selectedTicket.status),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm px-2 py-1 rounded-full \".concat(getStatusColor(selectedTicket.status)),\n                                    children: selectedTicket.status\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                    lineNumber: 901,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm px-2 py-1 rounded-full \".concat(getPriorityColor(selectedTicket.priority)),\n                                    children: selectedTicket.priority\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                    lineNumber: 904,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                            lineNumber: 899,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-50 p-4 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600 mb-2\",\n                                    children: \"Original Message:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                    lineNumber: 910,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-900\",\n                                    children: selectedTicket.message\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                    lineNumber: 911,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-500 mt-2\",\n                                    children: [\n                                        \"Created: \",\n                                        (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.formatDateTime)(selectedTicket.createdAt)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                    lineNumber: 912,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                            lineNumber: 909,\n                            columnNumber: 13\n                        }, undefined),\n                        selectedTicket.responses.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"font-medium text-gray-900\",\n                                    children: \"Responses:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                    lineNumber: 919,\n                                    columnNumber: 17\n                                }, undefined),\n                                selectedTicket.responses.map((response)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 rounded-lg \".concat(response.isAdmin ? 'bg-blue-50 border-l-4 border-blue-500' : 'bg-gray-50'),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-900\",\n                                                children: response.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                lineNumber: 927,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-500 mt-1\",\n                                                children: [\n                                                    response.isAdmin ? 'Support Team' : 'You',\n                                                    \" • \",\n                                                    (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.formatDateTime)(response.createdAt)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                lineNumber: 928,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, response.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                        lineNumber: 921,\n                                        columnNumber: 19\n                                    }, undefined))\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                            lineNumber: 918,\n                            columnNumber: 15\n                        }, undefined),\n                        selectedTicket.status !== 'CLOSED' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                    value: newResponse,\n                                    onChange: (e)=>setNewResponse(e.target.value),\n                                    placeholder: \"Add a response...\",\n                                    rows: 3,\n                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-solar-500 focus:border-transparent\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                    lineNumber: 938,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: ()=>addResponse(selectedTicket.id),\n                                    disabled: !newResponse.trim(),\n                                    className: \"w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                            lineNumber: 950,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        \"Send Response\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                    lineNumber: 945,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                            lineNumber: 937,\n                            columnNumber: 15\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                    lineNumber: 898,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                lineNumber: 892,\n                columnNumber: 9\n            }, undefined),\n            selectedFAQ && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Modal, {\n                isOpen: !!selectedFAQ,\n                onClose: ()=>setSelectedFAQ(null),\n                title: selectedFAQ.question,\n                size: \"xl\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-3 pb-4 border-b border-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 bg-solar-100 rounded-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(selectedFAQ.icon, {\n                                        className: \"h-6 w-6 text-solar-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                        lineNumber: 970,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                    lineNumber: 969,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium text-solar-600 bg-solar-50 px-3 py-1 rounded-full\",\n                                        children: selectedFAQ.category\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                        lineNumber: 973,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                    lineNumber: 972,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                            lineNumber: 968,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"prose prose-sm max-w-none\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"whitespace-pre-line text-gray-700 leading-relaxed\",\n                                children: selectedFAQ.answer\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                lineNumber: 980,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                            lineNumber: 979,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"pt-4 border-t border-gray-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium text-gray-700 mr-2\",\n                                        children: \"Tags:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                        lineNumber: 987,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    selectedFAQ.tags.map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-solar-600 bg-solar-50 px-3 py-1 rounded-full\",\n                                            children: tag\n                                        }, tag, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                            lineNumber: 989,\n                                            columnNumber: 19\n                                        }, undefined))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                lineNumber: 986,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                            lineNumber: 985,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"pt-4 border-t border-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600 mb-3\",\n                                    children: \"Still need help? Create a support ticket for personalized assistance.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                    lineNumber: 997,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: ()=>{\n                                        setSelectedFAQ(null);\n                                        setShowNewTicketModal(true);\n                                    },\n                                    className: \"w-full sm:w-auto\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                            lineNumber: 1007,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        \"Create Support Ticket\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                    lineNumber: 1000,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                            lineNumber: 996,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                    lineNumber: 967,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                lineNumber: 961,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n        lineNumber: 625,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SupportCenter, \"Ce9h9yPbRnJtX2zoEXEUNfGKYag=\");\n_c = SupportCenter;\nvar _c;\n$RefreshReg$(_c, \"SupportCenter\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2Rhc2hib2FyZC9TdXBwb3J0Q2VudGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFbUQ7QUFDOEM7QUFvQjNFO0FBQ3VCO0FBNkI3QyxXQUFXO0FBQ1gsTUFBTXdCLFVBQXFCO0lBQ3pCO1FBQ0VDLElBQUk7UUFDSkMsVUFBVTtRQUNWQyxVQUFVO1FBQ1ZDLFFBQVM7UUFhVEMsTUFBTVIsNkxBQVVBO1FBQ2hCUyxNQUFNO1lBQUM7WUFBVTtZQUFtQjtZQUFPO1NBQVU7SUFDdkQ7SUFDQTtRQUNFTCxJQUFJO1FBQ0pDLFVBQVU7UUFDVkMsVUFBVTtRQUNWQyxRQUFTO1FBaUJUQyxNQUFNUiw2TEFBVUE7UUFDaEJTLE1BQU07WUFBQztZQUFVO1lBQVc7WUFBZTtTQUFNO0lBQ25EO0lBQ0E7UUFDRUwsSUFBSTtRQUNKQyxVQUFVO1FBQ1ZDLFVBQVU7UUFDVkMsUUFBUztRQXFCVEMsTUFBTVAsNkxBQU1BO1FBQ1pRLE1BQU07WUFBQztZQUFVO1lBQVc7WUFBYztZQUFRO1NBQVE7SUFDNUQ7SUFDQTtRQUNFTCxJQUFJO1FBQ0pDLFVBQVU7UUFDVkMsVUFBVTtRQUNWQyxRQUFTO1FBcUJUQyxNQUFNViw2TEFBS0E7UUFDWFcsTUFBTTtZQUFDO1lBQVk7WUFBVTtZQUFjO1lBQVU7U0FBVztJQUNsRTtJQUNBO1FBQ0VMLElBQUk7UUFDSkMsVUFBVTtRQUNWQyxVQUFVO1FBQ1ZDLFFBQVM7UUF1QlRDLE1BQU1ULDZMQUFNQTtRQUNaVSxNQUFNO1lBQUM7WUFBTztZQUFnQjtZQUFZO1lBQWM7U0FBVztJQUNyRTtJQUNBO1FBQ0VMLElBQUk7UUFDSkMsVUFBVTtRQUNWQyxVQUFVO1FBQ1ZDLFFBQVM7UUE4QlRDLE1BQU1iLDZMQUFRQTtRQUNkYyxNQUFNO1lBQUM7WUFBYTtZQUFXO1lBQW1CO1lBQVM7U0FBZTtJQUM1RTtJQUNBO1FBQ0VMLElBQUk7UUFDSkMsVUFBVTtRQUNWQyxVQUFVO1FBQ1ZDLFFBQVM7UUFzQlRDLE1BQU1qQiw2TEFBS0E7UUFDWGtCLE1BQU07WUFBQztZQUFVO1lBQVU7WUFBUTtZQUFXO1NBQVE7SUFDeEQ7SUFDQTtRQUNFTCxJQUFJO1FBQ0pDLFVBQVU7UUFDVkMsVUFBVTtRQUNWQyxRQUFTO1FBMEJUQyxNQUFNUCw2TEFBTUE7UUFDWlEsTUFBTTtZQUFDO1lBQVU7WUFBUztZQUFTO1lBQWM7U0FBVTtJQUM3RDtJQUNBO1FBQ0VMLElBQUk7UUFDSkMsVUFBVTtRQUNWQyxVQUFVO1FBQ1ZDLFFBQVM7UUEyQlRDLE1BQU1SLDZMQUFVQTtRQUNoQlMsTUFBTTtZQUFDO1lBQVk7WUFBWTtZQUFZO1lBQVc7U0FBZTtJQUN2RTtJQUNBO1FBQ0VMLElBQUk7UUFDSkMsVUFBVTtRQUNWQyxVQUFVO1FBQ1ZDLFFBQVM7UUErQlRDLE1BQU1ULDZMQUFNQTtRQUNaVSxNQUFNO1lBQUM7WUFBWTtZQUFTO1lBQVk7WUFBTztTQUFRO0lBQ3pEO0lBQ0E7UUFDRUwsSUFBSTtRQUNKQyxVQUFVO1FBQ1ZDLFVBQVU7UUFDVkMsUUFBUztRQWdDVEMsTUFBTVgsOExBQVFBO1FBQ2RZLE1BQU07WUFBQztZQUFnQjtZQUFXO1lBQW1CO1lBQWE7U0FBUTtJQUM1RTtJQUNBO1FBQ0VMLElBQUk7UUFDSkMsVUFBVTtRQUNWQyxVQUFVO1FBQ1ZDLFFBQVM7UUFzQ1RDLE1BQU1SLDZMQUFVQTtRQUNoQlMsTUFBTTtZQUFDO1lBQVk7WUFBZTtZQUFjO1lBQWE7U0FBZTtJQUM5RTtDQUNEO0FBRU0sTUFBTUMsZ0JBQTBCOztJQUNyQyxNQUFNLENBQUNDLFNBQVNDLFdBQVcsR0FBR2hDLCtDQUFRQSxDQUFrQixFQUFFO0lBQzFELE1BQU0sQ0FBQ2lDLFNBQVNDLFdBQVcsR0FBR2xDLCtDQUFRQSxDQUFDO0lBQ3ZDLE1BQU0sQ0FBQ21DLG9CQUFvQkMsc0JBQXNCLEdBQUdwQywrQ0FBUUEsQ0FBQztJQUM3RCxNQUFNLENBQUNxQyxnQkFBZ0JDLGtCQUFrQixHQUFHdEMsK0NBQVFBLENBQXVCO0lBQzNFLE1BQU0sQ0FBQ3VDLGFBQWFDLGVBQWUsR0FBR3hDLCtDQUFRQSxDQUFpQjtJQUMvRCxNQUFNLENBQUN5QyxlQUFlQyxpQkFBaUIsR0FBRzFDLCtDQUFRQSxDQUFDO0lBQ25ELE1BQU0sQ0FBQzJDLGtCQUFrQkMsb0JBQW9CLEdBQUc1QywrQ0FBUUEsQ0FBUztJQUNqRSxNQUFNLENBQUM2QyxhQUFhQyxlQUFlLEdBQUc5QywrQ0FBUUEsQ0FBQztJQUMvQyxNQUFNK0MsZUFBZTtJQUNyQixNQUFNLENBQUNDLGVBQWVDLGlCQUFpQixHQUFHakQsK0NBQVFBLENBQUM7UUFDakRrRCxTQUFTO1FBQ1RDLFNBQVM7UUFDVEMsVUFBVTtJQUNaO0lBQ0EsTUFBTSxDQUFDQyxhQUFhQyxlQUFlLEdBQUd0RCwrQ0FBUUEsQ0FBQztJQUMvQyxNQUFNLENBQUN1RCxZQUFZQyxjQUFjLEdBQUd4RCwrQ0FBUUEsQ0FBQztJQUU3Q0MsZ0RBQVNBO21DQUFDO1lBQ1J3RDtRQUNGO2tDQUFHLEVBQUU7SUFFTCxzQ0FBc0M7SUFDdEMsTUFBTUMsYUFBYTtRQUFDO1dBQVVDLE1BQU1DLElBQUksQ0FBQyxJQUFJQyxJQUFJdEMsUUFBUXVDLEdBQUcsQ0FBQ0MsQ0FBQUEsTUFBT0EsSUFBSXRDLFFBQVE7S0FBSTtJQUVwRixnREFBZ0Q7SUFDaEQsTUFBTXVDLGVBQWV6QyxRQUFRMEMsTUFBTSxDQUFDRixDQUFBQTtRQUNsQyxNQUFNRyxnQkFBZ0JILElBQUlyQyxRQUFRLENBQUN5QyxXQUFXLEdBQUdDLFFBQVEsQ0FBQzNCLGNBQWMwQixXQUFXLE9BQzlESixJQUFJcEMsTUFBTSxDQUFDd0MsV0FBVyxHQUFHQyxRQUFRLENBQUMzQixjQUFjMEIsV0FBVyxPQUMzREosSUFBSWxDLElBQUksQ0FBQ3dDLElBQUksQ0FBQ0MsQ0FBQUEsTUFBT0EsSUFBSUgsV0FBVyxHQUFHQyxRQUFRLENBQUMzQixjQUFjMEIsV0FBVztRQUM5RixNQUFNSSxrQkFBa0I1QixxQkFBcUIsU0FBU29CLElBQUl0QyxRQUFRLEtBQUtrQjtRQUN2RSxPQUFPdUIsaUJBQWlCSztJQUMxQjtJQUVBLHVCQUF1QjtJQUN2QixNQUFNQyxhQUFhQyxLQUFLQyxJQUFJLENBQUNWLGFBQWFXLE1BQU0sR0FBRzVCO0lBQ25ELE1BQU02QixhQUFhLENBQUMvQixjQUFjLEtBQUtFO0lBQ3ZDLE1BQU04QixXQUFXRCxhQUFhN0I7SUFDOUIsTUFBTStCLGNBQWNkLGFBQWFlLEtBQUssQ0FBQ0gsWUFBWUM7SUFFbkQsMENBQTBDO0lBQzFDOUUsc0RBQWU7bUNBQUM7WUFDZCtDLGVBQWU7UUFDakI7a0NBQUc7UUFBQ0w7UUFBZUU7S0FBaUI7SUFFcEMsTUFBTWMsZUFBZTtRQUNuQixJQUFJO1lBQ0YsTUFBTXVCLFdBQVcsTUFBTUMsTUFBTSx3QkFBd0I7Z0JBQ25EQyxhQUFhO1lBQ2Y7WUFFQSxJQUFJRixTQUFTRyxFQUFFLEVBQUU7Z0JBQ2YsTUFBTUMsT0FBTyxNQUFNSixTQUFTSyxJQUFJO2dCQUNoQyxJQUFJRCxLQUFLRSxPQUFPLEVBQUU7b0JBQ2hCdEQsV0FBV29ELEtBQUtBLElBQUk7Z0JBQ3RCO1lBQ0Y7UUFDRixFQUFFLE9BQU9HLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLDRCQUE0QkE7UUFDNUMsU0FBVTtZQUNSckQsV0FBVztRQUNiO0lBQ0Y7SUFJQSxNQUFNdUQsZUFBZSxPQUFPQztRQUMxQkEsRUFBRUMsY0FBYztRQUNoQm5DLGNBQWM7UUFFZCxJQUFJO1lBQ0YsTUFBTXdCLFdBQVcsTUFBTUMsTUFBTSx3QkFBd0I7Z0JBQ25EVyxRQUFRO2dCQUNSQyxTQUFTO29CQUNQLGdCQUFnQjtnQkFDbEI7Z0JBQ0FYLGFBQWE7Z0JBQ2JZLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQ2hEO1lBQ3ZCO1lBRUEsSUFBSWdDLFNBQVNHLEVBQUUsRUFBRTtnQkFDZmxDLGlCQUFpQjtvQkFBRUMsU0FBUztvQkFBSUMsU0FBUztvQkFBSUMsVUFBVTtnQkFBUztnQkFDaEVoQixzQkFBc0I7Z0JBQ3RCcUI7WUFDRjtRQUNGLEVBQUUsT0FBTzhCLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLDRCQUE0QkE7UUFDNUMsU0FBVTtZQUNSL0IsY0FBYztRQUNoQjtJQUNGO0lBRUEsTUFBTXlDLGNBQWMsT0FBT0M7UUFDekIsSUFBSSxDQUFDN0MsWUFBWThDLElBQUksSUFBSTtRQUV6QixJQUFJO1lBQ0YsTUFBTW5CLFdBQVcsTUFBTUMsTUFBTSx3QkFBaUMsT0FBVGlCLFVBQVMsZUFBYTtnQkFDekVOLFFBQVE7Z0JBQ1JDLFNBQVM7b0JBQ1AsZ0JBQWdCO2dCQUNsQjtnQkFDQVgsYUFBYTtnQkFDYlksTUFBTUMsS0FBS0MsU0FBUyxDQUFDO29CQUFFN0MsU0FBU0U7Z0JBQVk7WUFDOUM7WUFFQSxJQUFJMkIsU0FBU0csRUFBRSxFQUFFO2dCQUNmN0IsZUFBZTtnQkFDZkc7Z0JBQ0EseUJBQXlCO2dCQUN6QixNQUFNMkMsaUJBQWlCLE1BQU1uQixNQUFNLHdCQUF3QjtvQkFDekRDLGFBQWE7Z0JBQ2Y7Z0JBQ0EsSUFBSWtCLGVBQWVqQixFQUFFLEVBQUU7b0JBQ3JCLE1BQU1DLE9BQU8sTUFBTWdCLGVBQWVmLElBQUk7b0JBQ3RDLE1BQU1nQixnQkFBZ0JqQixLQUFLQSxJQUFJLENBQUNrQixJQUFJLENBQUMsQ0FBQ0MsSUFBcUJBLEVBQUUvRSxFQUFFLEtBQUswRTtvQkFDcEUsSUFBSUcsZUFBZTt3QkFDakIvRCxrQkFBa0IrRDtvQkFDcEI7Z0JBQ0Y7WUFDRjtRQUNGLEVBQUUsT0FBT2QsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsMkJBQTJCQTtRQUMzQztJQUNGO0lBSUEsTUFBTWlCLGdCQUFnQixDQUFDQztRQUNyQixPQUFRQTtZQUNOLEtBQUs7Z0JBQ0gscUJBQU8sOERBQUM1Riw4TEFBV0E7b0JBQUM2RixXQUFVOzs7Ozs7WUFDaEMsS0FBSztnQkFDSCxxQkFBTyw4REFBQy9GLDZMQUFLQTtvQkFBQytGLFdBQVU7Ozs7OztZQUMxQixLQUFLO1lBQ0wsS0FBSztnQkFDSCxxQkFBTyw4REFBQzlGLDhMQUFXQTtvQkFBQzhGLFdBQVU7Ozs7OztZQUNoQztnQkFDRSxxQkFBTyw4REFBQ2pHLDhMQUFhQTtvQkFBQ2lHLFdBQVU7Ozs7OztRQUNwQztJQUNGO0lBRUEsTUFBTUMsaUJBQWlCLENBQUNGO1FBQ3RCLE9BQVFBO1lBQ04sS0FBSztnQkFDSCxPQUFPO1lBQ1QsS0FBSztnQkFDSCxPQUFPO1lBQ1QsS0FBSztZQUNMLEtBQUs7Z0JBQ0gsT0FBTztZQUNUO2dCQUNFLE9BQU87UUFDWDtJQUNGO0lBRUEsTUFBTUcsbUJBQW1CLENBQUN4RDtRQUN4QixPQUFRQTtZQUNOLEtBQUs7Z0JBQ0gsT0FBTztZQUNULEtBQUs7Z0JBQ0gsT0FBTztZQUNULEtBQUs7Z0JBQ0gsT0FBTztZQUNULEtBQUs7Z0JBQ0gsT0FBTztZQUNUO2dCQUNFLE9BQU87UUFDWDtJQUNGO0lBRUEsSUFBSW5CLFNBQVM7UUFDWCxxQkFDRSw4REFBQzRFO1lBQUlILFdBQVU7c0JBQ2IsNEVBQUNHO2dCQUFJSCxXQUFVOztrQ0FDYiw4REFBQ0c7d0JBQUlILFdBQVU7Ozs7OztrQ0FDZiw4REFBQ0c7d0JBQUlILFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBSXZCO0lBRUEscUJBQ0UsOERBQUNHO1FBQUlILFdBQVU7OzBCQUViLDhEQUFDRztnQkFBSUgsV0FBVTs7a0NBQ2IsOERBQUNHOzswQ0FDQyw4REFBQ0M7Z0NBQUdKLFdBQVU7MENBQW1DOzs7Ozs7MENBQ2pELDhEQUFDSztnQ0FBRUwsV0FBVTswQ0FBcUI7Ozs7Ozs7Ozs7OztrQ0FFcEMsOERBQUN4RyxrREFBTUE7d0JBQ0w4RyxTQUFTLElBQU01RSxzQkFBc0I7d0JBQ3JDc0UsV0FBVTs7MENBRVYsOERBQUNoRyw4TEFBSUE7Z0NBQUNnRyxXQUFVOzs7Ozs7NEJBQVk7Ozs7Ozs7Ozs7Ozs7MEJBTWhDLDhEQUFDRztnQkFBSUgsV0FBVTswQkFDWCw0RUFBQ3RHLGdEQUFJQTs7c0NBQ0gsOERBQUNDLHNEQUFVQTtzQ0FDVCw0RUFBQ0MscURBQVNBO2dDQUFDb0csV0FBVTs7a0RBQ25CLDhEQUFDakcsOExBQWFBO3dDQUFDaUcsV0FBVTs7Ozs7O29DQUFZOzs7Ozs7Ozs7Ozs7c0NBSXpDLDhEQUFDbkcsdURBQVdBO3NDQUNUd0IsUUFBUTRDLE1BQU0sR0FBRyxrQkFDaEIsOERBQUNrQztnQ0FBSUgsV0FBVTswQ0FDWjNFLFFBQVErQixHQUFHLENBQUMsQ0FBQ21ELHVCQUNaLDhEQUFDSjt3Q0FFQ0csU0FBUyxJQUFNMUUsa0JBQWtCMkU7d0NBQ2pDUCxXQUFVOzswREFFViw4REFBQ0c7Z0RBQUlILFdBQVU7O2tFQUNiLDhEQUFDUTt3REFBR1IsV0FBVTtrRUFDWE8sT0FBTy9ELE9BQU87Ozs7OztrRUFFakIsOERBQUMyRDt3REFBSUgsV0FBVTs7NERBQ1pGLGNBQWNTLE9BQU9SLE1BQU07MEVBQzVCLDhEQUFDVTtnRUFBS1QsV0FBVyxrQ0FBZ0UsT0FBOUJDLGVBQWVNLE9BQU9SLE1BQU07MEVBQzVFUSxPQUFPUixNQUFNOzs7Ozs7Ozs7Ozs7Ozs7Ozs7MERBSXBCLDhEQUFDTTtnREFBRUwsV0FBVTswREFDVk8sT0FBTzlELE9BQU87Ozs7OzswREFFakIsOERBQUMwRDtnREFBSUgsV0FBVTs7a0VBQ2IsOERBQUNTO3dEQUFLVCxXQUFXLDBCQUE0RCxPQUFsQ0UsaUJBQWlCSyxPQUFPN0QsUUFBUTtrRUFDeEU2RCxPQUFPN0QsUUFBUTs7Ozs7O2tFQUVsQiw4REFBQytEO2tFQUFNN0YsMERBQWNBLENBQUMyRixPQUFPRyxTQUFTOzs7Ozs7Ozs7Ozs7O3VDQXRCbkNILE9BQU96RixFQUFFOzs7Ozs7Ozs7MERBNEJwQiw4REFBQ3FGO2dDQUFJSCxXQUFVOztrREFDYiw4REFBQ2pHLDhMQUFhQTt3Q0FBQ2lHLFdBQVU7Ozs7OztrREFDekIsOERBQUNXO3dDQUFHWCxXQUFVO2tEQUF5Qzs7Ozs7O2tEQUN2RCw4REFBQ0s7d0NBQUVMLFdBQVU7a0RBQWdCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQVF6Qyw4REFBQ0c7Z0JBQUlILFdBQVU7MEJBQ2IsNEVBQUN0RyxnREFBSUE7O3NDQUNILDhEQUFDQyxzREFBVUE7c0NBQ1QsNEVBQUNDLHFEQUFTQTtnQ0FBQ29HLFdBQVU7O2tEQUNuQiw4REFBQzFGLDhMQUFVQTt3Q0FBQzBGLFdBQVU7Ozs7OztvQ0FBWTs7Ozs7Ozs7Ozs7O3NDQUl0Qyw4REFBQ25HLHVEQUFXQTs7OENBRVYsOERBQUNzRztvQ0FBSUgsV0FBVTs4Q0FDYiw0RUFBQ0c7d0NBQUlILFdBQVU7OzBEQUNiLDhEQUFDRztnREFBSUgsV0FBVTswREFDYiw0RUFBQ3ZHLGlEQUFLQTtvREFDSm1ILGFBQVk7b0RBQ1pDLE9BQU85RTtvREFDUCtFLFVBQVUsQ0FBQzlCLElBQU1oRCxpQkFBaUJnRCxFQUFFK0IsTUFBTSxDQUFDRixLQUFLO29EQUNoRGIsV0FBVTs7Ozs7Ozs7Ozs7MERBR2QsOERBQUNHO2dEQUFJSCxXQUFVOzBEQUNiLDRFQUFDZ0I7b0RBQ0NILE9BQU81RTtvREFDUDZFLFVBQVUsQ0FBQzlCLElBQU05QyxvQkFBb0I4QyxFQUFFK0IsTUFBTSxDQUFDRixLQUFLO29EQUNuRGIsV0FBVTs4REFFVGhELFdBQVdJLEdBQUcsQ0FBQ3JDLENBQUFBLHlCQUNkLDhEQUFDa0c7NERBQXNCSixPQUFPOUY7c0VBQVdBOzJEQUE1QkE7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQVF2Qiw4REFBQ29GO29DQUFJSCxXQUFVOzhDQUNaNUIsWUFBWWhCLEdBQUcsQ0FBQyxDQUFDQzt3Q0FDaEIsTUFBTTZELGdCQUFnQjdELElBQUluQyxJQUFJO3dDQUM5QixxQkFDRSw4REFBQ2lGOzRDQUVDRyxTQUFTLElBQU14RSxlQUFldUI7NENBQzlCMkMsV0FBVTs7OERBRVYsOERBQUNHO29EQUFJSCxXQUFVOztzRUFDYiw4REFBQ0c7NERBQUlILFdBQVU7c0VBQ2IsNEVBQUNrQjtnRUFBY2xCLFdBQVU7Ozs7Ozs7Ozs7O3NFQUUzQiw4REFBQ0c7NERBQUlILFdBQVU7c0VBQ2IsNEVBQUNTO2dFQUFLVCxXQUFVOzBFQUNiM0MsSUFBSXRDLFFBQVE7Ozs7Ozs7Ozs7Ozs7Ozs7OzhEQUluQiw4REFBQ3lGO29EQUFHUixXQUFVOzhEQUNYM0MsSUFBSXJDLFFBQVE7Ozs7Ozs4REFFZiw4REFBQ3FGO29EQUFFTCxXQUFVOzhEQUNWM0MsSUFBSXBDLE1BQU0sQ0FBQ2tHLEtBQUssQ0FBQyxLQUFLLENBQUMsRUFBRTs7Ozs7OzhEQUU1Qiw4REFBQ2hCO29EQUFJSCxXQUFVOzt3REFDWjNDLElBQUlsQyxJQUFJLENBQUNrRCxLQUFLLENBQUMsR0FBRyxHQUFHakIsR0FBRyxDQUFDUSxDQUFBQSxvQkFDeEIsOERBQUM2QztnRUFBZVQsV0FBVTswRUFDdkJwQzsrREFEUUE7Ozs7O3dEQUlaUCxJQUFJbEMsSUFBSSxDQUFDOEMsTUFBTSxHQUFHLG1CQUNqQiw4REFBQ3dDOzREQUFLVCxXQUFVOztnRUFBd0I7Z0VBQUUzQyxJQUFJbEMsSUFBSSxDQUFDOEMsTUFBTSxHQUFHO2dFQUFFOzs7Ozs7Ozs7Ozs7OzsyQ0EzQjdEWixJQUFJdkMsRUFBRTs7Ozs7b0NBZ0NqQjs7Ozs7O2dDQUdEd0MsYUFBYVcsTUFBTSxLQUFLLG1CQUN2Qiw4REFBQ2tDO29DQUFJSCxXQUFVOztzREFDYiw4REFBQ3pGLDhMQUFRQTs0Q0FBQ3lGLFdBQVU7Ozs7OztzREFDcEIsOERBQUNXOzRDQUFHWCxXQUFVO3NEQUF5Qzs7Ozs7O3NEQUN2RCw4REFBQ0s7NENBQUVMLFdBQVU7c0RBQ1ZqRSxpQkFBaUJFLHFCQUFxQixRQUNuQyxrREFDQTs7Ozs7Ozs7Ozs7O2dDQU1UcUIsYUFBYVcsTUFBTSxHQUFHNUIsOEJBQ3JCLDhEQUFDOEQ7b0NBQUlILFdBQVU7O3NEQUNiLDhEQUFDRzs0Q0FBSUgsV0FBVTs7Z0RBQXdCO2dEQUM1QjlCLGFBQWE7Z0RBQUU7Z0RBQUtILEtBQUtxRCxHQUFHLENBQUNqRCxVQUFVYixhQUFhVyxNQUFNO2dEQUFFO2dEQUFLWCxhQUFhVyxNQUFNO2dEQUFDOzs7Ozs7O3NEQUVoRyw4REFBQ2tDOzRDQUFJSCxXQUFVOzs4REFDYiw4REFBQ3FCO29EQUNDZixTQUFTLElBQU1sRSxlQUFla0YsQ0FBQUEsT0FBUXZELEtBQUt3RCxHQUFHLENBQUNELE9BQU8sR0FBRztvREFDekRFLFVBQVVyRixnQkFBZ0I7b0RBQzFCNkQsV0FBVTs4REFDWDs7Ozs7O2dEQUtBL0MsTUFBTUMsSUFBSSxDQUFDO29EQUFFZSxRQUFRSDtnREFBVyxHQUFHLENBQUMyRCxHQUFHQyxJQUFNQSxJQUFJLEdBQUd0RSxHQUFHLENBQUN1RSxDQUFBQSxxQkFDdkQsOERBQUNOO3dEQUVDZixTQUFTLElBQU1sRSxlQUFldUY7d0RBQzlCM0IsV0FBVyw0Q0FJVixPQUhDN0QsZ0JBQWdCd0YsT0FDWiw0QkFDQTtrRUFHTEE7dURBUklBOzs7Ozs4REFZVCw4REFBQ047b0RBQ0NmLFNBQVMsSUFBTWxFLGVBQWVrRixDQUFBQSxPQUFRdkQsS0FBS3FELEdBQUcsQ0FBQ0UsT0FBTyxHQUFHeEQ7b0RBQ3pEMEQsVUFBVXJGLGdCQUFnQjJCO29EQUMxQmtDLFdBQVU7OERBQ1g7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQVdiLDhEQUFDbEcsaURBQUtBO2dCQUNKOEgsUUFBUW5HO2dCQUNSb0csU0FBUyxJQUFNbkcsc0JBQXNCO2dCQUNyQ29HLE9BQU07MEJBRU4sNEVBQUNDO29CQUFLQyxVQUFVakQ7b0JBQWNpQixXQUFVOztzQ0FDdEMsOERBQUN2RyxpREFBS0E7NEJBQ0p3SSxPQUFNOzRCQUNOcEIsT0FBT3ZFLGNBQWNFLE9BQU87NEJBQzVCc0UsVUFBVSxDQUFDOUIsSUFBTXpDLGlCQUFpQitFLENBQUFBLE9BQVM7d0NBQUUsR0FBR0EsSUFBSTt3Q0FBRTlFLFNBQVN3QyxFQUFFK0IsTUFBTSxDQUFDRixLQUFLO29DQUFDOzRCQUM5RUQsYUFBWTs0QkFDWnNCLFFBQVE7Ozs7OztzQ0FHViw4REFBQy9COzs4Q0FDQyw4REFBQzhCO29DQUFNakMsV0FBVTs4Q0FBK0M7Ozs7Ozs4Q0FHaEUsOERBQUNnQjtvQ0FDQ0gsT0FBT3ZFLGNBQWNJLFFBQVE7b0NBQzdCb0UsVUFBVSxDQUFDOUIsSUFBTXpDLGlCQUFpQitFLENBQUFBLE9BQVM7Z0RBQUUsR0FBR0EsSUFBSTtnREFBRTVFLFVBQVVzQyxFQUFFK0IsTUFBTSxDQUFDRixLQUFLOzRDQUFRO29DQUN0RmIsV0FBVTs7c0RBRVYsOERBQUNpQjs0Q0FBT0osT0FBTTtzREFBTTs7Ozs7O3NEQUNwQiw4REFBQ0k7NENBQU9KLE9BQU07c0RBQVM7Ozs7OztzREFDdkIsOERBQUNJOzRDQUFPSixPQUFNO3NEQUFPOzs7Ozs7c0RBQ3JCLDhEQUFDSTs0Q0FBT0osT0FBTTtzREFBUzs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQUkzQiw4REFBQ1Y7OzhDQUNDLDhEQUFDOEI7b0NBQU1qQyxXQUFVOzhDQUErQzs7Ozs7OzhDQUdoRSw4REFBQ21DO29DQUNDdEIsT0FBT3ZFLGNBQWNHLE9BQU87b0NBQzVCcUUsVUFBVSxDQUFDOUIsSUFBTXpDLGlCQUFpQitFLENBQUFBLE9BQVM7Z0RBQUUsR0FBR0EsSUFBSTtnREFBRTdFLFNBQVN1QyxFQUFFK0IsTUFBTSxDQUFDRixLQUFLOzRDQUFDO29DQUM5RUQsYUFBWTtvQ0FDWndCLE1BQU07b0NBQ05wQyxXQUFVO29DQUNWa0MsUUFBUTs7Ozs7Ozs7Ozs7O3NDQUlaLDhEQUFDL0I7NEJBQUlILFdBQVU7OzhDQUNiLDhEQUFDeEcsa0RBQU1BO29DQUNMNkksTUFBSztvQ0FDTEMsU0FBUTtvQ0FDUmhDLFNBQVMsSUFBTTVFLHNCQUFzQjtvQ0FDckNzRSxXQUFVOzhDQUNYOzs7Ozs7OENBR0QsOERBQUN4RyxrREFBTUE7b0NBQ0w2SSxNQUFLO29DQUNMOUcsU0FBU3NCO29DQUNUbUQsV0FBVTs4Q0FDWDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7WUFRTnJFLGdDQUNDLDhEQUFDN0IsaURBQUtBO2dCQUNKOEgsUUFBUSxDQUFDLENBQUNqRztnQkFDVmtHLFNBQVMsSUFBTWpHLGtCQUFrQjtnQkFDakNrRyxPQUFPLFdBQWtDLE9BQXZCbkcsZUFBZWEsT0FBTztnQkFDeEMrRixNQUFLOzBCQUVMLDRFQUFDcEM7b0JBQUlILFdBQVU7O3NDQUNiLDhEQUFDRzs0QkFBSUgsV0FBVTs7Z0NBQ1pGLGNBQWNuRSxlQUFlb0UsTUFBTTs4Q0FDcEMsOERBQUNVO29DQUFLVCxXQUFXLGtDQUF3RSxPQUF0Q0MsZUFBZXRFLGVBQWVvRSxNQUFNOzhDQUNwRnBFLGVBQWVvRSxNQUFNOzs7Ozs7OENBRXhCLDhEQUFDVTtvQ0FBS1QsV0FBVyxrQ0FBNEUsT0FBMUNFLGlCQUFpQnZFLGVBQWVlLFFBQVE7OENBQ3hGZixlQUFlZSxRQUFROzs7Ozs7Ozs7Ozs7c0NBSTVCLDhEQUFDeUQ7NEJBQUlILFdBQVU7OzhDQUNiLDhEQUFDSztvQ0FBRUwsV0FBVTs4Q0FBNkI7Ozs7Ozs4Q0FDMUMsOERBQUNLO29DQUFFTCxXQUFVOzhDQUFpQnJFLGVBQWVjLE9BQU87Ozs7Ozs4Q0FDcEQsOERBQUM0RDtvQ0FBRUwsV0FBVTs7d0NBQTZCO3dDQUM5QnBGLDBEQUFjQSxDQUFDZSxlQUFlK0UsU0FBUzs7Ozs7Ozs7Ozs7Ozt3QkFJcEQvRSxlQUFlNkcsU0FBUyxDQUFDdkUsTUFBTSxHQUFHLG1CQUNqQyw4REFBQ2tDOzRCQUFJSCxXQUFVOzs4Q0FDYiw4REFBQ1E7b0NBQUdSLFdBQVU7OENBQTRCOzs7Ozs7Z0NBQ3pDckUsZUFBZTZHLFNBQVMsQ0FBQ3BGLEdBQUcsQ0FBQyxDQUFDa0IseUJBQzdCLDhEQUFDNkI7d0NBRUNILFdBQVcsa0JBRVYsT0FEQzFCLFNBQVNtRSxPQUFPLEdBQUcsMENBQTBDOzswREFHL0QsOERBQUNwQztnREFBRUwsV0FBVTswREFBaUIxQixTQUFTN0IsT0FBTzs7Ozs7OzBEQUM5Qyw4REFBQzREO2dEQUFFTCxXQUFVOztvREFDVjFCLFNBQVNtRSxPQUFPLEdBQUcsaUJBQWlCO29EQUFNO29EQUFJN0gsMERBQWNBLENBQUMwRCxTQUFTb0MsU0FBUzs7Ozs7Ozs7dUNBUDdFcEMsU0FBU3hELEVBQUU7Ozs7Ozs7Ozs7O3dCQWN2QmEsZUFBZW9FLE1BQU0sS0FBSywwQkFDekIsOERBQUNJOzRCQUFJSCxXQUFVOzs4Q0FDYiw4REFBQ21DO29DQUNDdEIsT0FBT2xFO29DQUNQbUUsVUFBVSxDQUFDOUIsSUFBTXBDLGVBQWVvQyxFQUFFK0IsTUFBTSxDQUFDRixLQUFLO29DQUM5Q0QsYUFBWTtvQ0FDWndCLE1BQU07b0NBQ05wQyxXQUFVOzs7Ozs7OENBRVosOERBQUN4RyxrREFBTUE7b0NBQ0w4RyxTQUFTLElBQU1mLFlBQVk1RCxlQUFlYixFQUFFO29DQUM1QzBHLFVBQVUsQ0FBQzdFLFlBQVk4QyxJQUFJO29DQUMzQk8sV0FBVTs7c0RBRVYsOERBQUM1Riw4TEFBSUE7NENBQUM0RixXQUFVOzs7Ozs7d0NBQWlCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7WUFVNUNuRSw2QkFDQyw4REFBQy9CLGlEQUFLQTtnQkFDSjhILFFBQVEsQ0FBQyxDQUFDL0Y7Z0JBQ1ZnRyxTQUFTLElBQU0vRixlQUFlO2dCQUM5QmdHLE9BQU9qRyxZQUFZYixRQUFRO2dCQUMzQnVILE1BQUs7MEJBRUwsNEVBQUNwQztvQkFBSUgsV0FBVTs7c0NBQ2IsOERBQUNHOzRCQUFJSCxXQUFVOzs4Q0FDYiw4REFBQ0c7b0NBQUlILFdBQVU7OENBQ2IsNEVBQUNuRSxZQUFZWCxJQUFJO3dDQUFDOEUsV0FBVTs7Ozs7Ozs7Ozs7OENBRTlCLDhEQUFDRzs4Q0FDQyw0RUFBQ007d0NBQUtULFdBQVU7a0RBQ2JuRSxZQUFZZCxRQUFROzs7Ozs7Ozs7Ozs7Ozs7OztzQ0FLM0IsOERBQUNvRjs0QkFBSUgsV0FBVTtzQ0FDYiw0RUFBQ0c7Z0NBQUlILFdBQVU7MENBQ1puRSxZQUFZWixNQUFNOzs7Ozs7Ozs7OztzQ0FJdkIsOERBQUNrRjs0QkFBSUgsV0FBVTtzQ0FDYiw0RUFBQ0c7Z0NBQUlILFdBQVU7O2tEQUNiLDhEQUFDUzt3Q0FBS1QsV0FBVTtrREFBeUM7Ozs7OztvQ0FDeERuRSxZQUFZVixJQUFJLENBQUNpQyxHQUFHLENBQUNRLENBQUFBLG9CQUNwQiw4REFBQzZDOzRDQUFlVCxXQUFVO3NEQUN2QnBDOzJDQURRQTs7Ozs7Ozs7Ozs7Ozs7OztzQ0FPakIsOERBQUN1Qzs0QkFBSUgsV0FBVTs7OENBQ2IsOERBQUNLO29DQUFFTCxXQUFVOzhDQUE2Qjs7Ozs7OzhDQUcxQyw4REFBQ3hHLGtEQUFNQTtvQ0FDTDhHLFNBQVM7d0NBQ1B4RSxlQUFlO3dDQUNmSixzQkFBc0I7b0NBQ3hCO29DQUNBc0UsV0FBVTs7c0RBRVYsOERBQUNqRyw4TEFBYUE7NENBQUNpRyxXQUFVOzs7Ozs7d0NBQWlCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFTMUQsRUFBRTtHQTdqQlc1RTtLQUFBQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxkcmVhbVxcRGVza3RvcFxcSGFzaF9NaW5pbmdzXFxoYXNoY29yZXhcXHNyY1xcY29tcG9uZW50c1xcZGFzaGJvYXJkXFxTdXBwb3J0Q2VudGVyLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgQnV0dG9uLCBJbnB1dCwgQ2FyZCwgQ2FyZEhlYWRlciwgQ2FyZFRpdGxlLCBDYXJkQ29udGVudCwgTW9kYWwgfSBmcm9tICdAL2NvbXBvbmVudHMvdWknO1xuaW1wb3J0IHsgR3JpZCB9IGZyb20gJ0AvY29tcG9uZW50cy9sYXlvdXQnO1xuaW1wb3J0IHtcbiAgTWVzc2FnZUNpcmNsZSxcbiAgUGx1cyxcbiAgQ2xvY2ssXG4gIENoZWNrQ2lyY2xlLFxuICBBbGVydENpcmNsZSxcbiAgU2VuZCxcbiAgQmVsbCxcbiAgU2V0dGluZ3MsXG4gIE1haWwsXG4gIFNtYXJ0cGhvbmUsXG4gIEhlbHBDaXJjbGUsXG4gIEJvb2tPcGVuLFxuICBDcmVkaXRDYXJkLFxuICBVc2VycyxcbiAgU2hpZWxkLFxuICBUcmVuZGluZ1VwLFxuICBXYWxsZXRcbn0gZnJvbSAnbHVjaWRlLXJlYWN0JztcbmltcG9ydCB7IGZvcm1hdERhdGVUaW1lIH0gZnJvbSAnQC9saWIvdXRpbHMnO1xuXG5pbnRlcmZhY2UgU3VwcG9ydFRpY2tldCB7XG4gIGlkOiBzdHJpbmc7XG4gIHN1YmplY3Q6IHN0cmluZztcbiAgbWVzc2FnZTogc3RyaW5nO1xuICBzdGF0dXM6ICdPUEVOJyB8ICdJTl9QUk9HUkVTUycgfCAnUkVTT0xWRUQnIHwgJ0NMT1NFRCc7XG4gIHByaW9yaXR5OiAnTE9XJyB8ICdNRURJVU0nIHwgJ0hJR0gnIHwgJ1VSR0VOVCc7XG4gIGNyZWF0ZWRBdDogc3RyaW5nO1xuICB1cGRhdGVkQXQ6IHN0cmluZztcbiAgcmVzcG9uc2VzOiBBcnJheTx7XG4gICAgaWQ6IHN0cmluZztcbiAgICBtZXNzYWdlOiBzdHJpbmc7XG4gICAgaXNBZG1pbjogYm9vbGVhbjtcbiAgICBjcmVhdGVkQXQ6IHN0cmluZztcbiAgfT47XG59XG5cbmludGVyZmFjZSBGQVFJdGVtIHtcbiAgaWQ6IHN0cmluZztcbiAgY2F0ZWdvcnk6IHN0cmluZztcbiAgcXVlc3Rpb246IHN0cmluZztcbiAgYW5zd2VyOiBzdHJpbmc7XG4gIGljb246IFJlYWN0LkNvbXBvbmVudFR5cGU8YW55PjtcbiAgdGFnczogc3RyaW5nW107XG59XG5cblxuXG4vLyBGQVEgRGF0YVxuY29uc3QgZmFxRGF0YTogRkFRSXRlbVtdID0gW1xuICB7XG4gICAgaWQ6ICcxJyxcbiAgICBjYXRlZ29yeTogJ0dldHRpbmcgU3RhcnRlZCcsXG4gICAgcXVlc3Rpb246ICdIb3cgZG8gSSBzdGFydCBtaW5pbmcgd2l0aCBIYXNoQ29yZVg/JyxcbiAgICBhbnN3ZXI6IGBUbyBzdGFydCBtaW5pbmcgd2l0aCBIYXNoQ29yZVgsIGZvbGxvdyB0aGVzZSBzaW1wbGUgc3RlcHM6XG5cbjEuIENvbXBsZXRlIEtZQyBWZXJpZmljYXRpb246IEZpcnN0LCBjb21wbGV0ZSB5b3VyIEtZQyB2ZXJpZmljYXRpb24gaW4gdGhlIFByb2ZpbGUgc2VjdGlvbi4gVGhpcyBpcyByZXF1aXJlZCBmb3IgYWxsIG1pbmluZyBhY3Rpdml0aWVzLlxuXG4yLiBEZXBvc2l0IEZ1bmRzOiBHbyB0byB5b3VyIFdhbGxldCBhbmQgZGVwb3NpdCBVU0RUIChUUkMyMCkgdG8gZnVuZCB5b3VyIGFjY291bnQuIFRoZSBtaW5pbXVtIGRlcG9zaXQgYW1vdW50IGlzIGNvbmZpZ3VyZWQgYnkgdGhlIGFkbWluLlxuXG4zLiBQdXJjaGFzZSBNaW5pbmcgVW5pdHM6IE5hdmlnYXRlIHRvIHRoZSBNaW5pbmcgc2VjdGlvbiBhbmQgcHVyY2hhc2UgbWluaW5nIHVuaXRzLiBDaG9vc2UgeW91ciBkZXNpcmVkIFRIL3MgYW1vdW50IGFuZCBpbnZlc3RtZW50IGxldmVsLlxuXG40LiBTdGFydCBFYXJuaW5nOiBZb3VyIG1pbmluZyB1bml0cyB3aWxsIHN0YXJ0IGdlbmVyYXRpbmcgZGFpbHkgcmV0dXJucyBhdXRvbWF0aWNhbGx5LiBFYXJuaW5ncyBhcmUgY2FsY3VsYXRlZCBiYXNlZCBvbiB5b3VyIFRIL3MgYW1vdW50IGFuZCBjdXJyZW50IG1hcmtldCBjb25kaXRpb25zLlxuXG41LiBUcmFjayBQcm9ncmVzczogTW9uaXRvciB5b3VyIGVhcm5pbmdzIGluIHRoZSBFYXJuaW5ncyBzZWN0aW9uIGFuZCB3YXRjaCB5b3VyIG1pbmluZyB1bml0cyBwcm9ncmVzcyB0b3dhcmQgdGhlaXIgNXggaW52ZXN0bWVudCByZXR1cm4gbGltaXQuXG5cblJlbWVtYmVyOiBNaW5pbmcgdW5pdHMgZXhwaXJlIGFmdGVyIDI0IG1vbnRocyBvciB3aGVuIHRoZXkgcmVhY2ggNXggdGhlaXIgaW52ZXN0bWVudCBhbW91bnQsIHdoaWNoZXZlciBjb21lcyBmaXJzdC5gLFxuICAgIGljb246IFRyZW5kaW5nVXAsXG4gICAgdGFnczogWydtaW5pbmcnLCAnZ2V0dGluZyBzdGFydGVkJywgJ2t5YycsICdkZXBvc2l0J11cbiAgfSxcbiAge1xuICAgIGlkOiAnMicsXG4gICAgY2F0ZWdvcnk6ICdNaW5pbmcgVW5pdHMnLFxuICAgIHF1ZXN0aW9uOiAnSG93IGFyZSBkYWlseSByZXR1cm5zIGNhbGN1bGF0ZWQ/JyxcbiAgICBhbnN3ZXI6IGBEYWlseSByZXR1cm5zIGFyZSBjYWxjdWxhdGVkIHVzaW5nIGEgZHluYW1pYyBzeXN0ZW0gYmFzZWQgb24geW91ciBtaW5pbmcgdW5pdCdzIFRIL3MgYW1vdW50OlxuXG5EeW5hbWljIFJPSSBTeXN0ZW06XG7igKIgRGlmZmVyZW50IFRIL3MgcmFuZ2VzIGhhdmUgZGlmZmVyZW50IHJldHVybiByYXRlc1xu4oCiIFJldHVybnMgYXJlIGNhbGN1bGF0ZWQgYXM6IChJbnZlc3RtZW50IEFtb3VudCDDlyBEYWlseSBST0klKSDDtyAxMDBcbuKAoiBST0kgcGVyY2VudGFnZXMgYXJlIGRldGVybWluZWQgYnkgYWRtaW4tY29uZmlndXJlZCByYW5nZXNcblxuRXhhbXBsZSBSYW5nZXM6XG7igKIgMC0xMCBUSC9zOiAwLjMlIC0gMC41JSBkYWlseVxu4oCiIDEwLTUwIFRIL3M6IDAuNCUgLSAwLjYlIGRhaWx5XG7igKIgNTArIFRIL3M6IDAuNSUgLSAwLjclIGRhaWx5XG5cbkltcG9ydGFudCBOb3RlczpcbuKAoiBSZXR1cm5zIGFyZSBjcmVkaXRlZCBkYWlseSBidXQgcGFpZCBvdXQgd2Vla2x5IChTdW5kYXlzIGF0IDAwOjAwIEFNIEdNVCs1OjMwKVxu4oCiIE1pbmluZyB1bml0cyBleHBpcmUgd2hlbiB0aGV5IHJlYWNoIDV4IHRoZWlyIGludmVzdG1lbnQgYW1vdW50XG7igKIgRklGTyBzeXN0ZW06IE9sZGVzdCB1bml0cyByZWNlaXZlIGVhcm5pbmdzIGZpcnN0XG7igKIgQWxsIGVhcm5pbmdzIGFyZSBzdWJqZWN0IHRvIG1hcmtldCBjb25kaXRpb25zIGFuZCBhZG1pbiBjb25maWd1cmF0aW9uYCxcbiAgICBpY29uOiBUcmVuZGluZ1VwLFxuICAgIHRhZ3M6IFsnbWluaW5nJywgJ3JldHVybnMnLCAnY2FsY3VsYXRpb24nLCAncm9pJ11cbiAgfSxcbiAge1xuICAgIGlkOiAnMycsXG4gICAgY2F0ZWdvcnk6ICdXYWxsZXQgJiBQYXltZW50cycsXG4gICAgcXVlc3Rpb246ICdIb3cgZG8gZGVwb3NpdHMgYW5kIHdpdGhkcmF3YWxzIHdvcms/JyxcbiAgICBhbnN3ZXI6IGBEZXBvc2l0czpcbuKAoiBPbmx5IFVTRFQgKFRSQzIwKSBkZXBvc2l0cyBhcmUgYWNjZXB0ZWRcbuKAoiBNaW5pbXVtIGRlcG9zaXQgYW1vdW50IGlzIHNldCBieSBhZG1pbiAodHlwaWNhbGx5ICQ1MClcbuKAoiBEZXBvc2l0cyBhcmUgYXV0b21hdGljYWxseSB2ZXJpZmllZCBvbiB0aGUgVHJvbiBibG9ja2NoYWluXG7igKIgRnVuZHMgYXJlIGF2YWlsYWJsZSBpbW1lZGlhdGVseSBhZnRlciBjb25maXJtYXRpb25cblxuV2l0aGRyYXdhbHM6XG7igKIgTWluaW11bSB3aXRoZHJhd2FsOiAkNTBcbuKAoiBGaXhlZCBmZWU6ICQzICsgMSUgb2Ygd2l0aGRyYXdhbCBhbW91bnRcbuKAoiBQcm9jZXNzaW5nIHRpbWU6IFVwIHRvIDMgYnVzaW5lc3MgZGF5c1xu4oCiIE9ubHkgdG8gdmVyaWZpZWQgVFJDMjAgYWRkcmVzc2VzXG5cbldhbGxldCBCYWxhbmNlOlxu4oCiIEF2YWlsYWJsZSBCYWxhbmNlOiBGdW5kcyByZWFkeSBmb3IgdXNlIG9yIHdpdGhkcmF3YWxcbuKAoiBQZW5kaW5nIEJhbGFuY2U6IEVhcm5pbmdzIHdhaXRpbmcgZm9yIHdlZWtseSBkaXN0cmlidXRpb25cbuKAoiBUb3RhbCBFYXJuaW5nczogQ3VtdWxhdGl2ZSBlYXJuaW5ncyBmcm9tIGFsbCBzb3VyY2VzXG5cbkltcG9ydGFudDpcbuKAoiBDb21wbGV0ZSBLWUMgdmVyaWZpY2F0aW9uIGJlZm9yZSBtYWtpbmcgd2l0aGRyYXdhbHNcbuKAoiBFbnN1cmUgeW91ciBUUkMyMCBhZGRyZXNzIGlzIGNvcnJlY3QgYmVmb3JlIHN1Ym1pdHRpbmdcbuKAoiBXaXRoZHJhd2FsIGZlZXMgYXJlIGRlZHVjdGVkIGZyb20geW91ciBiYWxhbmNlYCxcbiAgICBpY29uOiBXYWxsZXQsXG4gICAgdGFnczogWyd3YWxsZXQnLCAnZGVwb3NpdCcsICd3aXRoZHJhd2FsJywgJ3VzZHQnLCAndHJjMjAnXVxuICB9LFxuICB7XG4gICAgaWQ6ICc0JyxcbiAgICBjYXRlZ29yeTogJ1JlZmVycmFsIFN5c3RlbScsXG4gICAgcXVlc3Rpb246ICdIb3cgZG9lcyB0aGUgYmluYXJ5IHJlZmVycmFsIHN5c3RlbSB3b3JrPycsXG4gICAgYW5zd2VyOiBgSGFzaENvcmVYIHVzZXMgYSBiaW5hcnkgdHJlZSByZWZlcnJhbCBzeXN0ZW0gd2l0aCB0aHJlZSBwbGFjZW1lbnQgdHlwZXM6XG5cblBsYWNlbWVudCBUeXBlczpcbjEuIEdlbmVyYWwgUmVmZXJyYWw6IFBsYWNlZCBpbiB3ZWFrZXIgbGVnIGF1dG9tYXRpY2FsbHlcbjIuIExlZnQtU2lkZSBSZWZlcnJhbDogU3BlY2lmaWNhbGx5IHBsYWNlZCBvbiBsZWZ0IHNpZGVcbjMuIFJpZ2h0LVNpZGUgUmVmZXJyYWw6IFNwZWNpZmljYWxseSBwbGFjZWQgb24gcmlnaHQgc2lkZVxuXG5FYXJuaW5nIFN0cnVjdHVyZTpcbuKAoiBEaXJlY3QgUmVmZXJyYWwgQ29tbWlzc2lvbjogMTAlIG9uZS10aW1lIGJvbnVzIHdoZW4geW91ciByZWZlcnJhbCBwdXJjaGFzZXMgbWluaW5nIHVuaXRzXG7igKIgQmluYXJ5IE1hdGNoaW5nIEJvbnVzOiBXZWVrbHkgbWF0Y2hpbmcgb2YgYmluYXJ5IHBvaW50cyAoMSBwb2ludCA9ICQxMClcblxuQmluYXJ5IFBvaW50czpcbuKAoiBFYXJuZWQgd2hlbiB5b3VyIHJlZmVycmFscyBwdXJjaGFzZSBtaW5pbmcgdW5pdHNcbuKAoiAkMTUwIHB1cmNoYXNlID0gMS41IHBvaW50cyAoc3VwcG9ydHMgMiBkZWNpbWFsIHBsYWNlcylcbuKAoiBQb2ludHMgYXJlIG1hdGNoZWQgd2Vla2x5IGJldHdlZW4gbGVmdCBhbmQgcmlnaHQgc2lkZXNcbuKAoiBFeGNlc3MgcG9pbnRzIHJlc2V0IHRvIDAgYWZ0ZXIgbWF0Y2hpbmdcblxuUmVxdWlyZW1lbnRzOlxu4oCiIFlvdSBtdXN0IGhhdmUgYWN0aXZlIG1pbmluZyB1bml0cyB0byBlYXJuIGNvbW1pc3Npb25zXG7igKIgQmluYXJ5IG1hdGNoaW5nIG9jY3VycyB3ZWVrbHkgYXQgMTU6MDAgVVRDXG7igKIgQWxsIGVhcm5pbmdzIGFyZSBhbGxvY2F0ZWQgdG8geW91ciBtaW5pbmcgdW5pdHMgdXNpbmcgRklGTyBzeXN0ZW1gLFxuICAgIGljb246IFVzZXJzLFxuICAgIHRhZ3M6IFsncmVmZXJyYWwnLCAnYmluYXJ5JywgJ2NvbW1pc3Npb24nLCAncG9pbnRzJywgJ21hdGNoaW5nJ11cbiAgfSxcbiAge1xuICAgIGlkOiAnNScsXG4gICAgY2F0ZWdvcnk6ICdBY2NvdW50ICYgU2VjdXJpdHknLFxuICAgIHF1ZXN0aW9uOiAnV2hhdCBpcyBLWUMgYW5kIHdoeSBpcyBpdCByZXF1aXJlZD8nLFxuICAgIGFuc3dlcjogYEtZQyAoS25vdyBZb3VyIEN1c3RvbWVyKSBWZXJpZmljYXRpb246XG5cbktZQyBpcyBhIG1hbmRhdG9yeSB2ZXJpZmljYXRpb24gcHJvY2VzcyByZXF1aXJlZCBmb3IgYWxsIEhhc2hDb3JlWCB1c2VycyB0byBlbnN1cmUgY29tcGxpYW5jZSB3aXRoIGZpbmFuY2lhbCByZWd1bGF0aW9ucy5cblxuS1lDIFByb2Nlc3M6XG4xLiBQZXJzb25hbCBJbmZvcm1hdGlvbjogUHJvdmlkZSBhY2N1cmF0ZSBwZXJzb25hbCBkZXRhaWxzXG4yLiBJRCBEb2N1bWVudDogVXBsb2FkIGdvdmVybm1lbnQtaXNzdWVkIElEIChwYXNzcG9ydCwgZHJpdmVyJ3MgbGljZW5zZSwgZXRjLilcbjMuIFNlbGZpZSBWZXJpZmljYXRpb246IFRha2UgYSBzZWxmaWUgaG9sZGluZyB5b3VyIElEIGRvY3VtZW50XG40LiBBZG1pbiBSZXZpZXc6IE91ciB0ZWFtIHJldmlld3MgeW91ciBzdWJtaXNzaW9uICh0eXBpY2FsbHkgMjQtNDggaG91cnMpXG5cbktZQyBTdGF0dXM6XG7igKIgTm90IFN1Ym1pdHRlZDogS1lDIGRvY3VtZW50cyBub3QgeWV0IHVwbG9hZGVkXG7igKIgUGVuZGluZzogVW5kZXIgYWRtaW4gcmV2aWV3XG7igKIgQXBwcm92ZWQ6IFZlcmlmaWNhdGlvbiBjb21wbGV0ZSAtIGZ1bGwgYWNjZXNzIGdyYW50ZWRcbuKAoiBSZWplY3RlZDogUmVzdWJtaXNzaW9uIHJlcXVpcmVkIHdpdGggY29ycmVjdCBkb2N1bWVudHNcblxuV2h5IEtZQyBpcyBSZXF1aXJlZDpcbuKAoiBMZWdhbCBjb21wbGlhbmNlIHdpdGggZmluYW5jaWFsIHJlZ3VsYXRpb25zXG7igKIgQWNjb3VudCBzZWN1cml0eSBhbmQgZnJhdWQgcHJldmVudGlvblxu4oCiIEVuYWJsZXMgd2l0aGRyYXdhbHMgYW5kIGZ1bGwgcGxhdGZvcm0gYWNjZXNzXG7igKIgUHJvdGVjdHMgYm90aCB1c2VycyBhbmQgdGhlIHBsYXRmb3JtXG5cbkltcG9ydGFudDogUHJvZmlsZSBuYW1lIGZpZWxkcyBiZWNvbWUgcmVhZC1vbmx5IGFmdGVyIEtZQyBzdWJtaXNzaW9uIHRvIHByZXZlbnQgZnJhdWQuYCxcbiAgICBpY29uOiBTaGllbGQsXG4gICAgdGFnczogWydreWMnLCAndmVyaWZpY2F0aW9uJywgJ3NlY3VyaXR5JywgJ2NvbXBsaWFuY2UnLCAnaWRlbnRpdHknXVxuICB9LFxuICB7XG4gICAgaWQ6ICc2JyxcbiAgICBjYXRlZ29yeTogJ1RlY2huaWNhbCBTdXBwb3J0JyxcbiAgICBxdWVzdGlvbjogJ1doYXQgc2hvdWxkIEkgZG8gaWYgSSBlbmNvdW50ZXIgdGVjaG5pY2FsIGlzc3Vlcz8nLFxuICAgIGFuc3dlcjogYENvbW1vbiBTb2x1dGlvbnM6XG5cbkxvZ2luIElzc3VlczpcbuKAoiBDbGVhciBicm93c2VyIGNhY2hlIGFuZCBjb29raWVzXG7igKIgVHJ5IGluY29nbml0by9wcml2YXRlIGJyb3dzaW5nIG1vZGVcbuKAoiBFbnN1cmUgeW91J3JlIHVzaW5nIHRoZSBjb3JyZWN0IGVtYWlsIGFkZHJlc3NcbuKAoiBDaGVjayBpZiB5b3VyIGFjY291bnQgaXMgYWN0aXZlXG5cblRyYW5zYWN0aW9uIElzc3VlczpcbuKAoiBWZXJpZnkgdHJhbnNhY3Rpb24gaGFzaCBvbiBUcm9uIGJsb2NrY2hhaW4gZXhwbG9yZXJcbuKAoiBDaGVjayBpZiB5b3UncmUgdXNpbmcgdGhlIGNvcnJlY3QgbmV0d29yayAoTWFpbm5ldC9UZXN0bmV0KVxu4oCiIEVuc3VyZSBzdWZmaWNpZW50IGJhbGFuY2UgZm9yIGZlZXNcbuKAoiBXYWl0IGZvciBibG9ja2NoYWluIGNvbmZpcm1hdGlvbnNcblxuRGlzcGxheSBJc3N1ZXM6XG7igKIgUmVmcmVzaCB0aGUgcGFnZVxu4oCiIFRyeSBhIGRpZmZlcmVudCBicm93c2VyXG7igKIgRGlzYWJsZSBicm93c2VyIGV4dGVuc2lvbnMgdGVtcG9yYXJpbHlcbuKAoiBDaGVjayB5b3VyIGludGVybmV0IGNvbm5lY3Rpb25cblxuU3RpbGwgTmVlZCBIZWxwP1xuMS4gQ3JlYXRlIGEgc3VwcG9ydCB0aWNrZXQgd2l0aCBkZXRhaWxlZCBpbmZvcm1hdGlvblxuMi4gSW5jbHVkZSBzY3JlZW5zaG90cyBpZiBwb3NzaWJsZVxuMy4gUHJvdmlkZSB0cmFuc2FjdGlvbiBoYXNoZXMgZm9yIHBheW1lbnQgaXNzdWVzXG40LiBTcGVjaWZ5IHlvdXIgYnJvd3NlciBhbmQgZGV2aWNlIHR5cGVcblxuUmVzcG9uc2UgVGltZXM6XG7igKIgR2VuZXJhbCBpbnF1aXJpZXM6IDI0LTQ4IGhvdXJzXG7igKIgVGVjaG5pY2FsIGlzc3VlczogMTItMjQgaG91cnNcbuKAoiBQYXltZW50IGlzc3VlczogUHJpb3JpdHkgaGFuZGxpbmcgd2l0aGluIDEyIGhvdXJzYCxcbiAgICBpY29uOiBTZXR0aW5ncyxcbiAgICB0YWdzOiBbJ3RlY2huaWNhbCcsICdzdXBwb3J0JywgJ3Ryb3VibGVzaG9vdGluZycsICdsb2dpbicsICd0cmFuc2FjdGlvbnMnXVxuICB9LFxuICB7XG4gICAgaWQ6ICc3JyxcbiAgICBjYXRlZ29yeTogJ01pbmluZyBVbml0cycsXG4gICAgcXVlc3Rpb246ICdXaGF0IGhhcHBlbnMgd2hlbiBtaW5pbmcgdW5pdHMgZXhwaXJlPycsXG4gICAgYW5zd2VyOiBgTWluaW5nIHVuaXRzIGV4cGlyZSB1bmRlciB0d28gY29uZGl0aW9uczpcblxuRXhwaXJ5IENvbmRpdGlvbnM6XG7igKIgQWZ0ZXIgMjQgbW9udGhzIGZyb20gcHVyY2hhc2UgZGF0ZVxu4oCiIFdoZW4gdGhleSByZWFjaCA1eCB0aGVpciBpbnZlc3RtZW50IGFtb3VudCAod2hpY2hldmVyIGNvbWVzIGZpcnN0KVxuXG5GSUZPIEV4cGlyeSBTeXN0ZW06XG7igKIgT2xkZXN0IG1pbmluZyB1bml0cyBleHBpcmUgZmlyc3RcbuKAoiBFYXJuaW5ncyBhcmUgYWxsb2NhdGVkIHRvIG9sZGVzdCB1bml0cyBmaXJzdFxu4oCiIFRoaXMgZW5zdXJlcyBmYWlyIGRpc3RyaWJ1dGlvbiBhbmQgbWF4aW11bSByZXR1cm5zXG5cbldoYXQgSGFwcGVucyBhdCBFeHBpcnk6XG7igKIgVW5pdCBzdG9wcyBnZW5lcmF0aW5nIGRhaWx5IHJldHVybnNcbuKAoiBBbGwgYWNjdW11bGF0ZWQgZWFybmluZ3MgYXJlIGNyZWRpdGVkIHRvIHlvdXIgd2FsbGV0XG7igKIgVW5pdCBzdGF0dXMgY2hhbmdlcyB0byBcIkV4cGlyZWRcIlxu4oCiIE5vIGZ1cnRoZXIgZWFybmluZ3MgZnJvbSB0aGF0IHNwZWNpZmljIHVuaXRcblxuSW1wb3J0YW50IE5vdGVzOlxu4oCiIFlvdSBjYW4gdHJhY2sgZWFjaCB1bml0J3MgcHJvZ3Jlc3MgdG93YXJkIDV4IHJldHVyblxu4oCiIEV4cGlyZWQgdW5pdHMgYXJlIHNob3duIGluIHlvdXIgbWluaW5nIGhpc3RvcnlcbuKAoiBQdXJjaGFzZSBuZXcgdW5pdHMgdG8gY29udGludWUgZWFybmluZ1xu4oCiIEFsbCBlYXJuaW5ncyBhcmUgc3ViamVjdCB0byB3ZWVrbHkgcGF5b3V0IHNjaGVkdWxlYCxcbiAgICBpY29uOiBDbG9jayxcbiAgICB0YWdzOiBbJ21pbmluZycsICdleHBpcnknLCAnZmlmbycsICdyZXR1cm5zJywgJ3VuaXRzJ11cbiAgfSxcbiAge1xuICAgIGlkOiAnOCcsXG4gICAgY2F0ZWdvcnk6ICdXYWxsZXQgJiBQYXltZW50cycsXG4gICAgcXVlc3Rpb246ICdIb3cgZG8gSSBzZXQgdXAgbXkgVFJDMjAgd2FsbGV0IGFkZHJlc3M/JyxcbiAgICBhbnN3ZXI6IGBTZXR0aW5nIHVwIHlvdXIgVFJDMjAgd2FsbGV0IGFkZHJlc3MgZm9yIHdpdGhkcmF3YWxzOlxuXG5TdXBwb3J0ZWQgV2FsbGV0czpcbuKAoiBUcm9uTGluayAoQnJvd3NlciBleHRlbnNpb24pXG7igKIgVHJ1c3QgV2FsbGV0IChNb2JpbGUgYXBwKVxu4oCiIEF0b21pYyBXYWxsZXQgKERlc2t0b3AvTW9iaWxlKVxu4oCiIEFueSB3YWxsZXQgc3VwcG9ydGluZyBUUkMyMCB0b2tlbnNcblxuU2V0dXAgUHJvY2VzczpcbjEuIEdvIHRvIFByb2ZpbGUgU2V0dGluZ3Mg4oaSIEJpbGxpbmcgJiBQYXltZW50c1xuMi4gRW50ZXIgeW91ciBUUkMyMCB3YWxsZXQgYWRkcmVzc1xuMy4gVmVyaWZ5IHRoZSBhZGRyZXNzIGlzIGNvcnJlY3QgKGRvdWJsZS1jaGVjayEpXG40LiBTYXZlIHRoZSBhZGRyZXNzIGZvciBmdXR1cmUgd2l0aGRyYXdhbHNcblxuSW1wb3J0YW50IFNlY3VyaXR5IFRpcHM6XG7igKIgQWx3YXlzIGNvcHktcGFzdGUgYWRkcmVzc2VzLCBuZXZlciB0eXBlIG1hbnVhbGx5XG7igKIgVGVzdCB3aXRoIGEgc21hbGwgYW1vdW50IGZpcnN0XG7igKIgRW5zdXJlIHlvdXIgd2FsbGV0IHN1cHBvcnRzIFVTRFQgVFJDMjBcbuKAoiBLZWVwIHlvdXIgcHJpdmF0ZSBrZXlzIHNlY3VyZSBhbmQgbmV2ZXIgc2hhcmUgdGhlbVxu4oCiIFVzZSBvbmx5IHlvdXIgb3duIHdhbGxldCBhZGRyZXNzZXNcblxuTmV0d29yayBJbmZvcm1hdGlvbjpcbuKAoiBOZXR3b3JrOiBUcm9uIChUUlgpXG7igKIgVG9rZW4gU3RhbmRhcmQ6IFRSQzIwXG7igKIgU3VwcG9ydGVkIFRva2VuOiBVU0RUXG7igKIgV2l0aGRyYXdhbCBmZWVzIGFwcGx5IGFzIHBlciBwbGF0Zm9ybSBzZXR0aW5nc2AsXG4gICAgaWNvbjogV2FsbGV0LFxuICAgIHRhZ3M6IFsnd2FsbGV0JywgJ3RyYzIwJywgJ3NldHVwJywgJ3dpdGhkcmF3YWwnLCAnYWRkcmVzcyddXG4gIH0sXG4gIHtcbiAgICBpZDogJzknLFxuICAgIGNhdGVnb3J5OiAnUmVmZXJyYWwgU3lzdGVtJyxcbiAgICBxdWVzdGlvbjogJ0hvdyBjYW4gSSBtYXhpbWl6ZSBteSByZWZlcnJhbCBlYXJuaW5ncz8nLFxuICAgIGFuc3dlcjogYFN0cmF0ZWdpZXMgdG8gbWF4aW1pemUgeW91ciByZWZlcnJhbCBlYXJuaW5nczpcblxuQnVpbGRpbmcgWW91ciBOZXR3b3JrOlxu4oCiIFNoYXJlIHlvdXIgcmVmZXJyYWwgbGlua3Mgb24gc29jaWFsIG1lZGlhXG7igKIgRXhwbGFpbiB0aGUgYmVuZWZpdHMgb2YgSGFzaENvcmVYIHRvIGZyaWVuZHMgYW5kIGZhbWlseVxu4oCiIFVzZSBkaWZmZXJlbnQgcGxhY2VtZW50IHR5cGVzIHN0cmF0ZWdpY2FsbHlcbuKAoiBGb2N1cyBvbiBxdWFsaXR5IHJlZmVycmFscyB3aG8gd2lsbCBiZSBhY3RpdmVcblxuUGxhY2VtZW50IFN0cmF0ZWd5Olxu4oCiIFVzZSBnZW5lcmFsIHJlZmVycmFscyBmb3IgYXV0b21hdGljIGJhbGFuY2luZ1xu4oCiIFVzZSBsZWZ0L3JpZ2h0IHNwZWNpZmljIHBsYWNlbWVudHMgZm9yIHN0cmF0ZWdpYyBidWlsZGluZ1xu4oCiIE1vbml0b3IgeW91ciBiaW5hcnkgdHJlZSBiYWxhbmNlIHJlZ3VsYXJseVxu4oCiIEhlbHAgeW91ciByZWZlcnJhbHMgdW5kZXJzdGFuZCB0aGUgc3lzdGVtXG5cbkVhcm5pbmcgT3B0aW1pemF0aW9uOlxu4oCiIE1haW50YWluIGFjdGl2ZSBtaW5pbmcgdW5pdHMgdG8gZWFybiBjb21taXNzaW9uc1xu4oCiIEZvY3VzIG9uIGJvdGggZGlyZWN0IHJlZmVycmFscyBhbmQgYmluYXJ5IG1hdGNoaW5nXG7igKIgVHJhY2sgeW91ciBiaW5hcnkgcG9pbnRzIHdlZWtseVxu4oCiIFJlaW52ZXN0IGVhcm5pbmdzIHRvIHB1cmNoYXNlIG1vcmUgbWluaW5nIHVuaXRzXG5cbkJlc3QgUHJhY3RpY2VzOlxu4oCiIFByb3ZpZGUgc3VwcG9ydCB0byB5b3VyIHJlZmVycmFsc1xu4oCiIFNoYXJlIGVkdWNhdGlvbmFsIGNvbnRlbnQgYWJvdXQgbWluaW5nXG7igKIgQmUgdHJhbnNwYXJlbnQgYWJvdXQgcmlza3MgYW5kIHJld2FyZHNcbuKAoiBCdWlsZCBsb25nLXRlcm0gcmVsYXRpb25zaGlwcywgbm90IGp1c3QgcXVpY2sgcmVmZXJyYWxzXG5cblJlbWVtYmVyOiBZb3UgbXVzdCBoYXZlIGFjdGl2ZSBtaW5pbmcgdW5pdHMgdG8gZWFybiBhbnkgcmVmZXJyYWwgY29tbWlzc2lvbnMuYCxcbiAgICBpY29uOiBUcmVuZGluZ1VwLFxuICAgIHRhZ3M6IFsncmVmZXJyYWwnLCAnZWFybmluZ3MnLCAnc3RyYXRlZ3knLCAnbmV0d29yaycsICdvcHRpbWl6YXRpb24nXVxuICB9LFxuICB7XG4gICAgaWQ6ICcxMCcsXG4gICAgY2F0ZWdvcnk6ICdBY2NvdW50ICYgU2VjdXJpdHknLFxuICAgIHF1ZXN0aW9uOiAnSG93IGRvIEkgcmVzZXQgbXkgcGFzc3dvcmQ/JyxcbiAgICBhbnN3ZXI6IGBQYXNzd29yZCByZXNldCBwcm9jZXNzOlxuXG5TdGVwLWJ5LVN0ZXAgUHJvY2VzczpcbjEuIEdvIHRvIHRoZSBsb2dpbiBwYWdlXG4yLiBDbGljayBcIkZvcmdvdCBQYXNzd29yZFwiIGxpbmtcbjMuIEVudGVyIHlvdXIgcmVnaXN0ZXJlZCBlbWFpbCBhZGRyZXNzXG40LiBDaGVjayB5b3VyIGVtYWlsIGZvciBPVFAgdmVyaWZpY2F0aW9uIGNvZGVcbjUuIEVudGVyIHRoZSBPVFAgY29kZSB3aGVuIHByb21wdGVkXG42LiBDcmVhdGUgYSBuZXcgc3Ryb25nIHBhc3N3b3JkXG43LiBDb25maXJtIHlvdXIgbmV3IHBhc3N3b3JkXG44LiBZb3UnbGwgYmUgYXV0b21hdGljYWxseSByZWRpcmVjdGVkIHRvIGRhc2hib2FyZFxuXG5QYXNzd29yZCBSZXF1aXJlbWVudHM6XG7igKIgTWluaW11bSA4IGNoYXJhY3RlcnNcbuKAoiBJbmNsdWRlIHVwcGVyY2FzZSBhbmQgbG93ZXJjYXNlIGxldHRlcnNcbuKAoiBJbmNsdWRlIGF0IGxlYXN0IG9uZSBudW1iZXJcbuKAoiBJbmNsdWRlIGF0IGxlYXN0IG9uZSBzcGVjaWFsIGNoYXJhY3Rlclxu4oCiIEF2b2lkIGNvbW1vbiBwYXNzd29yZHNcblxuU2VjdXJpdHkgVGlwczpcbuKAoiBVc2UgYSB1bmlxdWUgcGFzc3dvcmQgZm9yIEhhc2hDb3JlWFxu4oCiIENvbnNpZGVyIHVzaW5nIGEgcGFzc3dvcmQgbWFuYWdlclxu4oCiIERvbid0IHNoYXJlIHlvdXIgcGFzc3dvcmQgd2l0aCBhbnlvbmVcbuKAoiBDaGFuZ2UgeW91ciBwYXNzd29yZCByZWd1bGFybHlcbuKAoiBMb2cgb3V0IGZyb20gc2hhcmVkIGRldmljZXNcblxuVHJvdWJsZXNob290aW5nOlxu4oCiIENoZWNrIHNwYW0gZm9sZGVyIGZvciBPVFAgZW1haWxcbuKAoiBFbnN1cmUgZW1haWwgYWRkcmVzcyBpcyBjb3JyZWN0XG7igKIgV2FpdCBhIGZldyBtaW51dGVzIGZvciBlbWFpbCBkZWxpdmVyeVxu4oCiIENvbnRhY3Qgc3VwcG9ydCBpZiBpc3N1ZXMgcGVyc2lzdGAsXG4gICAgaWNvbjogU2hpZWxkLFxuICAgIHRhZ3M6IFsncGFzc3dvcmQnLCAncmVzZXQnLCAnc2VjdXJpdHknLCAnb3RwJywgJ2xvZ2luJ11cbiAgfSxcbiAge1xuICAgIGlkOiAnMTEnLFxuICAgIGNhdGVnb3J5OiAnR2V0dGluZyBTdGFydGVkJyxcbiAgICBxdWVzdGlvbjogJ1doYXQgYXJlIHRoZSBtaW5pbXVtIHJlcXVpcmVtZW50cyB0byBzdGFydD8nLFxuICAgIGFuc3dlcjogYE1pbmltdW0gcmVxdWlyZW1lbnRzIHRvIHN0YXJ0IG1pbmluZyB3aXRoIEhhc2hDb3JlWDpcblxuRmluYW5jaWFsIFJlcXVpcmVtZW50czpcbuKAoiBNaW5pbXVtIGRlcG9zaXQ6ICQ1MCBVU0RUIChUUkMyMClcbuKAoiBNaW5pbXVtIG1pbmluZyB1bml0IHB1cmNoYXNlOiBBcyBjb25maWd1cmVkIGJ5IGFkbWluXG7igKIgVHJhbnNhY3Rpb24gZmVlczogTmV0d29yayBmZWVzIGZvciBkZXBvc2l0cy93aXRoZHJhd2Fsc1xuXG5BY2NvdW50IFJlcXVpcmVtZW50czpcbuKAoiBWYWxpZCBlbWFpbCBhZGRyZXNzIGZvciByZWdpc3RyYXRpb25cbuKAoiBDb21wbGV0ZSBLWUMgdmVyaWZpY2F0aW9uIChtYW5kYXRvcnkpXG7igKIgVFJDMjAgd2FsbGV0IGZvciBkZXBvc2l0cyBhbmQgd2l0aGRyYXdhbHNcbuKAoiBBY3RpdmUgc3RhdHVzIChtYWludGFpbmVkIGJ5IGhhdmluZyBtaW5pbmcgdW5pdHMpXG5cblRlY2huaWNhbCBSZXF1aXJlbWVudHM6XG7igKIgTW9kZXJuIHdlYiBicm93c2VyIChDaHJvbWUsIEZpcmVmb3gsIFNhZmFyaSwgRWRnZSlcbuKAoiBTdGFibGUgaW50ZXJuZXQgY29ubmVjdGlvblxu4oCiIEVtYWlsIGFjY2VzcyBmb3IgdmVyaWZpY2F0aW9uIGFuZCBub3RpZmljYXRpb25zXG7igKIgQmFzaWMgdW5kZXJzdGFuZGluZyBvZiBjcnlwdG9jdXJyZW5jeSB0cmFuc2FjdGlvbnNcblxuR2V0dGluZyBTdGFydGVkIENoZWNrbGlzdDpcbjEuIOKckyBSZWdpc3RlciB3aXRoIHZhbGlkIGVtYWlsXG4yLiDinJMgVmVyaWZ5IGVtYWlsIGFkZHJlc3NcbjMuIOKckyBDb21wbGV0ZSBLWUMgdmVyaWZpY2F0aW9uXG40LiDinJMgU2V0IHVwIFRSQzIwIHdhbGxldFxuNS4g4pyTIE1ha2UgZmlyc3QgZGVwb3NpdCAoJDUwKyBVU0RUKVxuNi4g4pyTIFB1cmNoYXNlIGZpcnN0IG1pbmluZyB1bml0c1xuNy4g4pyTIFN0YXJ0IGVhcm5pbmcgZGFpbHkgcmV0dXJuc1xuXG5UaW1lIEludmVzdG1lbnQ6XG7igKIgSW5pdGlhbCBzZXR1cDogMzAtNjAgbWludXRlc1xu4oCiIERhaWx5IG1vbml0b3Jpbmc6IDUtMTAgbWludXRlc1xu4oCiIFdlZWtseSByZXZpZXc6IDE1LTMwIG1pbnV0ZXNgLFxuICAgIGljb246IEJvb2tPcGVuLFxuICAgIHRhZ3M6IFsncmVxdWlyZW1lbnRzJywgJ21pbmltdW0nLCAnZ2V0dGluZyBzdGFydGVkJywgJ2NoZWNrbGlzdCcsICdzZXR1cCddXG4gIH0sXG4gIHtcbiAgICBpZDogJzEyJyxcbiAgICBjYXRlZ29yeTogJ01pbmluZyBVbml0cycsXG4gICAgcXVlc3Rpb246ICdIb3cgZG8gSSB0cmFjayBteSBtaW5pbmcgcGVyZm9ybWFuY2U/JyxcbiAgICBhbnN3ZXI6IGBUcmFja2luZyB5b3VyIG1pbmluZyBwZXJmb3JtYW5jZSBlZmZlY3RpdmVseTpcblxuRGFzaGJvYXJkIE92ZXJ2aWV3Olxu4oCiIFRvdGFsIGFjdGl2ZSBUSC9zIGFtb3VudFxu4oCiIERhaWx5IGVhcm5pbmdzIHN1bW1hcnlcbuKAoiBQZW5kaW5nIHZzIGF2YWlsYWJsZSBiYWxhbmNlXG7igKIgTWluaW5nIHVuaXRzIHN0YXR1cyBvdmVydmlld1xuXG5EZXRhaWxlZCBUcmFja2luZzpcbuKAoiBJbmRpdmlkdWFsIHVuaXQgcGVyZm9ybWFuY2VcbuKAoiBQcm9ncmVzcyB0b3dhcmQgNXggcmV0dXJuIGxpbWl0XG7igKIgRGFpbHkgUk9JIHBlcmNlbnRhZ2VzICg3LWRheSBhdmVyYWdlKVxu4oCiIEVhcm5pbmdzIGFsbG9jYXRpb24gcGVyIHVuaXRcblxuS2V5IE1ldHJpY3MgdG8gTW9uaXRvcjpcbuKAoiBUb3RhbCBpbnZlc3RtZW50IGFtb3VudFxu4oCiIFRvdGFsIGVhcm5pbmdzIHRvIGRhdGVcbuKAoiBBdmVyYWdlIGRhaWx5IHJldHVybiBwZXJjZW50YWdlXG7igKIgVGltZSByZW1haW5pbmcgdW50aWwgZXhwaXJ5XG7igKIgRklGTyBhbGxvY2F0aW9uIHByb2dyZXNzXG5cblBlcmZvcm1hbmNlIEFuYWx5c2lzOlxu4oCiIENvbXBhcmUgYWN0dWFsIHZzIGV4cGVjdGVkIHJldHVybnNcbuKAoiBNb25pdG9yIG1hcmtldCBjb25kaXRpb24gaW1wYWN0c1xu4oCiIFRyYWNrIHdlZWtseSBwYXlvdXQgc2NoZWR1bGVzXG7igKIgUmV2aWV3IGJpbmFyeSBlYXJuaW5ncyBhbGxvY2F0aW9uXG5cbk9wdGltaXphdGlvbiBUaXBzOlxu4oCiIFJlaW52ZXN0IGVhcm5pbmdzIGZvciBjb21wb3VuZCBncm93dGhcbuKAoiBNb25pdG9yIHVuaXQgZXhwaXJ5IGRhdGVzXG7igKIgQmFsYW5jZSBpbnZlc3RtZW50IGFjcm9zcyBkaWZmZXJlbnQgVEgvcyByYW5nZXNcbuKAoiBLZWVwIHRyYWNrIG9mIHJlZmVycmFsIGVhcm5pbmdzIGNvbnRyaWJ1dGlvblxuXG5SZXBvcnRzIEF2YWlsYWJsZTpcbuKAoiBUcmFuc2FjdGlvbiBoaXN0b3J5XG7igKIgRWFybmluZ3MgYnJlYWtkb3duXG7igKIgTWluaW5nIHVuaXQgZGV0YWlsc1xu4oCiIFJlZmVycmFsIHBlcmZvcm1hbmNlIHN1bW1hcnlgLFxuICAgIGljb246IFRyZW5kaW5nVXAsXG4gICAgdGFnczogWyd0cmFja2luZycsICdwZXJmb3JtYW5jZScsICdtb25pdG9yaW5nJywgJ2FuYWx5dGljcycsICdvcHRpbWl6YXRpb24nXVxuICB9XG5dO1xuXG5leHBvcnQgY29uc3QgU3VwcG9ydENlbnRlcjogUmVhY3QuRkMgPSAoKSA9PiB7XG4gIGNvbnN0IFt0aWNrZXRzLCBzZXRUaWNrZXRzXSA9IHVzZVN0YXRlPFN1cHBvcnRUaWNrZXRbXT4oW10pO1xuICBjb25zdCBbbG9hZGluZywgc2V0TG9hZGluZ10gPSB1c2VTdGF0ZSh0cnVlKTtcbiAgY29uc3QgW3Nob3dOZXdUaWNrZXRNb2RhbCwgc2V0U2hvd05ld1RpY2tldE1vZGFsXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW3NlbGVjdGVkVGlja2V0LCBzZXRTZWxlY3RlZFRpY2tldF0gPSB1c2VTdGF0ZTxTdXBwb3J0VGlja2V0IHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IFtzZWxlY3RlZEZBUSwgc2V0U2VsZWN0ZWRGQVFdID0gdXNlU3RhdGU8RkFRSXRlbSB8IG51bGw+KG51bGwpO1xuICBjb25zdCBbZmFxU2VhcmNoVGVybSwgc2V0RmFxU2VhcmNoVGVybV0gPSB1c2VTdGF0ZSgnJyk7XG4gIGNvbnN0IFtzZWxlY3RlZENhdGVnb3J5LCBzZXRTZWxlY3RlZENhdGVnb3J5XSA9IHVzZVN0YXRlPHN0cmluZz4oJ0FsbCcpO1xuICBjb25zdCBbY3VycmVudFBhZ2UsIHNldEN1cnJlbnRQYWdlXSA9IHVzZVN0YXRlKDEpO1xuICBjb25zdCBpdGVtc1BlclBhZ2UgPSA2O1xuICBjb25zdCBbbmV3VGlja2V0Rm9ybSwgc2V0TmV3VGlja2V0Rm9ybV0gPSB1c2VTdGF0ZSh7XG4gICAgc3ViamVjdDogJycsXG4gICAgbWVzc2FnZTogJycsXG4gICAgcHJpb3JpdHk6ICdNRURJVU0nIGFzIGNvbnN0LFxuICB9KTtcbiAgY29uc3QgW25ld1Jlc3BvbnNlLCBzZXROZXdSZXNwb25zZV0gPSB1c2VTdGF0ZSgnJyk7XG4gIGNvbnN0IFtzdWJtaXR0aW5nLCBzZXRTdWJtaXR0aW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGZldGNoVGlja2V0cygpO1xuICB9LCBbXSk7XG5cbiAgLy8gR2V0IHVuaXF1ZSBjYXRlZ29yaWVzIGZvciBmaWx0ZXJpbmdcbiAgY29uc3QgY2F0ZWdvcmllcyA9IFsnQWxsJywgLi4uQXJyYXkuZnJvbShuZXcgU2V0KGZhcURhdGEubWFwKGZhcSA9PiBmYXEuY2F0ZWdvcnkpKSldO1xuXG4gIC8vIEZpbHRlciBGQVFzIGJhc2VkIG9uIHNlYXJjaCB0ZXJtIGFuZCBjYXRlZ29yeVxuICBjb25zdCBmaWx0ZXJlZEZBUXMgPSBmYXFEYXRhLmZpbHRlcihmYXEgPT4ge1xuICAgIGNvbnN0IG1hdGNoZXNTZWFyY2ggPSBmYXEucXVlc3Rpb24udG9Mb3dlckNhc2UoKS5pbmNsdWRlcyhmYXFTZWFyY2hUZXJtLnRvTG93ZXJDYXNlKCkpIHx8XG4gICAgICAgICAgICAgICAgICAgICAgICAgZmFxLmFuc3dlci50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKGZhcVNlYXJjaFRlcm0udG9Mb3dlckNhc2UoKSkgfHxcbiAgICAgICAgICAgICAgICAgICAgICAgICBmYXEudGFncy5zb21lKHRhZyA9PiB0YWcudG9Mb3dlckNhc2UoKS5pbmNsdWRlcyhmYXFTZWFyY2hUZXJtLnRvTG93ZXJDYXNlKCkpKTtcbiAgICBjb25zdCBtYXRjaGVzQ2F0ZWdvcnkgPSBzZWxlY3RlZENhdGVnb3J5ID09PSAnQWxsJyB8fCBmYXEuY2F0ZWdvcnkgPT09IHNlbGVjdGVkQ2F0ZWdvcnk7XG4gICAgcmV0dXJuIG1hdGNoZXNTZWFyY2ggJiYgbWF0Y2hlc0NhdGVnb3J5O1xuICB9KTtcblxuICAvLyBDYWxjdWxhdGUgcGFnaW5hdGlvblxuICBjb25zdCB0b3RhbFBhZ2VzID0gTWF0aC5jZWlsKGZpbHRlcmVkRkFRcy5sZW5ndGggLyBpdGVtc1BlclBhZ2UpO1xuICBjb25zdCBzdGFydEluZGV4ID0gKGN1cnJlbnRQYWdlIC0gMSkgKiBpdGVtc1BlclBhZ2U7XG4gIGNvbnN0IGVuZEluZGV4ID0gc3RhcnRJbmRleCArIGl0ZW1zUGVyUGFnZTtcbiAgY29uc3QgY3VycmVudEZBUXMgPSBmaWx0ZXJlZEZBUXMuc2xpY2Uoc3RhcnRJbmRleCwgZW5kSW5kZXgpO1xuXG4gIC8vIFJlc2V0IHRvIGZpcnN0IHBhZ2Ugd2hlbiBmaWx0ZXJzIGNoYW5nZVxuICBSZWFjdC51c2VFZmZlY3QoKCkgPT4ge1xuICAgIHNldEN1cnJlbnRQYWdlKDEpO1xuICB9LCBbZmFxU2VhcmNoVGVybSwgc2VsZWN0ZWRDYXRlZ29yeV0pO1xuXG4gIGNvbnN0IGZldGNoVGlja2V0cyA9IGFzeW5jICgpID0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCgnL2FwaS9zdXBwb3J0L3RpY2tldHMnLCB7XG4gICAgICAgIGNyZWRlbnRpYWxzOiAnaW5jbHVkZScsXG4gICAgICB9KTtcblxuICAgICAgaWYgKHJlc3BvbnNlLm9rKSB7XG4gICAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gICAgICAgIGlmIChkYXRhLnN1Y2Nlc3MpIHtcbiAgICAgICAgICBzZXRUaWNrZXRzKGRhdGEuZGF0YSk7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIGZldGNoIHRpY2tldHM6JywgZXJyb3IpO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRMb2FkaW5nKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cblxuXG4gIGNvbnN0IGNyZWF0ZVRpY2tldCA9IGFzeW5jIChlOiBSZWFjdC5Gb3JtRXZlbnQpID0+IHtcbiAgICBlLnByZXZlbnREZWZhdWx0KCk7XG4gICAgc2V0U3VibWl0dGluZyh0cnVlKTtcblxuICAgIHRyeSB7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpL3N1cHBvcnQvdGlja2V0cycsIHtcbiAgICAgICAgbWV0aG9kOiAnUE9TVCcsXG4gICAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9LFxuICAgICAgICBjcmVkZW50aWFsczogJ2luY2x1ZGUnLFxuICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeShuZXdUaWNrZXRGb3JtKSxcbiAgICAgIH0pO1xuXG4gICAgICBpZiAocmVzcG9uc2Uub2spIHtcbiAgICAgICAgc2V0TmV3VGlja2V0Rm9ybSh7IHN1YmplY3Q6ICcnLCBtZXNzYWdlOiAnJywgcHJpb3JpdHk6ICdNRURJVU0nIH0pO1xuICAgICAgICBzZXRTaG93TmV3VGlja2V0TW9kYWwoZmFsc2UpO1xuICAgICAgICBmZXRjaFRpY2tldHMoKTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIGNyZWF0ZSB0aWNrZXQ6JywgZXJyb3IpO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRTdWJtaXR0aW5nKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgYWRkUmVzcG9uc2UgPSBhc3luYyAodGlja2V0SWQ6IHN0cmluZykgPT4ge1xuICAgIGlmICghbmV3UmVzcG9uc2UudHJpbSgpKSByZXR1cm47XG5cbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgL2FwaS9zdXBwb3J0L3RpY2tldHMvJHt0aWNrZXRJZH0vcmVzcG9uc2VzYCwge1xuICAgICAgICBtZXRob2Q6ICdQT1NUJyxcbiAgICAgICAgaGVhZGVyczoge1xuICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgIH0sXG4gICAgICAgIGNyZWRlbnRpYWxzOiAnaW5jbHVkZScsXG4gICAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHsgbWVzc2FnZTogbmV3UmVzcG9uc2UgfSksXG4gICAgICB9KTtcblxuICAgICAgaWYgKHJlc3BvbnNlLm9rKSB7XG4gICAgICAgIHNldE5ld1Jlc3BvbnNlKCcnKTtcbiAgICAgICAgZmV0Y2hUaWNrZXRzKCk7XG4gICAgICAgIC8vIFVwZGF0ZSBzZWxlY3RlZCB0aWNrZXRcbiAgICAgICAgY29uc3QgdXBkYXRlZFRpY2tldHMgPSBhd2FpdCBmZXRjaCgnL2FwaS9zdXBwb3J0L3RpY2tldHMnLCB7XG4gICAgICAgICAgY3JlZGVudGlhbHM6ICdpbmNsdWRlJyxcbiAgICAgICAgfSk7XG4gICAgICAgIGlmICh1cGRhdGVkVGlja2V0cy5vaykge1xuICAgICAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCB1cGRhdGVkVGlja2V0cy5qc29uKCk7XG4gICAgICAgICAgY29uc3QgdXBkYXRlZFRpY2tldCA9IGRhdGEuZGF0YS5maW5kKCh0OiBTdXBwb3J0VGlja2V0KSA9PiB0LmlkID09PSB0aWNrZXRJZCk7XG4gICAgICAgICAgaWYgKHVwZGF0ZWRUaWNrZXQpIHtcbiAgICAgICAgICAgIHNldFNlbGVjdGVkVGlja2V0KHVwZGF0ZWRUaWNrZXQpO1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gYWRkIHJlc3BvbnNlOicsIGVycm9yKTtcbiAgICB9XG4gIH07XG5cblxuXG4gIGNvbnN0IGdldFN0YXR1c0ljb24gPSAoc3RhdHVzOiBzdHJpbmcpID0+IHtcbiAgICBzd2l0Y2ggKHN0YXR1cykge1xuICAgICAgY2FzZSAnT1BFTic6XG4gICAgICAgIHJldHVybiA8QWxlcnRDaXJjbGUgY2xhc3NOYW1lPVwiaC00IHctNCB0ZXh0LXJlZC01MDBcIiAvPjtcbiAgICAgIGNhc2UgJ0lOX1BST0dSRVNTJzpcbiAgICAgICAgcmV0dXJuIDxDbG9jayBjbGFzc05hbWU9XCJoLTQgdy00IHRleHQtc29sYXItNTAwXCIgLz47XG4gICAgICBjYXNlICdSRVNPTFZFRCc6XG4gICAgICBjYXNlICdDTE9TRUQnOlxuICAgICAgICByZXR1cm4gPENoZWNrQ2lyY2xlIGNsYXNzTmFtZT1cImgtNCB3LTQgdGV4dC1lY28tNTAwXCIgLz47XG4gICAgICBkZWZhdWx0OlxuICAgICAgICByZXR1cm4gPE1lc3NhZ2VDaXJjbGUgY2xhc3NOYW1lPVwiaC00IHctNCB0ZXh0LWdyYXktNTAwXCIgLz47XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGdldFN0YXR1c0NvbG9yID0gKHN0YXR1czogc3RyaW5nKSA9PiB7XG4gICAgc3dpdGNoIChzdGF0dXMpIHtcbiAgICAgIGNhc2UgJ09QRU4nOlxuICAgICAgICByZXR1cm4gJ2JnLXJlZC0xMDAgdGV4dC1yZWQtNzAwJztcbiAgICAgIGNhc2UgJ0lOX1BST0dSRVNTJzpcbiAgICAgICAgcmV0dXJuICdiZy1zb2xhci0xMDAgdGV4dC1zb2xhci03MDAnO1xuICAgICAgY2FzZSAnUkVTT0xWRUQnOlxuICAgICAgY2FzZSAnQ0xPU0VEJzpcbiAgICAgICAgcmV0dXJuICdiZy1lY28tMTAwIHRleHQtZWNvLTcwMCc7XG4gICAgICBkZWZhdWx0OlxuICAgICAgICByZXR1cm4gJ2JnLWdyYXktMTAwIHRleHQtZ3JheS03MDAnO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBnZXRQcmlvcml0eUNvbG9yID0gKHByaW9yaXR5OiBzdHJpbmcpID0+IHtcbiAgICBzd2l0Y2ggKHByaW9yaXR5KSB7XG4gICAgICBjYXNlICdVUkdFTlQnOlxuICAgICAgICByZXR1cm4gJ2JnLXJlZC0xMDAgdGV4dC1yZWQtNzAwJztcbiAgICAgIGNhc2UgJ0hJR0gnOlxuICAgICAgICByZXR1cm4gJ2JnLW9yYW5nZS0xMDAgdGV4dC1vcmFuZ2UtNzAwJztcbiAgICAgIGNhc2UgJ01FRElVTSc6XG4gICAgICAgIHJldHVybiAnYmctc29sYXItMTAwIHRleHQtc29sYXItNzAwJztcbiAgICAgIGNhc2UgJ0xPVyc6XG4gICAgICAgIHJldHVybiAnYmctZ3JheS0xMDAgdGV4dC1ncmF5LTcwMCc7XG4gICAgICBkZWZhdWx0OlxuICAgICAgICByZXR1cm4gJ2JnLWdyYXktMTAwIHRleHQtZ3JheS03MDAnO1xuICAgIH1cbiAgfTtcblxuICBpZiAobG9hZGluZykge1xuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNlwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFuaW1hdGUtcHVsc2VcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtOCBiZy1ncmF5LTIwMCByb3VuZGVkIHctMS80IG1iLTRcIj48L2Rpdj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtNjQgYmctZ3JheS0yMDAgcm91bmRlZFwiPjwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgICk7XG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS04XCI+XG4gICAgICB7LyogSGVhZGVyICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgPGRpdj5cbiAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtZ3JheS05MDBcIj5TdXBwb3J0IENlbnRlcjwvaDI+XG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMCBtdC0xXCI+R2V0IGhlbHAgYW5kIG1hbmFnZSBzdXBwb3J0IHRpY2tldHM8L3A+XG4gICAgICAgIDwvZGl2PlxuICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2hvd05ld1RpY2tldE1vZGFsKHRydWUpfVxuICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCJcbiAgICAgICAgPlxuICAgICAgICAgIDxQbHVzIGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxuICAgICAgICAgIE5ldyBUaWNrZXRcbiAgICAgICAgPC9CdXR0b24+XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIFN1cHBvcnQgVGlja2V0cyAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XG4gICAgICAgICAgPENhcmQ+XG4gICAgICAgICAgICA8Q2FyZEhlYWRlcj5cbiAgICAgICAgICAgICAgPENhcmRUaXRsZSBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgICAgIDxNZXNzYWdlQ2lyY2xlIGNsYXNzTmFtZT1cImgtNSB3LTVcIiAvPlxuICAgICAgICAgICAgICAgIFN1cHBvcnQgVGlja2V0c1xuICAgICAgICAgICAgICA8L0NhcmRUaXRsZT5cbiAgICAgICAgICAgIDwvQ2FyZEhlYWRlcj5cbiAgICAgICAgICAgIDxDYXJkQ29udGVudD5cbiAgICAgICAgICAgICAge3RpY2tldHMubGVuZ3RoID4gMCA/IChcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktM1wiPlxuICAgICAgICAgICAgICAgICAge3RpY2tldHMubWFwKCh0aWNrZXQpID0+IChcbiAgICAgICAgICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICAgICAgICAgIGtleT17dGlja2V0LmlkfVxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNlbGVjdGVkVGlja2V0KHRpY2tldCl9XG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicC00IGJvcmRlciBib3JkZXItZ3JheS0yMDAgcm91bmRlZC1sZyBjdXJzb3ItcG9pbnRlclwiXG4gICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtc3RhcnQganVzdGlmeS1iZXR3ZWVuIG1iLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwIHRydW5jYXRlIGZsZXgtMVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7dGlja2V0LnN1YmplY3R9XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2g0PlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMiBtbC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtnZXRTdGF0dXNJY29uKHRpY2tldC5zdGF0dXMpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9e2B0ZXh0LXhzIHB4LTIgcHktMSByb3VuZGVkLWZ1bGwgJHtnZXRTdGF0dXNDb2xvcih0aWNrZXQuc3RhdHVzKX1gfT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7dGlja2V0LnN0YXR1c31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwIG1iLTIgbGluZS1jbGFtcC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICB7dGlja2V0Lm1lc3NhZ2V9XG4gICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIHRleHQteHMgdGV4dC1ncmF5LTUwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPXtgcHgtMiBweS0xIHJvdW5kZWQtZnVsbCAke2dldFByaW9yaXR5Q29sb3IodGlja2V0LnByaW9yaXR5KX1gfT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAge3RpY2tldC5wcmlvcml0eX1cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPntmb3JtYXREYXRlVGltZSh0aWNrZXQuY3JlYXRlZEF0KX08L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBweS04XCI+XG4gICAgICAgICAgICAgICAgICA8TWVzc2FnZUNpcmNsZSBjbGFzc05hbWU9XCJoLTEyIHctMTIgdGV4dC1ncmF5LTQwMCBteC1hdXRvIG1iLTRcIiAvPlxuICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMCBtYi0yXCI+Tm8gU3VwcG9ydCBUaWNrZXRzPC9oMz5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDBcIj5Zb3UgaGF2ZW4mYXBvczt0IGNyZWF0ZWQgYW55IHN1cHBvcnQgdGlja2V0cyB5ZXQuPC9wPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgICAgICA8L0NhcmQ+XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIEhlbHAgVHV0b3JpYWxzICYgRkFRIFNlY3Rpb24gKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNlwiPlxuICAgICAgICA8Q2FyZD5cbiAgICAgICAgICA8Q2FyZEhlYWRlcj5cbiAgICAgICAgICAgIDxDYXJkVGl0bGUgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cbiAgICAgICAgICAgICAgPEhlbHBDaXJjbGUgY2xhc3NOYW1lPVwiaC01IHctNVwiIC8+XG4gICAgICAgICAgICAgIEhlbHAgVHV0b3JpYWxzICYgRkFRXG4gICAgICAgICAgICA8L0NhcmRUaXRsZT5cbiAgICAgICAgICA8L0NhcmRIZWFkZXI+XG4gICAgICAgICAgPENhcmRDb250ZW50PlxuICAgICAgICAgICAgey8qIFNlYXJjaCBhbmQgRmlsdGVyICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi02IHNwYWNlLXktNFwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgc206ZmxleC1yb3cgZ2FwLTRcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMVwiPlxuICAgICAgICAgICAgICAgICAgPElucHV0XG4gICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiU2VhcmNoIEZBUXMuLi5cIlxuICAgICAgICAgICAgICAgICAgICB2YWx1ZT17ZmFxU2VhcmNoVGVybX1cbiAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRGYXFTZWFyY2hUZXJtKGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsXCJcbiAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzbTp3LTQ4XCI+XG4gICAgICAgICAgICAgICAgICA8c2VsZWN0XG4gICAgICAgICAgICAgICAgICAgIHZhbHVlPXtzZWxlY3RlZENhdGVnb3J5fVxuICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldFNlbGVjdGVkQ2F0ZWdvcnkoZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgcHgtMyBweS0yIGJvcmRlciBib3JkZXItZ3JheS0zMDAgcm91bmRlZC1sZyBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1zb2xhci01MDAgZm9jdXM6Ym9yZGVyLXRyYW5zcGFyZW50XCJcbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAge2NhdGVnb3JpZXMubWFwKGNhdGVnb3J5ID0+IChcbiAgICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIGtleT17Y2F0ZWdvcnl9IHZhbHVlPXtjYXRlZ29yeX0+e2NhdGVnb3J5fTwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICAgIDwvc2VsZWN0PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICB7LyogRkFRIENhcmRzIEdyaWQgKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgbGc6Z3JpZC1jb2xzLTMgZ2FwLTQgbWItNlwiPlxuICAgICAgICAgICAgICB7Y3VycmVudEZBUXMubWFwKChmYXEpID0+IHtcbiAgICAgICAgICAgICAgICBjb25zdCBJY29uQ29tcG9uZW50ID0gZmFxLmljb247XG4gICAgICAgICAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgICAgICAga2V5PXtmYXEuaWR9XG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNlbGVjdGVkRkFRKGZhcSl9XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInAtNiBib3JkZXIgYm9yZGVyLWdyYXktMjAwIHJvdW5kZWQtbGcgY3Vyc29yLXBvaW50ZXIgaG92ZXI6Ym9yZGVyLXNvbGFyLTMwMCBob3ZlcjpzaGFkb3ctbWQgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwIGJnLXdoaXRlXCJcbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLXN0YXJ0IGdhcC0zIG1iLTNcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtMiBiZy1zb2xhci0xMDAgcm91bmRlZC1sZ1wiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPEljb25Db21wb25lbnQgY2xhc3NOYW1lPVwiaC01IHctNSB0ZXh0LXNvbGFyLTYwMFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQteHMgZm9udC1tZWRpdW0gdGV4dC1zb2xhci02MDAgYmctc29sYXItNTAgcHgtMiBweS0xIHJvdW5kZWQtZnVsbFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7ZmFxLmNhdGVnb3J5fVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cImZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMCBtYi0yIGxpbmUtY2xhbXAtMlwiPlxuICAgICAgICAgICAgICAgICAgICAgIHtmYXEucXVlc3Rpb259XG4gICAgICAgICAgICAgICAgICAgIDwvaDQ+XG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMCBsaW5lLWNsYW1wLTMgbWItM1wiPlxuICAgICAgICAgICAgICAgICAgICAgIHtmYXEuYW5zd2VyLnNwbGl0KCdcXG4nKVswXX1cbiAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC13cmFwIGdhcC0xXCI+XG4gICAgICAgICAgICAgICAgICAgICAge2ZhcS50YWdzLnNsaWNlKDAsIDMpLm1hcCh0YWcgPT4gKFxuICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4ga2V5PXt0YWd9IGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTUwMCBiZy1ncmF5LTEwMCBweC0yIHB5LTEgcm91bmRlZFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7dGFnfVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgICAgICAgIHtmYXEudGFncy5sZW5ndGggPiAzICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTUwMFwiPit7ZmFxLnRhZ3MubGVuZ3RoIC0gM30gbW9yZTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICk7XG4gICAgICAgICAgICAgIH0pfVxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIHtmaWx0ZXJlZEZBUXMubGVuZ3RoID09PSAwICYmIChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBweS0xMiBjb2wtc3Bhbi1mdWxsXCI+XG4gICAgICAgICAgICAgICAgPEJvb2tPcGVuIGNsYXNzTmFtZT1cImgtMTIgdy0xMiB0ZXh0LWdyYXktNDAwIG14LWF1dG8gbWItNFwiIC8+XG4gICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMCBtYi0yXCI+Tm8gRkFRcyBGb3VuZDwvaDM+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMFwiPlxuICAgICAgICAgICAgICAgICAge2ZhcVNlYXJjaFRlcm0gfHwgc2VsZWN0ZWRDYXRlZ29yeSAhPT0gJ0FsbCdcbiAgICAgICAgICAgICAgICAgICAgPyAnVHJ5IGFkanVzdGluZyB5b3VyIHNlYXJjaCBvciBmaWx0ZXIgY3JpdGVyaWEuJ1xuICAgICAgICAgICAgICAgICAgICA6ICdGQVEgY29udGVudCBpcyBiZWluZyB1cGRhdGVkLid9XG4gICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgIHsvKiBQYWdpbmF0aW9uIENvbnRyb2xzICovfVxuICAgICAgICAgICAge2ZpbHRlcmVkRkFRcy5sZW5ndGggPiBpdGVtc1BlclBhZ2UgJiYgKFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbC1zcGFuLWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIG10LTYgcHQtNiBib3JkZXItdCBib3JkZXItZ3JheS0yMDBcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMFwiPlxuICAgICAgICAgICAgICAgICAgU2hvd2luZyB7c3RhcnRJbmRleCArIDF9IHRvIHtNYXRoLm1pbihlbmRJbmRleCwgZmlsdGVyZWRGQVFzLmxlbmd0aCl9IG9mIHtmaWx0ZXJlZEZBUXMubGVuZ3RofSBhcnRpY2xlc1xuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldEN1cnJlbnRQYWdlKHByZXYgPT4gTWF0aC5tYXgocHJldiAtIDEsIDEpKX1cbiAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2N1cnJlbnRQYWdlID09PSAxfVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJweC0zIHB5LTIgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNTAwIGJnLXdoaXRlIGJvcmRlciBib3JkZXItZ3JheS0zMDAgcm91bmRlZC1tZCBob3ZlcjpiZy1ncmF5LTUwIGRpc2FibGVkOm9wYWNpdHktNTAgZGlzYWJsZWQ6Y3Vyc29yLW5vdC1hbGxvd2VkXCJcbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgUHJldmlvdXNcbiAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuXG4gICAgICAgICAgICAgICAgICB7LyogUGFnZSBOdW1iZXJzICovfVxuICAgICAgICAgICAgICAgICAge0FycmF5LmZyb20oeyBsZW5ndGg6IHRvdGFsUGFnZXMgfSwgKF8sIGkpID0+IGkgKyAxKS5tYXAocGFnZSA9PiAoXG4gICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICBrZXk9e3BhZ2V9XG4gICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0Q3VycmVudFBhZ2UocGFnZSl9XG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgcHgtMyBweS0yIHRleHQtc20gZm9udC1tZWRpdW0gcm91bmRlZC1tZCAke1xuICAgICAgICAgICAgICAgICAgICAgICAgY3VycmVudFBhZ2UgPT09IHBhZ2VcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPyAnYmctc29sYXItNjAwIHRleHQtd2hpdGUnXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDogJ3RleHQtZ3JheS01MDAgYmctd2hpdGUgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCBob3ZlcjpiZy1ncmF5LTUwJ1xuICAgICAgICAgICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAge3BhZ2V9XG4gICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgKSl9XG5cbiAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0Q3VycmVudFBhZ2UocHJldiA9PiBNYXRoLm1pbihwcmV2ICsgMSwgdG90YWxQYWdlcykpfVxuICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZD17Y3VycmVudFBhZ2UgPT09IHRvdGFsUGFnZXN9XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTMgcHktMiB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS01MDAgYmctd2hpdGUgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkLW1kIGhvdmVyOmJnLWdyYXktNTAgZGlzYWJsZWQ6b3BhY2l0eS01MCBkaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWRcIlxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICBOZXh0XG4gICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApfVxuICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgIDwvQ2FyZD5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogTmV3IFRpY2tldCBNb2RhbCAqL31cbiAgICAgIDxNb2RhbFxuICAgICAgICBpc09wZW49e3Nob3dOZXdUaWNrZXRNb2RhbH1cbiAgICAgICAgb25DbG9zZT17KCkgPT4gc2V0U2hvd05ld1RpY2tldE1vZGFsKGZhbHNlKX1cbiAgICAgICAgdGl0bGU9XCJDcmVhdGUgU3VwcG9ydCBUaWNrZXRcIlxuICAgICAgPlxuICAgICAgICA8Zm9ybSBvblN1Ym1pdD17Y3JlYXRlVGlja2V0fSBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgIGxhYmVsPVwiU3ViamVjdFwiXG4gICAgICAgICAgICB2YWx1ZT17bmV3VGlja2V0Rm9ybS5zdWJqZWN0fVxuICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXROZXdUaWNrZXRGb3JtKHByZXYgPT4gKHsgLi4ucHJldiwgc3ViamVjdDogZS50YXJnZXQudmFsdWUgfSkpfVxuICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJCcmllZiBkZXNjcmlwdGlvbiBvZiB5b3VyIGlzc3VlXCJcbiAgICAgICAgICAgIHJlcXVpcmVkXG4gICAgICAgICAgLz5cblxuICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIG1iLTJcIj5cbiAgICAgICAgICAgICAgUHJpb3JpdHlcbiAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICA8c2VsZWN0XG4gICAgICAgICAgICAgIHZhbHVlPXtuZXdUaWNrZXRGb3JtLnByaW9yaXR5fVxuICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldE5ld1RpY2tldEZvcm0ocHJldiA9PiAoeyAuLi5wcmV2LCBwcmlvcml0eTogZS50YXJnZXQudmFsdWUgYXMgYW55IH0pKX1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHB4LTMgcHktMiBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbGcgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctc29sYXItNTAwIGZvY3VzOmJvcmRlci10cmFuc3BhcmVudFwiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJMT1dcIj5Mb3c8L29wdGlvbj5cbiAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIk1FRElVTVwiPk1lZGl1bTwvb3B0aW9uPlxuICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiSElHSFwiPkhpZ2g8L29wdGlvbj5cbiAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIlVSR0VOVFwiPlVyZ2VudDwvb3B0aW9uPlxuICAgICAgICAgICAgPC9zZWxlY3Q+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBtYi0yXCI+XG4gICAgICAgICAgICAgIE1lc3NhZ2VcbiAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICA8dGV4dGFyZWFcbiAgICAgICAgICAgICAgdmFsdWU9e25ld1RpY2tldEZvcm0ubWVzc2FnZX1cbiAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXROZXdUaWNrZXRGb3JtKHByZXYgPT4gKHsgLi4ucHJldiwgbWVzc2FnZTogZS50YXJnZXQudmFsdWUgfSkpfVxuICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkRlc2NyaWJlIHlvdXIgaXNzdWUgaW4gZGV0YWlsLi4uXCJcbiAgICAgICAgICAgICAgcm93cz17NH1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHB4LTMgcHktMiBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbGcgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctc29sYXItNTAwIGZvY3VzOmJvcmRlci10cmFuc3BhcmVudFwiXG4gICAgICAgICAgICAgIHJlcXVpcmVkXG4gICAgICAgICAgICAvPlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IHNwYWNlLXgtM1wiPlxuICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcbiAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxuICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTaG93TmV3VGlja2V0TW9kYWwoZmFsc2UpfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4LTFcIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICBDYW5jZWxcbiAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICB0eXBlPVwic3VibWl0XCJcbiAgICAgICAgICAgICAgbG9hZGluZz17c3VibWl0dGluZ31cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleC0xXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgQ3JlYXRlIFRpY2tldFxuICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZm9ybT5cbiAgICAgIDwvTW9kYWw+XG5cbiAgICAgIHsvKiBUaWNrZXQgRGV0YWlsIE1vZGFsICovfVxuICAgICAge3NlbGVjdGVkVGlja2V0ICYmIChcbiAgICAgICAgPE1vZGFsXG4gICAgICAgICAgaXNPcGVuPXshIXNlbGVjdGVkVGlja2V0fVxuICAgICAgICAgIG9uQ2xvc2U9eygpID0+IHNldFNlbGVjdGVkVGlja2V0KG51bGwpfVxuICAgICAgICAgIHRpdGxlPXtgVGlja2V0OiAke3NlbGVjdGVkVGlja2V0LnN1YmplY3R9YH1cbiAgICAgICAgICBzaXplPVwieGxcIlxuICAgICAgICA+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cbiAgICAgICAgICAgICAge2dldFN0YXR1c0ljb24oc2VsZWN0ZWRUaWNrZXQuc3RhdHVzKX1cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPXtgdGV4dC1zbSBweC0yIHB5LTEgcm91bmRlZC1mdWxsICR7Z2V0U3RhdHVzQ29sb3Ioc2VsZWN0ZWRUaWNrZXQuc3RhdHVzKX1gfT5cbiAgICAgICAgICAgICAgICB7c2VsZWN0ZWRUaWNrZXQuc3RhdHVzfVxuICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT17YHRleHQtc20gcHgtMiBweS0xIHJvdW5kZWQtZnVsbCAke2dldFByaW9yaXR5Q29sb3Ioc2VsZWN0ZWRUaWNrZXQucHJpb3JpdHkpfWB9PlxuICAgICAgICAgICAgICAgIHtzZWxlY3RlZFRpY2tldC5wcmlvcml0eX1cbiAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctZ3JheS01MCBwLTQgcm91bmRlZC1sZ1wiPlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDAgbWItMlwiPk9yaWdpbmFsIE1lc3NhZ2U6PC9wPlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktOTAwXCI+e3NlbGVjdGVkVGlja2V0Lm1lc3NhZ2V9PC9wPlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS01MDAgbXQtMlwiPlxuICAgICAgICAgICAgICAgIENyZWF0ZWQ6IHtmb3JtYXREYXRlVGltZShzZWxlY3RlZFRpY2tldC5jcmVhdGVkQXQpfVxuICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAge3NlbGVjdGVkVGlja2V0LnJlc3BvbnNlcy5sZW5ndGggPiAwICYmIChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTNcIj5cbiAgICAgICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMFwiPlJlc3BvbnNlczo8L2g0PlxuICAgICAgICAgICAgICAgIHtzZWxlY3RlZFRpY2tldC5yZXNwb25zZXMubWFwKChyZXNwb25zZSkgPT4gKFxuICAgICAgICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICAgICAgICBrZXk9e3Jlc3BvbnNlLmlkfVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BwLTMgcm91bmRlZC1sZyAke1xuICAgICAgICAgICAgICAgICAgICAgIHJlc3BvbnNlLmlzQWRtaW4gPyAnYmctYmx1ZS01MCBib3JkZXItbC00IGJvcmRlci1ibHVlLTUwMCcgOiAnYmctZ3JheS01MCdcbiAgICAgICAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS05MDBcIj57cmVzcG9uc2UubWVzc2FnZX08L3A+XG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTUwMCBtdC0xXCI+XG4gICAgICAgICAgICAgICAgICAgICAge3Jlc3BvbnNlLmlzQWRtaW4gPyAnU3VwcG9ydCBUZWFtJyA6ICdZb3UnfSDigKIge2Zvcm1hdERhdGVUaW1lKHJlc3BvbnNlLmNyZWF0ZWRBdCl9XG4gICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgIHtzZWxlY3RlZFRpY2tldC5zdGF0dXMgIT09ICdDTE9TRUQnICYmIChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTNcIj5cbiAgICAgICAgICAgICAgICA8dGV4dGFyZWFcbiAgICAgICAgICAgICAgICAgIHZhbHVlPXtuZXdSZXNwb25zZX1cbiAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0TmV3UmVzcG9uc2UoZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJBZGQgYSByZXNwb25zZS4uLlwiXG4gICAgICAgICAgICAgICAgICByb3dzPXszfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHB4LTMgcHktMiBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbGcgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctc29sYXItNTAwIGZvY3VzOmJvcmRlci10cmFuc3BhcmVudFwiXG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBhZGRSZXNwb25zZShzZWxlY3RlZFRpY2tldC5pZCl9XG4gICAgICAgICAgICAgICAgICBkaXNhYmxlZD17IW5ld1Jlc3BvbnNlLnRyaW0oKX1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbFwiXG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgPFNlbmQgY2xhc3NOYW1lPVwiaC00IHctNCBtci0yXCIgLz5cbiAgICAgICAgICAgICAgICAgIFNlbmQgUmVzcG9uc2VcbiAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L01vZGFsPlxuICAgICAgKX1cblxuICAgICAgey8qIEZBUSBEZXRhaWwgTW9kYWwgKi99XG4gICAgICB7c2VsZWN0ZWRGQVEgJiYgKFxuICAgICAgICA8TW9kYWxcbiAgICAgICAgICBpc09wZW49eyEhc2VsZWN0ZWRGQVF9XG4gICAgICAgICAgb25DbG9zZT17KCkgPT4gc2V0U2VsZWN0ZWRGQVEobnVsbCl9XG4gICAgICAgICAgdGl0bGU9e3NlbGVjdGVkRkFRLnF1ZXN0aW9ufVxuICAgICAgICAgIHNpemU9XCJ4bFwiXG4gICAgICAgID5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMyBwYi00IGJvcmRlci1iIGJvcmRlci1ncmF5LTIwMFwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtMyBiZy1zb2xhci0xMDAgcm91bmRlZC1sZ1wiPlxuICAgICAgICAgICAgICAgIDxzZWxlY3RlZEZBUS5pY29uIGNsYXNzTmFtZT1cImgtNiB3LTYgdGV4dC1zb2xhci02MDBcIiAvPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtc29sYXItNjAwIGJnLXNvbGFyLTUwIHB4LTMgcHktMSByb3VuZGVkLWZ1bGxcIj5cbiAgICAgICAgICAgICAgICAgIHtzZWxlY3RlZEZBUS5jYXRlZ29yeX1cbiAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicHJvc2UgcHJvc2Utc20gbWF4LXctbm9uZVwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIndoaXRlc3BhY2UtcHJlLWxpbmUgdGV4dC1ncmF5LTcwMCBsZWFkaW5nLXJlbGF4ZWRcIj5cbiAgICAgICAgICAgICAgICB7c2VsZWN0ZWRGQVEuYW5zd2VyfVxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInB0LTQgYm9yZGVyLXQgYm9yZGVyLWdyYXktMjAwXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LXdyYXAgZ2FwLTJcIj5cbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgbXItMlwiPlRhZ3M6PC9zcGFuPlxuICAgICAgICAgICAgICAgIHtzZWxlY3RlZEZBUS50YWdzLm1hcCh0YWcgPT4gKFxuICAgICAgICAgICAgICAgICAgPHNwYW4ga2V5PXt0YWd9IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1zb2xhci02MDAgYmctc29sYXItNTAgcHgtMyBweS0xIHJvdW5kZWQtZnVsbFwiPlxuICAgICAgICAgICAgICAgICAgICB7dGFnfVxuICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInB0LTQgYm9yZGVyLXQgYm9yZGVyLWdyYXktMjAwXCI+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMCBtYi0zXCI+XG4gICAgICAgICAgICAgICAgU3RpbGwgbmVlZCBoZWxwPyBDcmVhdGUgYSBzdXBwb3J0IHRpY2tldCBmb3IgcGVyc29uYWxpemVkIGFzc2lzdGFuY2UuXG4gICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHtcbiAgICAgICAgICAgICAgICAgIHNldFNlbGVjdGVkRkFRKG51bGwpO1xuICAgICAgICAgICAgICAgICAgc2V0U2hvd05ld1RpY2tldE1vZGFsKHRydWUpO1xuICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHNtOnctYXV0b1wiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8TWVzc2FnZUNpcmNsZSBjbGFzc05hbWU9XCJoLTQgdy00IG1yLTJcIiAvPlxuICAgICAgICAgICAgICAgIENyZWF0ZSBTdXBwb3J0IFRpY2tldFxuICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L01vZGFsPlxuICAgICAgKX1cbiAgICA8L2Rpdj5cbiAgKTtcbn07XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsIkJ1dHRvbiIsIklucHV0IiwiQ2FyZCIsIkNhcmRIZWFkZXIiLCJDYXJkVGl0bGUiLCJDYXJkQ29udGVudCIsIk1vZGFsIiwiTWVzc2FnZUNpcmNsZSIsIlBsdXMiLCJDbG9jayIsIkNoZWNrQ2lyY2xlIiwiQWxlcnRDaXJjbGUiLCJTZW5kIiwiU2V0dGluZ3MiLCJIZWxwQ2lyY2xlIiwiQm9va09wZW4iLCJVc2VycyIsIlNoaWVsZCIsIlRyZW5kaW5nVXAiLCJXYWxsZXQiLCJmb3JtYXREYXRlVGltZSIsImZhcURhdGEiLCJpZCIsImNhdGVnb3J5IiwicXVlc3Rpb24iLCJhbnN3ZXIiLCJpY29uIiwidGFncyIsIlN1cHBvcnRDZW50ZXIiLCJ0aWNrZXRzIiwic2V0VGlja2V0cyIsImxvYWRpbmciLCJzZXRMb2FkaW5nIiwic2hvd05ld1RpY2tldE1vZGFsIiwic2V0U2hvd05ld1RpY2tldE1vZGFsIiwic2VsZWN0ZWRUaWNrZXQiLCJzZXRTZWxlY3RlZFRpY2tldCIsInNlbGVjdGVkRkFRIiwic2V0U2VsZWN0ZWRGQVEiLCJmYXFTZWFyY2hUZXJtIiwic2V0RmFxU2VhcmNoVGVybSIsInNlbGVjdGVkQ2F0ZWdvcnkiLCJzZXRTZWxlY3RlZENhdGVnb3J5IiwiY3VycmVudFBhZ2UiLCJzZXRDdXJyZW50UGFnZSIsIml0ZW1zUGVyUGFnZSIsIm5ld1RpY2tldEZvcm0iLCJzZXROZXdUaWNrZXRGb3JtIiwic3ViamVjdCIsIm1lc3NhZ2UiLCJwcmlvcml0eSIsIm5ld1Jlc3BvbnNlIiwic2V0TmV3UmVzcG9uc2UiLCJzdWJtaXR0aW5nIiwic2V0U3VibWl0dGluZyIsImZldGNoVGlja2V0cyIsImNhdGVnb3JpZXMiLCJBcnJheSIsImZyb20iLCJTZXQiLCJtYXAiLCJmYXEiLCJmaWx0ZXJlZEZBUXMiLCJmaWx0ZXIiLCJtYXRjaGVzU2VhcmNoIiwidG9Mb3dlckNhc2UiLCJpbmNsdWRlcyIsInNvbWUiLCJ0YWciLCJtYXRjaGVzQ2F0ZWdvcnkiLCJ0b3RhbFBhZ2VzIiwiTWF0aCIsImNlaWwiLCJsZW5ndGgiLCJzdGFydEluZGV4IiwiZW5kSW5kZXgiLCJjdXJyZW50RkFRcyIsInNsaWNlIiwicmVzcG9uc2UiLCJmZXRjaCIsImNyZWRlbnRpYWxzIiwib2siLCJkYXRhIiwianNvbiIsInN1Y2Nlc3MiLCJlcnJvciIsImNvbnNvbGUiLCJjcmVhdGVUaWNrZXQiLCJlIiwicHJldmVudERlZmF1bHQiLCJtZXRob2QiLCJoZWFkZXJzIiwiYm9keSIsIkpTT04iLCJzdHJpbmdpZnkiLCJhZGRSZXNwb25zZSIsInRpY2tldElkIiwidHJpbSIsInVwZGF0ZWRUaWNrZXRzIiwidXBkYXRlZFRpY2tldCIsImZpbmQiLCJ0IiwiZ2V0U3RhdHVzSWNvbiIsInN0YXR1cyIsImNsYXNzTmFtZSIsImdldFN0YXR1c0NvbG9yIiwiZ2V0UHJpb3JpdHlDb2xvciIsImRpdiIsImgyIiwicCIsIm9uQ2xpY2siLCJ0aWNrZXQiLCJoNCIsInNwYW4iLCJjcmVhdGVkQXQiLCJoMyIsInBsYWNlaG9sZGVyIiwidmFsdWUiLCJvbkNoYW5nZSIsInRhcmdldCIsInNlbGVjdCIsIm9wdGlvbiIsIkljb25Db21wb25lbnQiLCJzcGxpdCIsIm1pbiIsImJ1dHRvbiIsInByZXYiLCJtYXgiLCJkaXNhYmxlZCIsIl8iLCJpIiwicGFnZSIsImlzT3BlbiIsIm9uQ2xvc2UiLCJ0aXRsZSIsImZvcm0iLCJvblN1Ym1pdCIsImxhYmVsIiwicmVxdWlyZWQiLCJ0ZXh0YXJlYSIsInJvd3MiLCJ0eXBlIiwidmFyaWFudCIsInNpemUiLCJyZXNwb25zZXMiLCJpc0FkbWluIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/SupportCenter.tsx\n"));

/***/ })

});