"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js":
/*!**********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/copy.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Copy)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.518.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"rect\",\n        {\n            width: \"14\",\n            height: \"14\",\n            x: \"8\",\n            y: \"8\",\n            rx: \"2\",\n            ry: \"2\",\n            key: \"17jyea\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2\",\n            key: \"zix9uf\"\n        }\n    ]\n];\nconst Copy = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"copy\", __iconNode);\n //# sourceMappingURL=copy.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/admin/DepositManagement.tsx":
/*!****************************************************!*\
  !*** ./src/components/admin/DepositManagement.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DepositManagement: () => (/* binding */ DepositManagement)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Check_CheckCircle_Clock_Copy_DollarSign_Eye_Filter_Info_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Check,CheckCircle,Clock,Copy,DollarSign,Eye,Filter,Info,RefreshCw,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Check_CheckCircle_Clock_Copy_DollarSign_Eye_Filter_Info_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Check,CheckCircle,Clock,Copy,DollarSign,Eye,Filter,Info,RefreshCw,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Check_CheckCircle_Clock_Copy_DollarSign_Eye_Filter_Info_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Check,CheckCircle,Clock,Copy,DollarSign,Eye,Filter,Info,RefreshCw,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Check_CheckCircle_Clock_Copy_DollarSign_Eye_Filter_Info_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Check,CheckCircle,Clock,Copy,DollarSign,Eye,Filter,Info,RefreshCw,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Check_CheckCircle_Clock_Copy_DollarSign_Eye_Filter_Info_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Check,CheckCircle,Clock,Copy,DollarSign,Eye,Filter,Info,RefreshCw,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Check_CheckCircle_Clock_Copy_DollarSign_Eye_Filter_Info_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Check,CheckCircle,Clock,Copy,DollarSign,Eye,Filter,Info,RefreshCw,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Check_CheckCircle_Clock_Copy_DollarSign_Eye_Filter_Info_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Check,CheckCircle,Clock,Copy,DollarSign,Eye,Filter,Info,RefreshCw,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/funnel.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Check_CheckCircle_Clock_Copy_DollarSign_Eye_Filter_Info_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Check,CheckCircle,Clock,Copy,DollarSign,Eye,Filter,Info,RefreshCw,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Check_CheckCircle_Clock_Copy_DollarSign_Eye_Filter_Info_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Check,CheckCircle,Clock,Copy,DollarSign,Eye,Filter,Info,RefreshCw,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Check_CheckCircle_Clock_Copy_DollarSign_Eye_Filter_Info_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Check,CheckCircle,Clock,Copy,DollarSign,Eye,Filter,Info,RefreshCw,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Check_CheckCircle_Clock_Copy_DollarSign_Eye_Filter_Info_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Check,CheckCircle,Clock,Copy,DollarSign,Eye,Filter,Info,RefreshCw,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ DepositManagement auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst DepositManagement = ()=>{\n    var _selectedDeposit_user, _selectedDeposit_user1;\n    _s();\n    const [deposits, setDeposits] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalDeposits: 0,\n        totalAmount: 0,\n        pendingDeposits: 0\n    });\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedStatus, setSelectedStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('ALL');\n    const [selectedDeposit, setSelectedDeposit] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [actionLoading, setActionLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [copiedField, setCopiedField] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const fetchDeposits = async ()=>{\n        try {\n            setLoading(true);\n            const params = new URLSearchParams();\n            if (selectedStatus !== 'ALL') {\n                params.append('status', selectedStatus);\n            }\n            params.append('limit', '50');\n            const response = await fetch(\"/api/admin/deposits?\".concat(params), {\n                credentials: 'include'\n            });\n            if (!response.ok) {\n                throw new Error('Failed to fetch deposits');\n            }\n            const data = await response.json();\n            if (data.success) {\n                setDeposits(data.data.deposits);\n                setStats(data.data.stats);\n            } else {\n                throw new Error(data.error || 'Failed to fetch deposits');\n            }\n        } catch (err) {\n            setError(err instanceof Error ? err.message : 'An error occurred');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleCopyToClipboard = async (text, fieldName)=>{\n        try {\n            await navigator.clipboard.writeText(text);\n            setCopiedField(fieldName);\n            setTimeout(()=>setCopiedField(null), 2000);\n        } catch (err) {\n            console.error('Failed to copy to clipboard:', err);\n        }\n    };\n    const handleDepositAction = async (transactionId, action, reason)=>{\n        try {\n            setActionLoading(transactionId);\n            const response = await fetch('/api/admin/deposits', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                credentials: 'include',\n                body: JSON.stringify({\n                    action,\n                    transactionId,\n                    reason\n                })\n            });\n            if (!response.ok) {\n                throw new Error('Failed to process deposit action');\n            }\n            const data = await response.json();\n            if (data.success) {\n                // Refresh deposits list\n                await fetchDeposits();\n                setSelectedDeposit(null);\n            } else {\n                throw new Error(data.error || 'Failed to process deposit action');\n            }\n        } catch (err) {\n            setError(err instanceof Error ? err.message : 'An error occurred');\n        } finally{\n            setActionLoading(null);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DepositManagement.useEffect\": ()=>{\n            fetchDeposits();\n        }\n    }[\"DepositManagement.useEffect\"], [\n        selectedStatus\n    ]);\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case 'COMPLETED':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Check_CheckCircle_Clock_Copy_DollarSign_Eye_Filter_Info_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"w-4 h-4 text-green-400\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                    lineNumber: 128,\n                    columnNumber: 16\n                }, undefined);\n            case 'PENDING':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Check_CheckCircle_Clock_Copy_DollarSign_Eye_Filter_Info_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"w-4 h-4 text-yellow-400\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                    lineNumber: 130,\n                    columnNumber: 16\n                }, undefined);\n            case 'FAILED':\n            case 'REJECTED':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Check_CheckCircle_Clock_Copy_DollarSign_Eye_Filter_Info_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"w-4 h-4 text-red-400\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                    lineNumber: 133,\n                    columnNumber: 16\n                }, undefined);\n            case 'VERIFYING':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Check_CheckCircle_Clock_Copy_DollarSign_Eye_Filter_Info_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"w-4 h-4 text-blue-400\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                    lineNumber: 135,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Check_CheckCircle_Clock_Copy_DollarSign_Eye_Filter_Info_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"w-4 h-4 text-gray-400\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                    lineNumber: 137,\n                    columnNumber: 16\n                }, undefined);\n        }\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case 'COMPLETED':\n                return 'text-green-400 bg-green-400/10';\n            case 'PENDING':\n                return 'text-yellow-400 bg-yellow-400/10';\n            case 'FAILED':\n            case 'REJECTED':\n                return 'text-red-400 bg-red-400/10';\n            case 'VERIFYING':\n                return 'text-blue-400 bg-blue-400/10';\n            default:\n                return 'text-gray-400 bg-gray-400/10';\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Check_CheckCircle_Clock_Copy_DollarSign_Eye_Filter_Info_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                className: \"w-8 h-8 animate-spin text-blue-400\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                lineNumber: 160,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n            lineNumber: 159,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-end\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: fetchDeposits,\n                    className: \"flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Check_CheckCircle_Clock_Copy_DollarSign_Eye_Filter_Info_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            className: \"w-4 h-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                            lineNumber: 173,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"hidden sm:inline\",\n                            children: \"Refresh\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                            lineNumber: 174,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                    lineNumber: 169,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                lineNumber: 168,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"bg-gray-800 border-gray-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-400 text-sm\",\n                                                children: \"Total Deposits\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                lineNumber: 184,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-white\",\n                                                children: stats.totalDeposits\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                lineNumber: 185,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Check_CheckCircle_Clock_Copy_DollarSign_Eye_Filter_Info_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-8 h-8 text-green-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                            lineNumber: 181,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"bg-gray-800 border-gray-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-400 text-sm\",\n                                                children: \"Total Amount\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                lineNumber: 196,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-white\",\n                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.formatCurrency)(stats.totalAmount)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                lineNumber: 197,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Check_CheckCircle_Clock_Copy_DollarSign_Eye_Filter_Info_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-8 h-8 text-blue-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                        lineNumber: 199,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                            lineNumber: 193,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                        lineNumber: 192,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"bg-gray-800 border-gray-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-400 text-sm\",\n                                                children: \"Pending Deposits\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-white\",\n                                                children: stats.pendingDeposits\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                lineNumber: 209,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Check_CheckCircle_Clock_Copy_DollarSign_Eye_Filter_Info_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-8 h-8 text-yellow-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                            lineNumber: 205,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                        lineNumber: 204,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                lineNumber: 179,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"bg-gray-800 border-gray-700\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"p-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row items-start sm:items-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Check_CheckCircle_Clock_Copy_DollarSign_Eye_Filter_Info_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"w-5 h-5 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-400 text-sm font-medium\",\n                                        children: \"Filter by Status:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                        lineNumber: 223,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                value: selectedStatus,\n                                onChange: (e)=>setSelectedStatus(e.target.value),\n                                className: \"w-full sm:w-auto bg-gray-700 border border-gray-600 text-white rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"ALL\",\n                                        children: \"All Status\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                        lineNumber: 230,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"PENDING\",\n                                        children: \"Pending\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                        lineNumber: 231,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"VERIFYING\",\n                                        children: \"Verifying\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                        lineNumber: 232,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"CONFIRMED\",\n                                        children: \"Confirmed\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"COMPLETED\",\n                                        children: \"Completed\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                        lineNumber: 234,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"FAILED\",\n                                        children: \"Failed\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                        lineNumber: 235,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"REJECTED\",\n                                        children: \"Rejected\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                lineNumber: 225,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                        lineNumber: 220,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                    lineNumber: 219,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                lineNumber: 218,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"bg-gray-800 border-gray-700\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"text-white\",\n                            children: \"Recent Deposits\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                            lineNumber: 245,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                        lineNumber: 244,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: [\n                            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4 p-4 bg-red-900/20 border border-red-500 rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-400\",\n                                    children: error\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                    lineNumber: 250,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden lg:block overflow-x-auto\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                    className: \"w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                className: \"border-b border-gray-700\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-left py-3 px-4 text-gray-400 font-medium\",\n                                                        children: \"User\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                        lineNumber: 259,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-left py-3 px-4 text-gray-400 font-medium\",\n                                                        children: \"Amount\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                        lineNumber: 260,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-left py-3 px-4 text-gray-400 font-medium\",\n                                                        children: \"Status\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                        lineNumber: 261,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-left py-3 px-4 text-gray-400 font-medium\",\n                                                        children: \"Transaction ID\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                        lineNumber: 262,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-left py-3 px-4 text-gray-400 font-medium\",\n                                                        children: \"Date\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                        lineNumber: 263,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-left py-3 px-4 text-gray-400 font-medium\",\n                                                        children: \"Actions\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                        lineNumber: 264,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                lineNumber: 258,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                            lineNumber: 257,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                            children: deposits.map((deposit)=>{\n                                                var _deposit_user;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"border-b border-gray-700/50 hover:bg-gray-700/30\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-white\",\n                                                                    children: deposit.user ? \"\".concat(deposit.user.firstName, \" \").concat(deposit.user.lastName) : 'Unknown'\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                                    lineNumber: 271,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm text-gray-400\",\n                                                                    children: ((_deposit_user = deposit.user) === null || _deposit_user === void 0 ? void 0 : _deposit_user.email) || 'No email'\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                                    lineNumber: 274,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                            lineNumber: 270,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-white font-medium\",\n                                                                    children: [\n                                                                        (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.formatCurrency)(deposit.usdtAmount),\n                                                                        \" USDT\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                                    lineNumber: 279,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm text-gray-400\",\n                                                                    children: [\n                                                                        deposit.confirmations,\n                                                                        \" confirmations\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                                    lineNumber: 282,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                            lineNumber: 278,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"inline-flex items-center space-x-2 px-2 py-1 rounded-full text-xs font-medium \".concat(getStatusColor(deposit.status)),\n                                                                children: [\n                                                                    getStatusIcon(deposit.status),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: deposit.status\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                                        lineNumber: 289,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                                lineNumber: 287,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                            lineNumber: 286,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-white font-mono text-sm\",\n                                                                children: [\n                                                                    deposit.transactionId.slice(0, 8),\n                                                                    \"...\",\n                                                                    deposit.transactionId.slice(-8)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                                lineNumber: 293,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                            lineNumber: 292,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4 text-gray-300\",\n                                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.formatDateTime)(deposit.createdAt)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                            lineNumber: 297,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>setSelectedDeposit(deposit),\n                                                                className: \"flex items-center space-x-1 px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white rounded text-sm transition-colors\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Check_CheckCircle_Clock_Copy_DollarSign_Eye_Filter_Info_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        className: \"w-4 h-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                                        lineNumber: 305,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"View\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                                        lineNumber: 306,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                                lineNumber: 301,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                            lineNumber: 300,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, deposit.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                    lineNumber: 269,\n                                                    columnNumber: 19\n                                                }, undefined);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                            lineNumber: 267,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                    lineNumber: 256,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:hidden space-y-4\",\n                                children: deposits.map((deposit)=>{\n                                    var _deposit_user;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-700 rounded-lg p-4 border border-gray-600\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-white font-medium\",\n                                                                children: deposit.user ? \"\".concat(deposit.user.firstName, \" \").concat(deposit.user.lastName) : 'Unknown'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                                lineNumber: 322,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-400\",\n                                                                children: ((_deposit_user = deposit.user) === null || _deposit_user === void 0 ? void 0 : _deposit_user.email) || 'No email'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                                lineNumber: 325,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                        lineNumber: 321,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"inline-flex items-center space-x-2 px-2 py-1 rounded-full text-xs font-medium \".concat(getStatusColor(deposit.status)),\n                                                        children: [\n                                                            getStatusIcon(deposit.status),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: deposit.status\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                                lineNumber: 331,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                        lineNumber: 329,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                lineNumber: 320,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-2 gap-3 text-sm mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-gray-400 text-xs mb-1\",\n                                                                children: \"Amount\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                                lineNumber: 338,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-white font-medium\",\n                                                                children: [\n                                                                    (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.formatCurrency)(deposit.usdtAmount),\n                                                                    \" USDT\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                                lineNumber: 339,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-400\",\n                                                                children: [\n                                                                    deposit.confirmations,\n                                                                    \" confirmations\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                                lineNumber: 342,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                        lineNumber: 337,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-gray-400 text-xs mb-1\",\n                                                                children: \"Date\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                                lineNumber: 347,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-gray-300 text-xs\",\n                                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.formatDateTime)(deposit.createdAt)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                                lineNumber: 348,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                        lineNumber: 346,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                lineNumber: 336,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-gray-400 text-xs mb-1\",\n                                                        children: \"Transaction ID\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                        lineNumber: 356,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-white font-mono text-xs bg-gray-800 p-2 rounded border\",\n                                                        children: [\n                                                            deposit.transactionId.slice(0, 8),\n                                                            \"...\",\n                                                            deposit.transactionId.slice(-8)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                        lineNumber: 357,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                lineNumber: 355,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setSelectedDeposit(deposit),\n                                                className: \"w-full flex items-center justify-center space-x-2 px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded text-sm transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Check_CheckCircle_Clock_Copy_DollarSign_Eye_Filter_Info_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                        lineNumber: 367,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"View Details\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                        lineNumber: 368,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                lineNumber: 363,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, deposit.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                        lineNumber: 318,\n                                        columnNumber: 15\n                                    }, undefined);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                lineNumber: 316,\n                                columnNumber: 11\n                            }, undefined),\n                            deposits.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8 text-gray-400\",\n                                children: \"No deposits found\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                lineNumber: 375,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                        lineNumber: 247,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                lineNumber: 243,\n                columnNumber: 7\n            }, undefined),\n            selectedDeposit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gray-800 border border-gray-700 rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-bold text-white\",\n                                    children: \"Deposit Details\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                    lineNumber: 387,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setSelectedDeposit(null),\n                                    className: \"text-gray-400 hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Check_CheckCircle_Clock_Copy_DollarSign_Eye_Filter_Info_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"w-6 h-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                        lineNumber: 392,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                    lineNumber: 388,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                            lineNumber: 386,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"text-gray-400 text-sm\",\n                                                    children: \"User\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                    lineNumber: 399,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-white\",\n                                                    children: selectedDeposit.user ? \"\".concat(selectedDeposit.user.firstName, \" \").concat(selectedDeposit.user.lastName) : 'Unknown'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                    lineNumber: 400,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-400 text-sm flex-1\",\n                                                            children: (_selectedDeposit_user = selectedDeposit.user) === null || _selectedDeposit_user === void 0 ? void 0 : _selectedDeposit_user.email\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                            lineNumber: 404,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        ((_selectedDeposit_user1 = selectedDeposit.user) === null || _selectedDeposit_user1 === void 0 ? void 0 : _selectedDeposit_user1.email) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>{\n                                                                var _selectedDeposit_user;\n                                                                return handleCopyToClipboard(((_selectedDeposit_user = selectedDeposit.user) === null || _selectedDeposit_user === void 0 ? void 0 : _selectedDeposit_user.email) || '', 'email');\n                                                            },\n                                                            className: \"p-1 text-gray-400 hover:text-white transition-colors\",\n                                                            title: \"Copy email\",\n                                                            children: copiedField === 'email' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Check_CheckCircle_Clock_Copy_DollarSign_Eye_Filter_Info_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"w-4 h-4 text-green-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                                lineNumber: 412,\n                                                                columnNumber: 27\n                                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Check_CheckCircle_Clock_Copy_DollarSign_Eye_Filter_Info_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                                lineNumber: 414,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                            lineNumber: 406,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                    lineNumber: 403,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                            lineNumber: 398,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"text-gray-400 text-sm\",\n                                                    children: \"Amount\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                    lineNumber: 421,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-white font-medium\",\n                                                    children: [\n                                                        (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.formatCurrency)(selectedDeposit.usdtAmount),\n                                                        \" USDT\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                    lineNumber: 422,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                            lineNumber: 420,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                    lineNumber: 397,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"text-gray-400 text-sm\",\n                                                    children: \"Status\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                    lineNumber: 428,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"inline-flex items-center space-x-2 px-2 py-1 rounded-full text-xs font-medium \".concat(getStatusColor(selectedDeposit.status)),\n                                                    children: [\n                                                        getStatusIcon(selectedDeposit.status),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: selectedDeposit.status\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                            lineNumber: 431,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                    lineNumber: 429,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                            lineNumber: 427,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"text-gray-400 text-sm\",\n                                                    children: \"Confirmations\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                    lineNumber: 435,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-white\",\n                                                    children: selectedDeposit.confirmations\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                    lineNumber: 436,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                            lineNumber: 434,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                    lineNumber: 426,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"text-gray-400 text-sm\",\n                                            children: \"Transaction ID\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                            lineNumber: 441,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white font-mono text-sm break-all\",\n                                            children: selectedDeposit.transactionId\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                            lineNumber: 442,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                    lineNumber: 440,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"text-gray-400 text-sm\",\n                                                    children: \"Sender Address\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                    lineNumber: 447,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-white font-mono text-sm break-all\",\n                                                    children: selectedDeposit.senderAddress || 'N/A'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                    lineNumber: 448,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                            lineNumber: 446,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"text-gray-400 text-sm\",\n                                                    children: \"Deposit Address\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                    lineNumber: 453,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-white font-mono text-sm break-all\",\n                                                    children: selectedDeposit.tronAddress\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                    lineNumber: 454,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                            lineNumber: 452,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                    lineNumber: 445,\n                                    columnNumber: 15\n                                }, undefined),\n                                selectedDeposit.failureReason && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"text-gray-400 text-sm\",\n                                            children: \"Failure Reason\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                            lineNumber: 460,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-red-400\",\n                                            children: selectedDeposit.failureReason\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                            lineNumber: 461,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                    lineNumber: 459,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"text-gray-400 text-sm\",\n                                                    children: \"Created At\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                    lineNumber: 467,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-white\",\n                                                    children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.formatDateTime)(selectedDeposit.createdAt)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                    lineNumber: 468,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                            lineNumber: 466,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"text-gray-400 text-sm\",\n                                                    children: \"Processed At\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                    lineNumber: 471,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-white\",\n                                                    children: selectedDeposit.processedAt ? (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.formatDateTime)(selectedDeposit.processedAt) : 'Not processed'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                    lineNumber: 472,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                            lineNumber: 470,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                    lineNumber: 465,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                            lineNumber: 396,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6 pt-6 border-t border-gray-700\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-900/50 border border-blue-700 rounded-lg p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Check_CheckCircle_Clock_Copy_DollarSign_Eye_Filter_Info_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"w-5 h-5 text-blue-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                            lineNumber: 483,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-blue-300 font-medium\",\n                                                    children: \"Automated Processing\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                    lineNumber: 485,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-blue-200 text-sm mt-1\",\n                                                    children: \"Deposits are now processed automatically. The system verifies transactions and credits wallets once confirmations are met.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                                    lineNumber: 486,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                            lineNumber: 484,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                    lineNumber: 482,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                                lineNumber: 481,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                            lineNumber: 480,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                    lineNumber: 385,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n                lineNumber: 384,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\admin\\\\DepositManagement.tsx\",\n        lineNumber: 166,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DepositManagement, \"WvKeujTlw9xddF3J5qUG41AMNmw=\");\n_c = DepositManagement;\nvar _c;\n$RefreshReg$(_c, \"DepositManagement\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/DepositManagement.tsx\n"));

/***/ })

});