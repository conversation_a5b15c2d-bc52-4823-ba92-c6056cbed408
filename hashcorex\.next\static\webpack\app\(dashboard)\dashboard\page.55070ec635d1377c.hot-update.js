"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/SupportCenter.tsx":
/*!****************************************************!*\
  !*** ./src/components/dashboard/SupportCenter.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SupportCenter: () => (/* binding */ SupportCenter)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BookOpen,CheckCircle,Clock,HelpCircle,MessageCircle,Plus,Send,Settings,Shield,TrendingUp,Users,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BookOpen,CheckCircle,Clock,HelpCircle,MessageCircle,Plus,Send,Settings,Shield,TrendingUp,Users,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wallet.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BookOpen,CheckCircle,Clock,HelpCircle,MessageCircle,Plus,Send,Settings,Shield,TrendingUp,Users,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BookOpen,CheckCircle,Clock,HelpCircle,MessageCircle,Plus,Send,Settings,Shield,TrendingUp,Users,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BookOpen,CheckCircle,Clock,HelpCircle,MessageCircle,Plus,Send,Settings,Shield,TrendingUp,Users,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BookOpen,CheckCircle,Clock,HelpCircle,MessageCircle,Plus,Send,Settings,Shield,TrendingUp,Users,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BookOpen,CheckCircle,Clock,HelpCircle,MessageCircle,Plus,Send,Settings,Shield,TrendingUp,Users,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BookOpen,CheckCircle,Clock,HelpCircle,MessageCircle,Plus,Send,Settings,Shield,TrendingUp,Users,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BookOpen,CheckCircle,Clock,HelpCircle,MessageCircle,Plus,Send,Settings,Shield,TrendingUp,Users,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BookOpen,CheckCircle,Clock,HelpCircle,MessageCircle,Plus,Send,Settings,Shield,TrendingUp,Users,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BookOpen,CheckCircle,Clock,HelpCircle,MessageCircle,Plus,Send,Settings,Shield,TrendingUp,Users,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BookOpen,CheckCircle,Clock,HelpCircle,MessageCircle,Plus,Send,Settings,Shield,TrendingUp,Users,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-question-mark.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BookOpen,CheckCircle,Clock,HelpCircle,MessageCircle,Plus,Send,Settings,Shield,TrendingUp,Users,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ SupportCenter auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n// FAQ Data\nconst faqData = [\n    {\n        id: '1',\n        category: 'Getting Started',\n        question: 'How do I start mining with HashCoreX?',\n        answer: \"To start mining with HashCoreX, follow these simple steps:\\n\\n1. Complete KYC Verification: First, complete your KYC verification in the Profile section. This is required for all mining activities.\\n\\n2. Deposit Funds: Go to your Wallet and deposit USDT (TRC20) to fund your account. The minimum deposit amount is configured by the admin.\\n\\n3. Purchase Mining Units: Navigate to the Mining section and purchase mining units. Choose your desired TH/s amount and investment level.\\n\\n4. Start Earning: Your mining units will start generating daily returns automatically. Earnings are calculated based on your TH/s amount and current market conditions.\\n\\n5. Track Progress: Monitor your earnings in the Earnings section and watch your mining units progress toward their 5x investment return limit.\\n\\nRemember: Mining units expire after 24 months or when they reach 5x their investment amount, whichever comes first.\",\n        icon: _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        tags: [\n            'mining',\n            'getting started',\n            'kyc',\n            'deposit'\n        ]\n    },\n    {\n        id: '2',\n        category: 'Mining Units',\n        question: 'How are daily returns calculated?',\n        answer: \"Daily returns are calculated using a dynamic system based on your mining unit's TH/s amount:\\n\\nDynamic ROI System:\\n• Different TH/s ranges have different return rates\\n• Returns are calculated as: (Investment Amount \\xd7 Daily ROI%) \\xf7 100\\n• ROI percentages are determined by admin-configured ranges\\n\\nExample Ranges:\\n• 0-10 TH/s: 0.3% - 0.5% daily\\n• 10-50 TH/s: 0.4% - 0.6% daily\\n• 50+ TH/s: 0.5% - 0.7% daily\\n\\nImportant Notes:\\n• Returns are credited daily but paid out weekly (Sundays at 00:00 AM GMT+5:30)\\n• Mining units expire when they reach 5x their investment amount\\n• FIFO system: Oldest units receive earnings first\\n• All earnings are subject to market conditions and admin configuration\",\n        icon: _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        tags: [\n            'mining',\n            'returns',\n            'calculation',\n            'roi'\n        ]\n    },\n    {\n        id: '3',\n        category: 'Wallet & Payments',\n        question: 'How do deposits and withdrawals work?',\n        answer: \"Deposits:\\n• Only USDT (TRC20) deposits are accepted\\n• Minimum deposit amount is set by admin (typically $50)\\n• Deposits are automatically verified on the Tron blockchain\\n• Funds are available immediately after confirmation\\n\\nWithdrawals:\\n• Minimum withdrawal: $50\\n• Fixed fee: $3 + 1% of withdrawal amount\\n• Processing time: Up to 3 business days\\n• Only to verified TRC20 addresses\\n\\nWallet Balance:\\n• Available Balance: Funds ready for use or withdrawal\\n• Pending Balance: Earnings waiting for weekly distribution\\n• Total Earnings: Cumulative earnings from all sources\\n\\nImportant:\\n• Complete KYC verification before making withdrawals\\n• Ensure your TRC20 address is correct before submitting\\n• Withdrawal fees are deducted from your balance\",\n        icon: _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        tags: [\n            'wallet',\n            'deposit',\n            'withdrawal',\n            'usdt',\n            'trc20'\n        ]\n    },\n    {\n        id: '4',\n        category: 'Referral System',\n        question: 'How does the binary referral system work?',\n        answer: \"HashCoreX uses a binary tree referral system with three placement types:\\n\\nPlacement Types:\\n1. General Referral: Placed in weaker leg automatically\\n2. Left-Side Referral: Specifically placed on left side\\n3. Right-Side Referral: Specifically placed on right side\\n\\nEarning Structure:\\n• Direct Referral Commission: 10% one-time bonus when your referral purchases mining units\\n• Binary Matching Bonus: Weekly matching of binary points (1 point = $10)\\n\\nBinary Points:\\n• Earned when your referrals purchase mining units\\n• $150 purchase = 1.5 points (supports 2 decimal places)\\n• Points are matched weekly between left and right sides\\n• Excess points reset to 0 after matching\\n\\nRequirements:\\n• You must have active mining units to earn commissions\\n• Binary matching occurs weekly at 15:00 UTC\\n• All earnings are allocated to your mining units using FIFO system\",\n        icon: _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        tags: [\n            'referral',\n            'binary',\n            'commission',\n            'points',\n            'matching'\n        ]\n    },\n    {\n        id: '5',\n        category: 'Account & Security',\n        question: 'What is KYC and why is it required?',\n        answer: \"KYC (Know Your Customer) Verification:\\n\\nKYC is a mandatory verification process required for all HashCoreX users to ensure compliance with financial regulations.\\n\\nKYC Process:\\n1. Personal Information: Provide accurate personal details\\n2. ID Document: Upload government-issued ID (passport, driver's license, etc.)\\n3. Selfie Verification: Take a selfie holding your ID document\\n4. Admin Review: Our team reviews your submission (typically 24-48 hours)\\n\\nKYC Status:\\n• Not Submitted: KYC documents not yet uploaded\\n• Pending: Under admin review\\n• Approved: Verification complete - full access granted\\n• Rejected: Resubmission required with correct documents\\n\\nWhy KYC is Required:\\n• Legal compliance with financial regulations\\n• Account security and fraud prevention\\n• Enables withdrawals and full platform access\\n• Protects both users and the platform\\n\\nImportant: Profile name fields become read-only after KYC submission to prevent fraud.\",\n        icon: _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        tags: [\n            'kyc',\n            'verification',\n            'security',\n            'compliance',\n            'identity'\n        ]\n    },\n    {\n        id: '6',\n        category: 'Technical Support',\n        question: 'What should I do if I encounter technical issues?',\n        answer: \"Common Solutions:\\n\\nLogin Issues:\\n• Clear browser cache and cookies\\n• Try incognito/private browsing mode\\n• Ensure you're using the correct email address\\n• Check if your account is active\\n\\nTransaction Issues:\\n• Verify transaction hash on Tron blockchain explorer\\n• Check if you're using the correct network (Mainnet/Testnet)\\n• Ensure sufficient balance for fees\\n• Wait for blockchain confirmations\\n\\nDisplay Issues:\\n• Refresh the page\\n• Try a different browser\\n• Disable browser extensions temporarily\\n• Check your internet connection\\n\\nStill Need Help?\\n1. Create a support ticket with detailed information\\n2. Include screenshots if possible\\n3. Provide transaction hashes for payment issues\\n4. Specify your browser and device type\\n\\nResponse Times:\\n• General inquiries: 24-48 hours\\n• Technical issues: 12-24 hours\\n• Payment issues: Priority handling within 12 hours\",\n        icon: _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        tags: [\n            'technical',\n            'support',\n            'troubleshooting',\n            'login',\n            'transactions'\n        ]\n    },\n    {\n        id: '7',\n        category: 'Mining Units',\n        question: 'What happens when mining units expire?',\n        answer: 'Mining units expire under two conditions:\\n\\nExpiry Conditions:\\n• After 24 months from purchase date\\n• When they reach 5x their investment amount (whichever comes first)\\n\\nFIFO Expiry System:\\n• Oldest mining units expire first\\n• Earnings are allocated to oldest units first\\n• This ensures fair distribution and maximum returns\\n\\nWhat Happens at Expiry:\\n• Unit stops generating daily returns\\n• All accumulated earnings are credited to your wallet\\n• Unit status changes to \"Expired\"\\n• No further earnings from that specific unit\\n\\nImportant Notes:\\n• You can track each unit\\'s progress toward 5x return\\n• Expired units are shown in your mining history\\n• Purchase new units to continue earning\\n• All earnings are subject to weekly payout schedule',\n        icon: _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        tags: [\n            'mining',\n            'expiry',\n            'fifo',\n            'returns',\n            'units'\n        ]\n    },\n    {\n        id: '8',\n        category: 'Wallet & Payments',\n        question: 'How do I set up my TRC20 wallet address?',\n        answer: \"Setting up your TRC20 wallet address for withdrawals:\\n\\nSupported Wallets:\\n• TronLink (Browser extension)\\n• Trust Wallet (Mobile app)\\n• Atomic Wallet (Desktop/Mobile)\\n• Any wallet supporting TRC20 tokens\\n\\nSetup Process:\\n1. Go to Profile Settings → Billing & Payments\\n2. Enter your TRC20 wallet address\\n3. Verify the address is correct (double-check!)\\n4. Save the address for future withdrawals\\n\\nImportant Security Tips:\\n• Always copy-paste addresses, never type manually\\n• Test with a small amount first\\n• Ensure your wallet supports USDT TRC20\\n• Keep your private keys secure and never share them\\n• Use only your own wallet addresses\\n\\nNetwork Information:\\n• Network: Tron (TRX)\\n• Token Standard: TRC20\\n• Supported Token: USDT\\n• Withdrawal fees apply as per platform settings\",\n        icon: _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        tags: [\n            'wallet',\n            'trc20',\n            'setup',\n            'withdrawal',\n            'address'\n        ]\n    },\n    {\n        id: '9',\n        category: 'Referral System',\n        question: 'How can I maximize my referral earnings?',\n        answer: \"Strategies to maximize your referral earnings:\\n\\nBuilding Your Network:\\n• Share your referral links on social media\\n• Explain the benefits of HashCoreX to friends and family\\n• Use different placement types strategically\\n• Focus on quality referrals who will be active\\n\\nPlacement Strategy:\\n• Use general referrals for automatic balancing\\n• Use left/right specific placements for strategic building\\n• Monitor your binary tree balance regularly\\n• Help your referrals understand the system\\n\\nEarning Optimization:\\n• Maintain active mining units to earn commissions\\n• Focus on both direct referrals and binary matching\\n• Track your binary points weekly\\n• Reinvest earnings to purchase more mining units\\n\\nBest Practices:\\n• Provide support to your referrals\\n• Share educational content about mining\\n• Be transparent about risks and rewards\\n• Build long-term relationships, not just quick referrals\\n\\nRemember: You must have active mining units to earn any referral commissions.\",\n        icon: _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        tags: [\n            'referral',\n            'earnings',\n            'strategy',\n            'network',\n            'optimization'\n        ]\n    },\n    {\n        id: '10',\n        category: 'Account & Security',\n        question: 'How do I reset my password?',\n        answer: \"Password reset process:\\n\\nStep-by-Step Process:\\n1. Go to the login page\\n2. Click \\\"Forgot Password\\\" link\\n3. Enter your registered email address\\n4. Check your email for OTP verification code\\n5. Enter the OTP code when prompted\\n6. Create a new strong password\\n7. Confirm your new password\\n8. You'll be automatically redirected to dashboard\\n\\nPassword Requirements:\\n• Minimum 8 characters\\n• Include uppercase and lowercase letters\\n• Include at least one number\\n• Include at least one special character\\n• Avoid common passwords\\n\\nSecurity Tips:\\n• Use a unique password for HashCoreX\\n• Consider using a password manager\\n• Don't share your password with anyone\\n• Change your password regularly\\n• Log out from shared devices\\n\\nTroubleshooting:\\n• Check spam folder for OTP email\\n• Ensure email address is correct\\n• Wait a few minutes for email delivery\\n• Contact support if issues persist\",\n        icon: _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        tags: [\n            'password',\n            'reset',\n            'security',\n            'otp',\n            'login'\n        ]\n    },\n    {\n        id: '11',\n        category: 'Getting Started',\n        question: 'What are the minimum requirements to start?',\n        answer: \"Minimum requirements to start mining with HashCoreX:\\n\\nFinancial Requirements:\\n• Minimum deposit: $50 USDT (TRC20)\\n• Minimum mining unit purchase: As configured by admin\\n• Transaction fees: Network fees for deposits/withdrawals\\n\\nAccount Requirements:\\n• Valid email address for registration\\n• Complete KYC verification (mandatory)\\n• TRC20 wallet for deposits and withdrawals\\n• Active status (maintained by having mining units)\\n\\nTechnical Requirements:\\n• Modern web browser (Chrome, Firefox, Safari, Edge)\\n• Stable internet connection\\n• Email access for verification and notifications\\n• Basic understanding of cryptocurrency transactions\\n\\nGetting Started Checklist:\\n1. ✓ Register with valid email\\n2. ✓ Verify email address\\n3. ✓ Complete KYC verification\\n4. ✓ Set up TRC20 wallet\\n5. ✓ Make first deposit ($50+ USDT)\\n6. ✓ Purchase first mining units\\n7. ✓ Start earning daily returns\\n\\nTime Investment:\\n• Initial setup: 30-60 minutes\\n• Daily monitoring: 5-10 minutes\\n• Weekly review: 15-30 minutes\",\n        icon: _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        tags: [\n            'requirements',\n            'minimum',\n            'getting started',\n            'checklist',\n            'setup'\n        ]\n    },\n    {\n        id: '12',\n        category: 'Mining Units',\n        question: 'How do I track my mining performance?',\n        answer: \"Tracking your mining performance effectively:\\n\\nDashboard Overview:\\n• Total active TH/s amount\\n• Daily earnings summary\\n• Pending vs available balance\\n• Mining units status overview\\n\\nDetailed Tracking:\\n• Individual unit performance\\n• Progress toward 5x return limit\\n• Daily ROI percentages (7-day average)\\n• Earnings allocation per unit\\n\\nKey Metrics to Monitor:\\n• Total investment amount\\n• Total earnings to date\\n• Average daily return percentage\\n• Time remaining until expiry\\n• FIFO allocation progress\\n\\nPerformance Analysis:\\n• Compare actual vs expected returns\\n• Monitor market condition impacts\\n• Track weekly payout schedules\\n• Review binary earnings allocation\\n\\nOptimization Tips:\\n• Reinvest earnings for compound growth\\n• Monitor unit expiry dates\\n• Balance investment across different TH/s ranges\\n• Keep track of referral earnings contribution\\n\\nReports Available:\\n• Transaction history\\n• Earnings breakdown\\n• Mining unit details\\n• Referral performance summary\",\n        icon: _barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        tags: [\n            'tracking',\n            'performance',\n            'monitoring',\n            'analytics',\n            'optimization'\n        ]\n    }\n];\nconst SupportCenter = ()=>{\n    _s();\n    const [tickets, setTickets] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showNewTicketModal, setShowNewTicketModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedTicket, setSelectedTicket] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedFAQ, setSelectedFAQ] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [faqSearchTerm, setFaqSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('All');\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const itemsPerPage = 6;\n    const [newTicketForm, setNewTicketForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        subject: '',\n        message: '',\n        priority: 'MEDIUM'\n    });\n    const [newResponse, setNewResponse] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [submitting, setSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SupportCenter.useEffect\": ()=>{\n            fetchTickets();\n        }\n    }[\"SupportCenter.useEffect\"], []);\n    // Get unique categories for filtering\n    const categories = [\n        'All',\n        ...Array.from(new Set(faqData.map((faq)=>faq.category)))\n    ];\n    // Filter FAQs based on search term and category\n    const filteredFAQs = faqData.filter((faq)=>{\n        const matchesSearch = faq.question.toLowerCase().includes(faqSearchTerm.toLowerCase()) || faq.answer.toLowerCase().includes(faqSearchTerm.toLowerCase()) || faq.tags.some((tag)=>tag.toLowerCase().includes(faqSearchTerm.toLowerCase()));\n        const matchesCategory = selectedCategory === 'All' || faq.category === selectedCategory;\n        return matchesSearch && matchesCategory;\n    });\n    // Calculate pagination\n    const totalPages = Math.ceil(filteredFAQs.length / itemsPerPage);\n    const startIndex = (currentPage - 1) * itemsPerPage;\n    const endIndex = startIndex + itemsPerPage;\n    const currentFAQs = filteredFAQs.slice(startIndex, endIndex);\n    // Reset to first page when filters change\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect({\n        \"SupportCenter.useEffect\": ()=>{\n            setCurrentPage(1);\n        }\n    }[\"SupportCenter.useEffect\"], [\n        faqSearchTerm,\n        selectedCategory\n    ]);\n    const fetchTickets = async ()=>{\n        try {\n            const response = await fetch('/api/support/tickets', {\n                credentials: 'include'\n            });\n            if (response.ok) {\n                const data = await response.json();\n                if (data.success) {\n                    setTickets(data.data);\n                }\n            }\n        } catch (error) {\n            console.error('Failed to fetch tickets:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const createTicket = async (e)=>{\n        e.preventDefault();\n        setSubmitting(true);\n        try {\n            const response = await fetch('/api/support/tickets', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                credentials: 'include',\n                body: JSON.stringify(newTicketForm)\n            });\n            if (response.ok) {\n                setNewTicketForm({\n                    subject: '',\n                    message: '',\n                    priority: 'MEDIUM'\n                });\n                setShowNewTicketModal(false);\n                fetchTickets();\n            }\n        } catch (error) {\n            console.error('Failed to create ticket:', error);\n        } finally{\n            setSubmitting(false);\n        }\n    };\n    const addResponse = async (ticketId)=>{\n        if (!newResponse.trim()) return;\n        try {\n            const response = await fetch(\"/api/support/tickets/\".concat(ticketId, \"/responses\"), {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                credentials: 'include',\n                body: JSON.stringify({\n                    message: newResponse\n                })\n            });\n            if (response.ok) {\n                setNewResponse('');\n                fetchTickets();\n                // Update selected ticket\n                const updatedTickets = await fetch('/api/support/tickets', {\n                    credentials: 'include'\n                });\n                if (updatedTickets.ok) {\n                    const data = await updatedTickets.json();\n                    const updatedTicket = data.data.find((t)=>t.id === ticketId);\n                    if (updatedTicket) {\n                        setSelectedTicket(updatedTicket);\n                    }\n                }\n            }\n        } catch (error) {\n            console.error('Failed to add response:', error);\n        }\n    };\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case 'OPEN':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    className: \"h-4 w-4 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                    lineNumber: 573,\n                    columnNumber: 16\n                }, undefined);\n            case 'IN_PROGRESS':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"h-4 w-4 text-solar-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                    lineNumber: 575,\n                    columnNumber: 16\n                }, undefined);\n            case 'RESOLVED':\n            case 'CLOSED':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    className: \"h-4 w-4 text-eco-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                    lineNumber: 578,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    className: \"h-4 w-4 text-gray-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                    lineNumber: 580,\n                    columnNumber: 16\n                }, undefined);\n        }\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case 'OPEN':\n                return 'bg-red-100 text-red-700';\n            case 'IN_PROGRESS':\n                return 'bg-solar-100 text-solar-700';\n            case 'RESOLVED':\n            case 'CLOSED':\n                return 'bg-eco-100 text-eco-700';\n            default:\n                return 'bg-gray-100 text-gray-700';\n        }\n    };\n    const getPriorityColor = (priority)=>{\n        switch(priority){\n            case 'URGENT':\n                return 'bg-red-100 text-red-700';\n            case 'HIGH':\n                return 'bg-orange-100 text-orange-700';\n            case 'MEDIUM':\n                return 'bg-solar-100 text-solar-700';\n            case 'LOW':\n                return 'bg-gray-100 text-gray-700';\n            default:\n                return 'bg-gray-100 text-gray-700';\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-pulse\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-8 bg-gray-200 rounded w-1/4 mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                        lineNumber: 617,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-64 bg-gray-200 rounded\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                        lineNumber: 618,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                lineNumber: 616,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n            lineNumber: 615,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: \"Support Center\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                lineNumber: 629,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mt-1\",\n                                children: \"Get help and manage support tickets\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                lineNumber: 630,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                        lineNumber: 628,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        onClick: ()=>setShowNewTicketModal(true),\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                lineNumber: 636,\n                                columnNumber: 11\n                            }, undefined),\n                            \"New Ticket\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                        lineNumber: 632,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                lineNumber: 627,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                        lineNumber: 646,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    \"Support Tickets\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                lineNumber: 645,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                            lineNumber: 644,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            children: tickets.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: tickets.map((ticket)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        onClick: ()=>setSelectedTicket(ticket),\n                                        className: \"p-4 border border-gray-200 rounded-lg cursor-pointer\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start justify-between mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-gray-900 truncate flex-1\",\n                                                        children: ticket.subject\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                        lineNumber: 660,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2 ml-2\",\n                                                        children: [\n                                                            getStatusIcon(ticket.status),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs px-2 py-1 rounded-full \".concat(getStatusColor(ticket.status)),\n                                                                children: ticket.status\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                                lineNumber: 665,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                        lineNumber: 663,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                lineNumber: 659,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600 mb-2 line-clamp-2\",\n                                                children: ticket.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                lineNumber: 670,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between text-xs text-gray-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-2 py-1 rounded-full \".concat(getPriorityColor(ticket.priority)),\n                                                        children: ticket.priority\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                        lineNumber: 674,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.formatDateTime)(ticket.createdAt)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                        lineNumber: 677,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                lineNumber: 673,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, ticket.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                        lineNumber: 654,\n                                        columnNumber: 21\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                lineNumber: 652,\n                                columnNumber: 17\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                        lineNumber: 684,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                                        children: \"No Support Tickets\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                        lineNumber: 685,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"You haven't created any support tickets yet.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                        lineNumber: 686,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                lineNumber: 683,\n                                columnNumber: 17\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                            lineNumber: 650,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                    lineNumber: 643,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                lineNumber: 642,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                        lineNumber: 698,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    \"Help Tutorials & FAQ\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                lineNumber: 697,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                            lineNumber: 696,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6 space-y-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col sm:flex-row gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                                    placeholder: \"Search FAQs...\",\n                                                    value: faqSearchTerm,\n                                                    onChange: (e)=>setFaqSearchTerm(e.target.value),\n                                                    className: \"w-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                    lineNumber: 707,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                lineNumber: 706,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"sm:w-48\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: selectedCategory,\n                                                    onChange: (e)=>setSelectedCategory(e.target.value),\n                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-solar-500 focus:border-transparent\",\n                                                    children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: category,\n                                                            children: category\n                                                        }, category, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                            lineNumber: 721,\n                                                            columnNumber: 23\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                    lineNumber: 715,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                lineNumber: 714,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                        lineNumber: 705,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                    lineNumber: 704,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6\",\n                                    children: currentFAQs.map((faq)=>{\n                                        const IconComponent = faq.icon;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            onClick: ()=>setSelectedFAQ(faq),\n                                            className: \"p-6 border border-gray-200 rounded-lg cursor-pointer hover:border-solar-300 hover:shadow-md transition-all duration-200 bg-white\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start gap-3 mb-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-2 bg-solar-100 rounded-lg\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                                className: \"h-5 w-5 text-solar-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                                lineNumber: 740,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                            lineNumber: 739,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs font-medium text-solar-600 bg-solar-50 px-2 py-1 rounded-full\",\n                                                                children: faq.category\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                                lineNumber: 743,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                            lineNumber: 742,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                    lineNumber: 738,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-semibold text-gray-900 mb-2 line-clamp-2\",\n                                                    children: faq.question\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                    lineNumber: 748,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600 line-clamp-3 mb-3\",\n                                                    children: faq.answer.split('\\n')[0]\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                    lineNumber: 751,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-wrap gap-1\",\n                                                    children: [\n                                                        faq.tags.slice(0, 3).map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded\",\n                                                                children: tag\n                                                            }, tag, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                                lineNumber: 756,\n                                                                columnNumber: 25\n                                                            }, undefined)),\n                                                        faq.tags.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-gray-500\",\n                                                            children: [\n                                                                \"+\",\n                                                                faq.tags.length - 3,\n                                                                \" more\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                            lineNumber: 761,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                    lineNumber: 754,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, faq.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                            lineNumber: 733,\n                                            columnNumber: 19\n                                        }, undefined);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                    lineNumber: 729,\n                                    columnNumber: 13\n                                }, undefined),\n                                filteredFAQs.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-12\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                            lineNumber: 771,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-gray-900 mb-2\",\n                                            children: \"No FAQs Found\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                            lineNumber: 772,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: faqSearchTerm || selectedCategory !== 'All' ? 'Try adjusting your search or filter criteria.' : 'FAQ content is being updated.'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                            lineNumber: 773,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                    lineNumber: 770,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                            lineNumber: 702,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                    lineNumber: 695,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                lineNumber: 694,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Modal, {\n                isOpen: showNewTicketModal,\n                onClose: ()=>setShowNewTicketModal(false),\n                title: \"Create Support Ticket\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: createTicket,\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                            label: \"Subject\",\n                            value: newTicketForm.subject,\n                            onChange: (e)=>setNewTicketForm((prev)=>({\n                                        ...prev,\n                                        subject: e.target.value\n                                    })),\n                            placeholder: \"Brief description of your issue\",\n                            required: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                            lineNumber: 791,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"Priority\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                    lineNumber: 800,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: newTicketForm.priority,\n                                    onChange: (e)=>setNewTicketForm((prev)=>({\n                                                ...prev,\n                                                priority: e.target.value\n                                            })),\n                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-solar-500 focus:border-transparent\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"LOW\",\n                                            children: \"Low\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                            lineNumber: 808,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"MEDIUM\",\n                                            children: \"Medium\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                            lineNumber: 809,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"HIGH\",\n                                            children: \"High\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                            lineNumber: 810,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"URGENT\",\n                                            children: \"Urgent\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                            lineNumber: 811,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                    lineNumber: 803,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                            lineNumber: 799,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"Message\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                    lineNumber: 816,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                    value: newTicketForm.message,\n                                    onChange: (e)=>setNewTicketForm((prev)=>({\n                                                ...prev,\n                                                message: e.target.value\n                                            })),\n                                    placeholder: \"Describe your issue in detail...\",\n                                    rows: 4,\n                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-solar-500 focus:border-transparent\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                    lineNumber: 819,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                            lineNumber: 815,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    type: \"button\",\n                                    variant: \"outline\",\n                                    onClick: ()=>setShowNewTicketModal(false),\n                                    className: \"flex-1\",\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                    lineNumber: 830,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    type: \"submit\",\n                                    loading: submitting,\n                                    className: \"flex-1\",\n                                    children: \"Create Ticket\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                    lineNumber: 838,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                            lineNumber: 829,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                    lineNumber: 790,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                lineNumber: 785,\n                columnNumber: 7\n            }, undefined),\n            selectedTicket && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Modal, {\n                isOpen: !!selectedTicket,\n                onClose: ()=>setSelectedTicket(null),\n                title: \"Ticket: \".concat(selectedTicket.subject),\n                size: \"xl\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                getStatusIcon(selectedTicket.status),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm px-2 py-1 rounded-full \".concat(getStatusColor(selectedTicket.status)),\n                                    children: selectedTicket.status\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                    lineNumber: 860,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm px-2 py-1 rounded-full \".concat(getPriorityColor(selectedTicket.priority)),\n                                    children: selectedTicket.priority\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                    lineNumber: 863,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                            lineNumber: 858,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-50 p-4 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600 mb-2\",\n                                    children: \"Original Message:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                    lineNumber: 869,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-900\",\n                                    children: selectedTicket.message\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                    lineNumber: 870,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-500 mt-2\",\n                                    children: [\n                                        \"Created: \",\n                                        (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.formatDateTime)(selectedTicket.createdAt)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                    lineNumber: 871,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                            lineNumber: 868,\n                            columnNumber: 13\n                        }, undefined),\n                        selectedTicket.responses.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"font-medium text-gray-900\",\n                                    children: \"Responses:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                    lineNumber: 878,\n                                    columnNumber: 17\n                                }, undefined),\n                                selectedTicket.responses.map((response)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 rounded-lg \".concat(response.isAdmin ? 'bg-blue-50 border-l-4 border-blue-500' : 'bg-gray-50'),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-900\",\n                                                children: response.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                lineNumber: 886,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-500 mt-1\",\n                                                children: [\n                                                    response.isAdmin ? 'Support Team' : 'You',\n                                                    \" • \",\n                                                    (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.formatDateTime)(response.createdAt)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                                lineNumber: 887,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, response.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                        lineNumber: 880,\n                                        columnNumber: 19\n                                    }, undefined))\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                            lineNumber: 877,\n                            columnNumber: 15\n                        }, undefined),\n                        selectedTicket.status !== 'CLOSED' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                    value: newResponse,\n                                    onChange: (e)=>setNewResponse(e.target.value),\n                                    placeholder: \"Add a response...\",\n                                    rows: 3,\n                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-solar-500 focus:border-transparent\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                    lineNumber: 897,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: ()=>addResponse(selectedTicket.id),\n                                    disabled: !newResponse.trim(),\n                                    className: \"w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                            lineNumber: 909,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        \"Send Response\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                    lineNumber: 904,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                            lineNumber: 896,\n                            columnNumber: 15\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                    lineNumber: 857,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                lineNumber: 851,\n                columnNumber: 9\n            }, undefined),\n            selectedFAQ && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Modal, {\n                isOpen: !!selectedFAQ,\n                onClose: ()=>setSelectedFAQ(null),\n                title: selectedFAQ.question,\n                size: \"xl\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-3 pb-4 border-b border-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 bg-solar-100 rounded-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(selectedFAQ.icon, {\n                                        className: \"h-6 w-6 text-solar-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                        lineNumber: 929,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                    lineNumber: 928,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium text-solar-600 bg-solar-50 px-3 py-1 rounded-full\",\n                                        children: selectedFAQ.category\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                        lineNumber: 932,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                    lineNumber: 931,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                            lineNumber: 927,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"prose prose-sm max-w-none\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"whitespace-pre-line text-gray-700 leading-relaxed\",\n                                children: selectedFAQ.answer\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                lineNumber: 939,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                            lineNumber: 938,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"pt-4 border-t border-gray-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium text-gray-700 mr-2\",\n                                        children: \"Tags:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                        lineNumber: 946,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    selectedFAQ.tags.map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-solar-600 bg-solar-50 px-3 py-1 rounded-full\",\n                                            children: tag\n                                        }, tag, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                            lineNumber: 948,\n                                            columnNumber: 19\n                                        }, undefined))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                lineNumber: 945,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                            lineNumber: 944,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"pt-4 border-t border-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600 mb-3\",\n                                    children: \"Still need help? Create a support ticket for personalized assistance.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                    lineNumber: 956,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: ()=>{\n                                        setSelectedFAQ(null);\n                                        setShowNewTicketModal(true);\n                                    },\n                                    className: \"w-full sm:w-auto\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BookOpen_CheckCircle_Clock_HelpCircle_MessageCircle_Plus_Send_Settings_Shield_TrendingUp_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                            lineNumber: 966,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        \"Create Support Ticket\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                                    lineNumber: 959,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                            lineNumber: 955,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                    lineNumber: 926,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n                lineNumber: 920,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\SupportCenter.tsx\",\n        lineNumber: 625,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SupportCenter, \"Ce9h9yPbRnJtX2zoEXEUNfGKYag=\");\n_c = SupportCenter;\nvar _c;\n$RefreshReg$(_c, \"SupportCenter\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/SupportCenter.tsx\n"));

/***/ })

});