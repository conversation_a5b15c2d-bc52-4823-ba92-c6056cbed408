"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_src_lib_emailNotificationService_ts";
exports.ids = ["_rsc_src_lib_emailNotificationService_ts"];
exports.modules = {

/***/ "(rsc)/./src/lib/email.ts":
/*!**************************!*\
  !*** ./src/lib/email.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   emailService: () => (/* binding */ emailService),\n/* harmony export */   generateOTP: () => (/* binding */ generateOTP),\n/* harmony export */   isValidEmail: () => (/* binding */ isValidEmail)\n/* harmony export */ });\n/* harmony import */ var nodemailer__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! nodemailer */ \"(rsc)/./node_modules/nodemailer/lib/nodemailer.js\");\n/* harmony import */ var _database__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./database */ \"(rsc)/./src/lib/database.ts\");\n\n\nclass EmailService {\n    async getEmailConfig() {\n        try {\n            const settings = await _database__WEBPACK_IMPORTED_MODULE_1__.systemSettingsDb.getEmailSettings();\n            if (!settings || !settings.smtpHost || !settings.smtpUser || !settings.smtpPassword) {\n                console.warn('Email configuration not found or incomplete');\n                return null;\n            }\n            return {\n                host: settings.smtpHost,\n                port: settings.smtpPort || 587,\n                secure: settings.smtpSecure || false,\n                user: settings.smtpUser,\n                password: settings.smtpPassword,\n                fromName: settings.fromName || 'HashCoreX',\n                fromEmail: settings.fromEmail || settings.smtpUser\n            };\n        } catch (error) {\n            console.error('Failed to get email configuration:', error);\n            return null;\n        }\n    }\n    async initializeTransporter() {\n        try {\n            this.config = await this.getEmailConfig();\n            if (!this.config) {\n                return false;\n            }\n            this.transporter = nodemailer__WEBPACK_IMPORTED_MODULE_0__.createTransport({\n                host: this.config.host,\n                port: this.config.port,\n                secure: this.config.secure,\n                auth: {\n                    user: this.config.user,\n                    pass: this.config.password\n                },\n                tls: {\n                    rejectUnauthorized: false\n                }\n            });\n            // Verify connection\n            await this.transporter.verify();\n            console.log('Email transporter initialized successfully');\n            return true;\n        } catch (error) {\n            console.error('Failed to initialize email transporter:', error);\n            this.transporter = null;\n            return false;\n        }\n    }\n    async sendEmail(emailData) {\n        try {\n            if (!this.transporter || !this.config) {\n                const initialized = await this.initializeTransporter();\n                if (!initialized) {\n                    throw new Error('Email service not configured');\n                }\n            }\n            const mailOptions = {\n                from: `\"${this.config.fromName}\" <${this.config.fromEmail}>`,\n                to: emailData.to,\n                subject: emailData.subject,\n                html: emailData.html,\n                text: emailData.text\n            };\n            const result = await this.transporter.sendMail(mailOptions);\n            console.log('Email sent successfully:', result.messageId);\n            return true;\n        } catch (error) {\n            console.error('Failed to send email:', error);\n            return false;\n        }\n    }\n    async sendOTPEmail(email, otp, firstName, purpose = 'email_verification') {\n        const templateName = purpose === 'password_reset' ? 'password_reset_otp' : 'otp_verification';\n        const template = await this.getEmailTemplate(templateName);\n        if (!template) {\n            console.error(`Email template '${templateName}' not found. Please ensure email templates are seeded.`);\n            return false;\n        }\n        // Use custom template\n        let html = template.htmlContent;\n        let text = template.textContent || '';\n        // Replace placeholders\n        html = html.replace(/{{firstName}}/g, firstName || 'User');\n        html = html.replace(/{{otp}}/g, otp);\n        text = text.replace(/{{firstName}}/g, firstName || 'User');\n        text = text.replace(/{{otp}}/g, otp);\n        return await this.sendEmail({\n            to: email,\n            subject: template.subject,\n            html,\n            text\n        });\n    }\n    async getEmailTemplate(templateName) {\n        try {\n            const template = await _database__WEBPACK_IMPORTED_MODULE_1__.systemSettingsDb.getEmailTemplate(templateName);\n            return template;\n        } catch (error) {\n            console.error('Failed to get email template:', error);\n            return null;\n        }\n    }\n    async testConnection() {\n        try {\n            // Get fresh config\n            this.config = await this.getEmailConfig();\n            if (!this.config) {\n                console.error('Email configuration not found or incomplete');\n                return false;\n            }\n            // Create transporter\n            this.transporter = nodemailer__WEBPACK_IMPORTED_MODULE_0__.createTransport({\n                host: this.config.host,\n                port: this.config.port,\n                secure: this.config.secure,\n                auth: {\n                    user: this.config.user,\n                    pass: this.config.password\n                },\n                tls: {\n                    rejectUnauthorized: false\n                }\n            });\n            // Test connection\n            await this.transporter.verify();\n            console.log('Email connection test successful');\n            return true;\n        } catch (error) {\n            console.error('Email connection test failed:', error);\n            this.transporter = null;\n            throw error; // Re-throw to get specific error message\n        }\n    }\n    constructor(){\n        this.transporter = null;\n        this.config = null;\n    }\n}\n// Export singleton instance\nconst emailService = new EmailService();\n// Utility functions\nconst generateOTP = ()=>{\n    return Math.floor(100000 + Math.random() * 900000).toString();\n};\nconst isValidEmail = (email)=>{\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/email.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/emailNotificationService.ts":
/*!*********************************************!*\
  !*** ./src/lib/emailNotificationService.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   emailNotificationService: () => (/* binding */ emailNotificationService)\n/* harmony export */ });\n/* harmony import */ var _email__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./email */ \"(rsc)/./src/lib/email.ts\");\n/* harmony import */ var _database__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./database */ \"(rsc)/./src/lib/database.ts\");\n\n\nclass EmailNotificationService {\n    /**\n   * Send deposit success notification\n   */ async sendDepositSuccessNotification(data) {\n        try {\n            const template = await _email__WEBPACK_IMPORTED_MODULE_0__.emailService.getEmailTemplate('deposit_success');\n            if (!template) {\n                console.warn('Email template \"deposit_success\" not found. Using default template.');\n            }\n            const subject = template?.subject || 'Deposit Confirmed - HashCoreX';\n            let html = template?.htmlContent || this.getDefaultDepositSuccessTemplate();\n            let text = template?.textContent || '';\n            // Replace variables\n            html = this.replaceVariables(html, {\n                firstName: data.firstName,\n                lastName: data.lastName,\n                email: data.email,\n                amount: data.amount.toString(),\n                transactionId: data.transactionId,\n                currency: data.currency\n            });\n            text = this.replaceVariables(text, {\n                firstName: data.firstName,\n                lastName: data.lastName,\n                email: data.email,\n                amount: data.amount.toString(),\n                transactionId: data.transactionId,\n                currency: data.currency\n            });\n            // Create email log entry\n            const emailLog = await _database__WEBPACK_IMPORTED_MODULE_1__.emailLogDb.create({\n                to: data.email,\n                subject,\n                template: 'deposit_success',\n                status: 'PENDING'\n            });\n            const emailSent = await _email__WEBPACK_IMPORTED_MODULE_0__.emailService.sendEmail({\n                to: data.email,\n                subject,\n                html,\n                text: text || `Deposit Confirmed\\n\\nAmount: ${data.amount} ${data.currency}\\nTransaction ID: ${data.transactionId}`\n            });\n            if (emailSent) {\n                await _database__WEBPACK_IMPORTED_MODULE_1__.emailLogDb.updateStatus(emailLog.id, 'SENT');\n                return true;\n            } else {\n                await _database__WEBPACK_IMPORTED_MODULE_1__.emailLogDb.updateStatus(emailLog.id, 'FAILED', 'Email service error');\n                return false;\n            }\n        } catch (error) {\n            console.error('Error sending deposit success notification:', error);\n            return false;\n        }\n    }\n    /**\n   * Send KYC status notification\n   */ async sendKYCStatusNotification(data) {\n        try {\n            const templateName = data.status === 'APPROVED' ? 'kyc_approved' : 'kyc_rejected';\n            const template = await _email__WEBPACK_IMPORTED_MODULE_0__.emailService.getEmailTemplate(templateName);\n            if (!template) {\n                console.warn(`Email template \"${templateName}\" not found. Using default template.`);\n            }\n            const subject = template?.subject || `KYC ${data.status === 'APPROVED' ? 'Approved' : 'Rejected'} - HashCoreX`;\n            let html = template?.htmlContent || this.getDefaultKYCTemplate(data.status);\n            let text = template?.textContent || '';\n            // Replace variables\n            html = this.replaceVariables(html, {\n                firstName: data.firstName,\n                lastName: data.lastName,\n                email: data.email,\n                status: data.status,\n                rejectionReason: data.rejectionReason || ''\n            });\n            text = this.replaceVariables(text, {\n                firstName: data.firstName,\n                lastName: data.lastName,\n                email: data.email,\n                status: data.status,\n                rejectionReason: data.rejectionReason || ''\n            });\n            // Create email log entry\n            const emailLog = await _database__WEBPACK_IMPORTED_MODULE_1__.emailLogDb.create({\n                to: data.email,\n                subject,\n                template: templateName,\n                status: 'PENDING'\n            });\n            const emailSent = await _email__WEBPACK_IMPORTED_MODULE_0__.emailService.sendEmail({\n                to: data.email,\n                subject,\n                html,\n                text: text || `KYC ${data.status}\\n\\n${data.rejectionReason ? `Reason: ${data.rejectionReason}` : 'Your KYC has been approved.'}`\n            });\n            if (emailSent) {\n                await _database__WEBPACK_IMPORTED_MODULE_1__.emailLogDb.updateStatus(emailLog.id, 'SENT');\n                return true;\n            } else {\n                await _database__WEBPACK_IMPORTED_MODULE_1__.emailLogDb.updateStatus(emailLog.id, 'FAILED', 'Email service error');\n                return false;\n            }\n        } catch (error) {\n            console.error('Error sending KYC status notification:', error);\n            return false;\n        }\n    }\n    /**\n   * Send withdrawal status notification\n   */ async sendWithdrawalStatusNotification(data) {\n        try {\n            const templateName = this.getWithdrawalTemplateName(data.status);\n            const template = await _email__WEBPACK_IMPORTED_MODULE_0__.emailService.getEmailTemplate(templateName);\n            if (!template) {\n                console.warn(`Email template \"${templateName}\" not found. Using default template.`);\n            }\n            const subject = template?.subject || `Withdrawal ${this.getWithdrawalStatusText(data.status)} - HashCoreX`;\n            let html = template?.htmlContent || this.getDefaultWithdrawalTemplate(data.status);\n            let text = template?.textContent || '';\n            // Replace variables\n            html = this.replaceVariables(html, {\n                firstName: data.firstName,\n                lastName: data.lastName,\n                email: data.email,\n                amount: data.amount.toString(),\n                status: data.status,\n                transactionHash: data.transactionHash || '',\n                rejectionReason: data.rejectionReason || '',\n                usdtAddress: data.usdtAddress || ''\n            });\n            text = this.replaceVariables(text, {\n                firstName: data.firstName,\n                lastName: data.lastName,\n                email: data.email,\n                amount: data.amount.toString(),\n                status: data.status,\n                transactionHash: data.transactionHash || '',\n                rejectionReason: data.rejectionReason || '',\n                usdtAddress: data.usdtAddress || ''\n            });\n            // Create email log entry\n            const emailLog = await _database__WEBPACK_IMPORTED_MODULE_1__.emailLogDb.create({\n                to: data.email,\n                subject,\n                template: templateName,\n                status: 'PENDING'\n            });\n            const emailSent = await _email__WEBPACK_IMPORTED_MODULE_0__.emailService.sendEmail({\n                to: data.email,\n                subject,\n                html,\n                text: text || this.getDefaultWithdrawalText(data)\n            });\n            if (emailSent) {\n                await _database__WEBPACK_IMPORTED_MODULE_1__.emailLogDb.updateStatus(emailLog.id, 'SENT');\n                return true;\n            } else {\n                await _database__WEBPACK_IMPORTED_MODULE_1__.emailLogDb.updateStatus(emailLog.id, 'FAILED', 'Email service error');\n                return false;\n            }\n        } catch (error) {\n            console.error('Error sending withdrawal status notification:', error);\n            return false;\n        }\n    }\n    /**\n   * Replace template variables\n   */ replaceVariables(template, variables) {\n        let result = template;\n        for (const [key, value] of Object.entries(variables)){\n            const regex = new RegExp(`{{${key}}}`, 'g');\n            result = result.replace(regex, value);\n        }\n        return result;\n    }\n    /**\n   * Get withdrawal template name based on status\n   */ getWithdrawalTemplateName(status) {\n        switch(status){\n            case 'APPROVED':\n                return 'withdrawal_approved';\n            case 'REJECTED':\n                return 'withdrawal_rejected';\n            case 'COMPLETED':\n                return 'withdrawal_completed';\n            case 'FAILED':\n                return 'withdrawal_failed';\n            default:\n                return 'withdrawal_status';\n        }\n    }\n    /**\n   * Get withdrawal status text\n   */ getWithdrawalStatusText(status) {\n        switch(status){\n            case 'APPROVED':\n                return 'Approved';\n            case 'REJECTED':\n                return 'Rejected';\n            case 'COMPLETED':\n                return 'Completed';\n            case 'FAILED':\n                return 'Failed';\n            default:\n                return 'Updated';\n        }\n    }\n    /**\n   * Default deposit success template\n   */ getDefaultDepositSuccessTemplate() {\n        return `\n      <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n        <div style=\"background: linear-gradient(135deg, #ffd60a 0%, #10b981 100%); padding: 20px; text-align: center;\">\n          <h1 style=\"color: white; margin: 0;\">HashCoreX</h1>\n        </div>\n        <div style=\"padding: 30px; background: #f9f9f9;\">\n          <h2 style=\"color: #333;\">Deposit Confirmed!</h2>\n          <p>Hello {{firstName}},</p>\n          <p>Great news! Your deposit has been successfully confirmed and credited to your account.</p>\n          <div style=\"background: white; padding: 20px; margin: 20px 0; border-radius: 8px; border-left: 4px solid #10b981;\">\n            <h3 style=\"margin: 0 0 10px 0; color: #10b981;\">Deposit Details</h3>\n            <p><strong>Amount:</strong> {{amount}} {{currency}}</p>\n            <p><strong>Transaction ID:</strong> {{transactionId}}</p>\n            <p><strong>Status:</strong> Confirmed</p>\n          </div>\n          <p>Your funds are now available in your wallet and you can start using them immediately.</p>\n          <p>Best regards,<br>The HashCoreX Team</p>\n        </div>\n        <div style=\"background: #333; color: white; padding: 20px; text-align: center; font-size: 12px;\">\n          <p>&copy; 2024 HashCoreX. All rights reserved.</p>\n        </div>\n      </div>\n    `;\n    }\n    /**\n   * Default KYC template\n   */ getDefaultKYCTemplate(status) {\n        const isApproved = status === 'APPROVED';\n        const color = isApproved ? '#10b981' : '#ef4444';\n        const title = isApproved ? 'KYC Approved!' : 'KYC Rejected';\n        const message = isApproved ? 'Congratulations! Your KYC verification has been approved. You now have full access to all platform features.' : 'Unfortunately, your KYC verification has been rejected. Please review the reason below and resubmit with correct documents.';\n        return `\n      <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n        <div style=\"background: linear-gradient(135deg, #ffd60a 0%, ${color} 100%); padding: 20px; text-align: center;\">\n          <h1 style=\"color: white; margin: 0;\">HashCoreX</h1>\n        </div>\n        <div style=\"padding: 30px; background: #f9f9f9;\">\n          <h2 style=\"color: #333;\">${title}</h2>\n          <p>Hello {{firstName}},</p>\n          <p>${message}</p>\n          ${!isApproved ? '<div style=\"background: white; padding: 20px; margin: 20px 0; border-radius: 8px; border-left: 4px solid #ef4444;\"><p><strong>Rejection Reason:</strong> {{rejectionReason}}</p></div>' : ''}\n          <p>Best regards,<br>The HashCoreX Team</p>\n        </div>\n        <div style=\"background: #333; color: white; padding: 20px; text-align: center; font-size: 12px;\">\n          <p>&copy; 2024 HashCoreX. All rights reserved.</p>\n        </div>\n      </div>\n    `;\n    }\n    /**\n   * Default withdrawal template\n   */ getDefaultWithdrawalTemplate(status) {\n        const getStatusColor = (status)=>{\n            switch(status){\n                case 'APPROVED':\n                    return '#10b981';\n                case 'COMPLETED':\n                    return '#10b981';\n                case 'REJECTED':\n                    return '#ef4444';\n                case 'FAILED':\n                    return '#ef4444';\n                default:\n                    return '#6b7280';\n            }\n        };\n        return `\n      <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n        <div style=\"background: linear-gradient(135deg, #ffd60a 0%, ${getStatusColor(status)} 100%); padding: 20px; text-align: center;\">\n          <h1 style=\"color: white; margin: 0;\">HashCoreX</h1>\n        </div>\n        <div style=\"padding: 30px; background: #f9f9f9;\">\n          <h2 style=\"color: #333;\">Withdrawal {{status}}</h2>\n          <p>Hello {{firstName}},</p>\n          <p>Your withdrawal request has been {{status}}.</p>\n          <div style=\"background: white; padding: 20px; margin: 20px 0; border-radius: 8px; border-left: 4px solid ${getStatusColor(status)};\">\n            <h3 style=\"margin: 0 0 10px 0; color: ${getStatusColor(status)};\">Withdrawal Details</h3>\n            <p><strong>Amount:</strong> {{amount}} USDT</p>\n            <p><strong>Status:</strong> {{status}}</p>\n            {{#if usdtAddress}}<p><strong>Address:</strong> {{usdtAddress}}</p>{{/if}}\n            {{#if transactionHash}}<p><strong>Transaction Hash:</strong> {{transactionHash}}</p>{{/if}}\n            {{#if rejectionReason}}<p><strong>Reason:</strong> {{rejectionReason}}</p>{{/if}}\n          </div>\n          <p>Best regards,<br>The HashCoreX Team</p>\n        </div>\n        <div style=\"background: #333; color: white; padding: 20px; text-align: center; font-size: 12px;\">\n          <p>&copy; 2024 HashCoreX. All rights reserved.</p>\n        </div>\n      </div>\n    `;\n    }\n    /**\n   * Default withdrawal text\n   */ getDefaultWithdrawalText(data) {\n        let text = `Withdrawal ${data.status}\\n\\nAmount: ${data.amount} USDT\\nStatus: ${data.status}`;\n        if (data.usdtAddress) text += `\\nAddress: ${data.usdtAddress}`;\n        if (data.transactionHash) text += `\\nTransaction Hash: ${data.transactionHash}`;\n        if (data.rejectionReason) text += `\\nReason: ${data.rejectionReason}`;\n        return text;\n    }\n    /**\n   * Send mining unit purchase notification\n   */ async sendMiningUnitPurchaseNotification(data) {\n        try {\n            const template = await _email__WEBPACK_IMPORTED_MODULE_0__.emailService.getEmailTemplate('mining_unit_purchase');\n            if (!template) {\n                console.warn('Email template \"mining_unit_purchase\" not found. Using default template.');\n            }\n            const subject = template?.subject || 'Mining Unit Purchase Confirmed - HashCoreX';\n            let html = template?.htmlContent || this.getDefaultMiningUnitPurchaseTemplate();\n            let text = template?.textContent || this.getDefaultMiningUnitPurchaseText(data);\n            // Replace variables\n            html = this.replaceVariables(html, {\n                firstName: data.firstName,\n                lastName: data.lastName,\n                email: data.email,\n                thsAmount: data.thsAmount.toString(),\n                investmentAmount: data.investmentAmount.toString(),\n                dailyROI: data.dailyROI.toFixed(2),\n                purchaseDate: new Date(data.purchaseDate).toLocaleDateString(),\n                expiryDate: new Date(data.expiryDate).toLocaleDateString()\n            });\n            text = this.replaceVariables(text, {\n                firstName: data.firstName,\n                lastName: data.lastName,\n                email: data.email,\n                thsAmount: data.thsAmount.toString(),\n                investmentAmount: data.investmentAmount.toString(),\n                dailyROI: data.dailyROI.toFixed(2),\n                purchaseDate: new Date(data.purchaseDate).toLocaleDateString(),\n                expiryDate: new Date(data.expiryDate).toLocaleDateString()\n            });\n            // Create email log entry\n            const emailLog = await _database__WEBPACK_IMPORTED_MODULE_1__.emailLogDb.create({\n                to: data.email,\n                subject,\n                template: 'mining_unit_purchase',\n                status: 'PENDING'\n            });\n            // Send email\n            const sent = await _email__WEBPACK_IMPORTED_MODULE_0__.emailService.sendEmail({\n                to: data.email,\n                subject,\n                html,\n                text\n            });\n            // Update email log status\n            await _database__WEBPACK_IMPORTED_MODULE_1__.emailLogDb.update(emailLog.id, {\n                status: sent ? 'SENT' : 'FAILED',\n                sentAt: sent ? new Date().toISOString() : undefined\n            });\n            return sent;\n        } catch (error) {\n            console.error('Failed to send mining unit purchase notification:', error);\n            return false;\n        }\n    }\n    /**\n   * Send mining unit expiry notification\n   */ async sendMiningUnitExpiryNotification(data) {\n        try {\n            const template = await _email__WEBPACK_IMPORTED_MODULE_0__.emailService.getEmailTemplate('mining_unit_expiry');\n            if (!template) {\n                console.warn('Email template \"mining_unit_expiry\" not found. Using default template.');\n            }\n            const subject = template?.subject || 'Mining Unit Expired - HashCoreX';\n            let html = template?.htmlContent || this.getDefaultMiningUnitExpiryTemplate();\n            let text = template?.textContent || this.getDefaultMiningUnitExpiryText(data);\n            // Replace variables\n            html = this.replaceVariables(html, {\n                firstName: data.firstName,\n                lastName: data.lastName,\n                email: data.email,\n                thsAmount: data.thsAmount.toString(),\n                investmentAmount: data.investmentAmount.toString(),\n                totalEarned: data.totalEarned.toString(),\n                purchaseDate: new Date(data.purchaseDate).toLocaleDateString(),\n                expiryDate: new Date(data.expiryDate).toLocaleDateString(),\n                expiryReason: data.expiryReason === 'TIME_LIMIT' ? '24-month time limit reached' : '5x return limit achieved'\n            });\n            text = this.replaceVariables(text, {\n                firstName: data.firstName,\n                lastName: data.lastName,\n                email: data.email,\n                thsAmount: data.thsAmount.toString(),\n                investmentAmount: data.investmentAmount.toString(),\n                totalEarned: data.totalEarned.toString(),\n                purchaseDate: new Date(data.purchaseDate).toLocaleDateString(),\n                expiryDate: new Date(data.expiryDate).toLocaleDateString(),\n                expiryReason: data.expiryReason === 'TIME_LIMIT' ? '24-month time limit reached' : '5x return limit achieved'\n            });\n            // Create email log entry\n            const emailLog = await _database__WEBPACK_IMPORTED_MODULE_1__.emailLogDb.create({\n                to: data.email,\n                subject,\n                template: 'mining_unit_expiry',\n                status: 'PENDING'\n            });\n            // Send email\n            const sent = await _email__WEBPACK_IMPORTED_MODULE_0__.emailService.sendEmail({\n                to: data.email,\n                subject,\n                html,\n                text\n            });\n            // Update email log status\n            await _database__WEBPACK_IMPORTED_MODULE_1__.emailLogDb.update(emailLog.id, {\n                status: sent ? 'SENT' : 'FAILED',\n                sentAt: sent ? new Date().toISOString() : undefined\n            });\n            return sent;\n        } catch (error) {\n            console.error('Failed to send mining unit expiry notification:', error);\n            return false;\n        }\n    }\n    /**\n   * Default mining unit purchase template\n   */ getDefaultMiningUnitPurchaseTemplate() {\n        return `\n      <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n        <div style=\"background: linear-gradient(135deg, #ffd60a 0%, #10b981 100%); padding: 20px; text-align: center;\">\n          <h1 style=\"color: white; margin: 0;\">HashCoreX</h1>\n        </div>\n        <div style=\"padding: 30px; background: #f9f9f9;\">\n          <h2 style=\"color: #333;\">Mining Unit Purchase Confirmed!</h2>\n          <p>Hello {{firstName}},</p>\n          <p>Congratulations! Your mining unit purchase has been successfully processed and is now active.</p>\n          <div style=\"background: white; padding: 20px; margin: 20px 0; border-radius: 8px; border-left: 4px solid #10b981;\">\n            <h3 style=\"margin: 0 0 10px 0; color: #10b981;\">Mining Unit Details</h3>\n            <p><strong>TH/s Amount:</strong> {{thsAmount}} TH/s</p>\n            <p><strong>Investment Amount:</strong> ${{\n            investmentAmount\n        }} USDT</p>\n            <p><strong>Daily ROI:</strong> {{dailyROI}}%</p>\n            <p><strong>Purchase Date:</strong> {{purchaseDate}}</p>\n            <p><strong>Expiry Date:</strong> {{expiryDate}}</p>\n          </div>\n          <div style=\"background: #e3f2fd; padding: 15px; margin: 20px 0; border-radius: 8px;\">\n            <h4 style=\"margin: 0 0 10px 0; color: #1976d2;\">Important Information</h4>\n            <ul style=\"margin: 0; padding-left: 20px;\">\n              <li>Your mining unit will start generating daily returns immediately</li>\n              <li>Earnings are paid out weekly on Sundays at 00:00 AM GMT+5:30</li>\n              <li>Mining units expire after 24 months or when they reach 5x return, whichever comes first</li>\n              <li>You can track your earnings in the Mining section of your dashboard</li>\n            </ul>\n          </div>\n          <p>Thank you for choosing HashCoreX for your mining investment!</p>\n          <p>Best regards,<br>The HashCoreX Team</p>\n        </div>\n        <div style=\"background: #333; color: white; padding: 20px; text-align: center; font-size: 12px;\">\n          <p>&copy; 2024 HashCoreX. All rights reserved.</p>\n        </div>\n      </div>\n    `;\n    }\n    /**\n   * Default mining unit expiry template\n   */ getDefaultMiningUnitExpiryTemplate() {\n        return `\n      <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n        <div style=\"background: linear-gradient(135deg, #ffd60a 0%, #ff9800 100%); padding: 20px; text-align: center;\">\n          <h1 style=\"color: white; margin: 0;\">HashCoreX</h1>\n        </div>\n        <div style=\"padding: 30px; background: #f9f9f9;\">\n          <h2 style=\"color: #333;\">Mining Unit Expired</h2>\n          <p>Hello {{firstName}},</p>\n          <p>We're writing to inform you that one of your mining units has reached its expiry condition.</p>\n          <div style=\"background: white; padding: 20px; margin: 20px 0; border-radius: 8px; border-left: 4px solid #ff9800;\">\n            <h3 style=\"margin: 0 0 10px 0; color: #ff9800;\">Expired Mining Unit Details</h3>\n            <p><strong>TH/s Amount:</strong> {{thsAmount}} TH/s</p>\n            <p><strong>Original Investment:</strong> ${{\n            investmentAmount\n        }} USDT</p>\n            <p><strong>Total Earned:</strong> ${{\n            totalEarned\n        }} USDT</p>\n            <p><strong>Purchase Date:</strong> {{purchaseDate}}</p>\n            <p><strong>Expiry Date:</strong> {{expiryDate}}</p>\n            <p><strong>Expiry Reason:</strong> {{expiryReason}}</p>\n          </div>\n          <div style=\"background: #e8f5e8; padding: 15px; margin: 20px 0; border-radius: 8px;\">\n            <h4 style=\"margin: 0 0 10px 0; color: #2e7d32;\">What Happens Next?</h4>\n            <ul style=\"margin: 0; padding-left: 20px;\">\n              <li>All accumulated earnings have been credited to your wallet</li>\n              <li>This mining unit will no longer generate daily returns</li>\n              <li>You can view the complete history in your Mining section</li>\n              <li>Consider purchasing new mining units to continue earning</li>\n            </ul>\n          </div>\n          <p>Thank you for your continued trust in HashCoreX!</p>\n          <p>Best regards,<br>The HashCoreX Team</p>\n        </div>\n        <div style=\"background: #333; color: white; padding: 20px; text-align: center; font-size: 12px;\">\n          <p>&copy; 2024 HashCoreX. All rights reserved.</p>\n        </div>\n      </div>\n    `;\n    }\n    /**\n   * Default mining unit purchase text\n   */ getDefaultMiningUnitPurchaseText(data) {\n        return `Mining Unit Purchase Confirmed!\n\nHello ${data.firstName},\n\nYour mining unit purchase has been successfully processed:\n\nTH/s Amount: ${data.thsAmount} TH/s\nInvestment Amount: $${data.investmentAmount} USDT\nDaily ROI: ${data.dailyROI.toFixed(2)}%\nPurchase Date: ${new Date(data.purchaseDate).toLocaleDateString()}\nExpiry Date: ${new Date(data.expiryDate).toLocaleDateString()}\n\nYour mining unit will start generating daily returns immediately.\n\nBest regards,\nThe HashCoreX Team`;\n    }\n    /**\n   * Default mining unit expiry text\n   */ getDefaultMiningUnitExpiryText(data) {\n        return `Mining Unit Expired\n\nHello ${data.firstName},\n\nOne of your mining units has expired:\n\nTH/s Amount: ${data.thsAmount} TH/s\nOriginal Investment: $${data.investmentAmount} USDT\nTotal Earned: $${data.totalEarned} USDT\nPurchase Date: ${new Date(data.purchaseDate).toLocaleDateString()}\nExpiry Date: ${new Date(data.expiryDate).toLocaleDateString()}\nExpiry Reason: ${data.expiryReason === 'TIME_LIMIT' ? '24-month time limit reached' : '5x return limit achieved'}\n\nAll earnings have been credited to your wallet.\n\nBest regards,\nThe HashCoreX Team`;\n    }\n    /**\n   * Send welcome email notification\n   */ async sendWelcomeEmailNotification(data) {\n        try {\n            const template = await _email__WEBPACK_IMPORTED_MODULE_0__.emailService.getEmailTemplate('welcome_email');\n            if (!template) {\n                console.warn('Email template \"welcome_email\" not found. Using default template.');\n            }\n            const subject = template?.subject || 'Welcome to HashCoreX - Your Mining Journey Begins!';\n            let html = template?.htmlContent || this.getDefaultWelcomeTemplate();\n            let text = template?.textContent || this.getDefaultWelcomeText(data);\n            // Replace variables\n            html = this.replaceVariables(html, {\n                firstName: data.firstName,\n                lastName: data.lastName,\n                email: data.email\n            });\n            text = this.replaceVariables(text, {\n                firstName: data.firstName,\n                lastName: data.lastName,\n                email: data.email\n            });\n            // Create email log entry\n            const emailLog = await _database__WEBPACK_IMPORTED_MODULE_1__.emailLogDb.create({\n                to: data.email,\n                subject,\n                template: 'welcome_email',\n                status: 'PENDING'\n            });\n            // Send email\n            const sent = await _email__WEBPACK_IMPORTED_MODULE_0__.emailService.sendEmail({\n                to: data.email,\n                subject,\n                html,\n                text\n            });\n            // Update email log status\n            await _database__WEBPACK_IMPORTED_MODULE_1__.emailLogDb.update(emailLog.id, {\n                status: sent ? 'SENT' : 'FAILED',\n                sentAt: sent ? new Date().toISOString() : undefined\n            });\n            return sent;\n        } catch (error) {\n            console.error('Failed to send welcome email notification:', error);\n            return false;\n        }\n    }\n    /**\n   * Default welcome email template\n   */ getDefaultWelcomeTemplate() {\n        return `\n      <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n        <div style=\"background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;\">\n          <h1 style=\"color: white; margin: 0; font-size: 28px;\">Welcome to HashCoreX!</h1>\n          <p style=\"color: #f0f0f0; margin: 10px 0 0 0;\">Your Sustainable Mining Journey Starts Here</p>\n        </div>\n\n        <div style=\"background: white; padding: 40px; border-radius: 0 0 10px 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);\">\n          <h2 style=\"color: #667eea; margin: 0 0 20px 0; font-size: 24px;\">🎉 Welcome {{firstName}}!</h2>\n\n          <p style=\"margin: 0 0 20px 0; font-size: 16px;\">Thank you for joining HashCoreX, the leading sustainable cryptocurrency mining platform.</p>\n\n          <div style=\"background: #f8f9fa; padding: 25px; margin: 25px 0; border-radius: 8px; border-left: 4px solid #667eea;\">\n            <h3 style=\"margin: 0 0 15px 0; color: #667eea; font-size: 18px;\">🚀 What's Next?</h3>\n            <ul style=\"margin: 0; padding-left: 20px; color: #555;\">\n              <li style=\"margin-bottom: 8px;\">Complete your KYC verification to unlock all features</li>\n              <li style=\"margin-bottom: 8px;\">Explore our mining packages and start earning</li>\n              <li style=\"margin-bottom: 8px;\">Invite friends and earn referral bonuses</li>\n              <li style=\"margin-bottom: 8px;\">Track your earnings in real-time on your dashboard</li>\n            </ul>\n          </div>\n\n          <div style=\"background: #e3f2fd; padding: 20px; margin: 25px 0; border-radius: 8px; border-left: 4px solid #2196f3;\">\n            <h4 style=\"margin: 0 0 15px 0; color: #1976d2; font-size: 16px;\">💡 Getting Started Tips</h4>\n            <ul style=\"margin: 0; padding-left: 20px; color: #555;\">\n              <li style=\"margin-bottom: 8px;\">Start with a small mining unit to understand the platform</li>\n              <li style=\"margin-bottom: 8px;\">Check your dashboard daily for earnings updates</li>\n              <li style=\"margin-bottom: 8px;\">Join our community for tips and updates</li>\n              <li style=\"margin-bottom: 8px;\">Contact support if you have any questions</li>\n            </ul>\n          </div>\n\n          <div style=\"text-align: center; margin: 30px 0;\">\n            <a href=\"https://hashcorex.com/dashboard\" style=\"background: #667eea; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block; margin-right: 10px;\">Go to Dashboard</a>\n            <a href=\"https://hashcorex.com/contact\" style=\"background: #10b981; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;\">Contact Support</a>\n          </div>\n\n          <p style=\"margin: 25px 0 0 0; font-size: 16px;\">We're excited to have you on board and look forward to helping you achieve your mining goals!</p>\n\n          <div style=\"margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; text-align: center; color: #666; font-size: 14px;\">\n            <p>Best regards,<br><strong>The HashCoreX Team</strong></p>\n          </div>\n        </div>\n      </div>\n    `;\n    }\n    /**\n   * Default welcome email text\n   */ getDefaultWelcomeText(data) {\n        return `Welcome to HashCoreX!\n\nHello ${data.firstName},\n\nThank you for joining HashCoreX, the leading sustainable cryptocurrency mining platform.\n\nWhat's Next?\n- Complete your KYC verification to unlock all features\n- Explore our mining packages and start earning\n- Invite friends and earn referral bonuses\n- Track your earnings in real-time on your dashboard\n\nGetting Started Tips:\n- Start with a small mining unit to understand the platform\n- Check your dashboard daily for earnings updates\n- Join our community for tips and updates\n- Contact support if you have any questions\n\nWe're excited to have you on board and look forward to helping you achieve your mining goals!\n\nBest regards,\nThe HashCoreX Team`;\n    }\n}\nconst emailNotificationService = new EmailNotificationService();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/emailNotificationService.ts\n");

/***/ })

};
;